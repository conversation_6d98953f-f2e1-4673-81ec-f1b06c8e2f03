"""
Multimodal Enhancement Service

This service orchestrates multimodal AI features by combining:
- LLa<PERSON> (Large Language and Vision Assistant) for image analysis
- Nomic Embed Text for semantic embeddings and similarity matching
- LLM-based prompt enhancement for creative text generation

Key Features:
- Image-to-prompt enhancement using visual reference analysis
- Iterative refinement based on generated results and user feedback
- Visual similarity search through generation history
- Style and mood matching using multimodal understanding
- Quality assessment and improvement suggestions

Architecture:
This service acts as the coordinator between different AI models and services,
handling the complex workflows that combine visual understanding with semantic
text processing to create enhanced creative workflows.

Dependencies:
- llava_service: For image analysis and visual understanding
- embedding_service: For semantic text embeddings and similarity search
- prompt_enhancement_service: For LLM-based text enhancement
- SQLite database: For storing enhancement results and learning from usage

Usage:
    service = MultimodalEnhancementService()
    result = await service.image_to_prompt_enhancement(
        user_prompt="a beautiful landscape",
        reference_image_path="path/to/reference.jpg",
        enhancement_mode="visual_matching"
    )
"""

from typing import Dict, List, Optional, Any, Tuple
import json
import logging
from pathlib import Path
from datetime import datetime
import asyncio

from .llava_service import LLaVAService, llava_service
from .embedding_service import EmbeddingService
from .prompt_enhancement_service import PromptEnhancementService

logger = logging.getLogger(__name__)

class MultimodalAnalysisCache:
    """
    Cache for storing analysis results to avoid re-analyzing the same images
    """
    
    def __init__(self, max_size: int = 100):
        self.cache = {}
        self.max_size = max_size
        self.access_order = []
    
    def get(self, image_path: str, analysis_type: str) -> Optional[Dict[str, Any]]:
        """Get cached analysis result"""
        key = f"{image_path}:{analysis_type}"
        if key in self.cache:
            # Move to end (most recently accessed)
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def set(self, image_path: str, analysis_type: str, result: Dict[str, Any]):
        """Store analysis result in cache"""
        key = f"{image_path}:{analysis_type}"
        
        # Remove oldest entries if cache is full
        while len(self.cache) >= self.max_size:
            oldest_key = self.access_order.pop(0)
            del self.cache[oldest_key]
        
        self.cache[key] = result
        self.access_order.append(key)
    
    def clear(self):
        """Clear the cache"""
        self.cache.clear()
        self.access_order.clear()

class MultimodalEnhancementService:
    """
    Core service that orchestrates multimodal AI features
    
    Combines LLaVA vision analysis with Nomic embeddings and LLM enhancement
    to provide sophisticated creative AI workflows.
    """
    
    def __init__(self):
        self.llava = llava_service
        self.embedding_service = EmbeddingService()
        self.prompt_enhancement = PromptEnhancementService()
        self.analysis_cache = MultimodalAnalysisCache()
        
        # Configuration for different enhancement modes
        self.enhancement_configs = {
            'visual_matching': {
                'description': 'Match visual characteristics from reference image',
                'llava_analysis_type': 'comprehensive',
                'embedding_weight': 0.7,
                'visual_weight': 0.3,
                'enhancement_focus': 'visual_fidelity',
                'similarity_threshold': 0.6,
                'max_similar_prompts': 5
            },
            'style_transfer': {
                'description': 'Transfer artistic style from reference image',
                'llava_analysis_type': 'style_focused',
                'embedding_weight': 0.5,
                'visual_weight': 0.5,
                'enhancement_focus': 'artistic_style',
                'similarity_threshold': 0.5,
                'max_similar_prompts': 7
            },
            'mood_matching': {
                'description': 'Match emotional tone and atmosphere',
                'llava_analysis_type': 'mood_focused',
                'embedding_weight': 0.6,
                'visual_weight': 0.4,
                'enhancement_focus': 'emotional_tone',
                'similarity_threshold': 0.55,
                'max_similar_prompts': 6
            },
            'composition_guide': {
                'description': 'Learn compositional techniques from reference',
                'llava_analysis_type': 'composition_focused',
                'embedding_weight': 0.4,
                'visual_weight': 0.6,
                'enhancement_focus': 'composition_structure',
                'similarity_threshold': 0.5,
                'max_similar_prompts': 4
            }
        }
    
    async def analyze_reference_image(
        self, 
        image_path: str,
        analysis_focus: str = 'comprehensive',
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Comprehensive analysis of reference image with caching
        
        Args:
            image_path: Path to the reference image
            analysis_focus: Type of analysis to perform
            use_cache: Whether to use cached results if available
            
        Returns:
            Dict containing structured analysis results
        """
        logger.info(f"Starting reference image analysis: {Path(image_path).name} ({analysis_focus})")
        
        # Check cache first
        if use_cache:
            cached_result = self.analysis_cache.get(image_path, analysis_focus)
            if cached_result:
                logger.info("Using cached analysis result")
                return cached_result
        
        try:
            start_time = datetime.now()
            
            # Perform LLaVA analysis
            async with self.llava as llava_session:
                analysis_result = await llava_session.analyze_image(
                    image_path, 
                    analysis_type=analysis_focus
                )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Structure the analysis results
            structured_analysis = {
                'file_path': image_path,
                'file_name': Path(image_path).name,
                'analysis_type': analysis_focus,
                'processing_time': processing_time,
                'visual_elements': {
                    'style': analysis_result.style_analysis,
                    'mood': analysis_result.mood_analysis,
                    'composition': analysis_result.composition,
                    'lighting': analysis_result.lighting,
                    'color_palette': analysis_result.color_palette,
                    'objects': analysis_result.objects,
                    'technical_quality': analysis_result.technical_quality,
                    'keywords': analysis_result.keywords
                },
                'summary': analysis_result.summary,
                'confidence_score': self._calculate_analysis_confidence(analysis_result),
                'raw_analysis': analysis_result.raw_response,
                'timestamp': analysis_result.timestamp
            }
            
            # Cache the result
            if use_cache:
                self.analysis_cache.set(image_path, analysis_focus, structured_analysis)
            
            logger.info(f"Reference image analysis completed in {processing_time:.2f}s")
            return structured_analysis
            
        except Exception as e:
            logger.error(f"Reference image analysis failed: {str(e)}")
            raise Exception(f"Failed to analyze reference image: {str(e)}")
    
    async def image_to_prompt_enhancement(
        self,
        user_prompt: str,
        reference_image_path: str,
        enhancement_mode: str = 'visual_matching',
        additional_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate enhanced prompt based on reference image analysis
        
        Args:
            user_prompt: User's original prompt
            reference_image_path: Path to reference image
            enhancement_mode: Type of enhancement to apply
            additional_context: Optional additional context for enhancement
            
        Returns:
            Dict containing enhanced prompt and analysis data
        """
        logger.info(f"Starting image-to-prompt enhancement: {enhancement_mode}")
        
        try:
            start_time = datetime.now()
            
            # Validate enhancement mode
            if enhancement_mode not in self.enhancement_configs:
                logger.warning(f"Unknown enhancement mode: {enhancement_mode}, using visual_matching")
                enhancement_mode = 'visual_matching'
            
            config = self.enhancement_configs[enhancement_mode]
            
            # Step 1: Analyze reference image
            logger.info("Step 1: Analyzing reference image")
            visual_analysis = await self.analyze_reference_image(
                reference_image_path,
                config['llava_analysis_type']
            )
            
            # Step 2: Convert visual analysis to text for embedding
            logger.info("Step 2: Processing visual analysis for semantic matching")
            visual_description = self._format_visual_analysis_for_embedding(
                visual_analysis['visual_elements'],
                enhancement_mode
            )
            
            # Step 3: Find semantically similar prompts
            logger.info("Step 3: Finding semantically similar prompts")
            try:
                visual_embedding = await self.embedding_service.get_embedding(visual_description)
                similar_prompts = await self.embedding_service.find_similar_prompts(
                    visual_embedding,
                    limit=config['max_similar_prompts'],
                    threshold=config['similarity_threshold']
                )
            except Exception as e:
                logger.warning(f"Semantic search failed, continuing without: {str(e)}")
                similar_prompts = []
            
            # Step 4: Create comprehensive enhancement context
            logger.info("Step 4: Creating enhancement context")
            enhancement_context = {
                'visual_analysis': visual_analysis,
                'similar_prompts': similar_prompts,
                'enhancement_mode': enhancement_mode,
                'config': config,
                'reference_image_path': reference_image_path,
                'original_prompt': user_prompt,
                'visual_description': visual_description,
                'additional_context': additional_context or {}
            }
            
            # Step 5: Generate enhanced prompt using LLM
            logger.info("Step 5: Generating enhanced prompt")
            enhanced_result = await self.prompt_enhancement.enhance_with_multimodal_context(
                user_prompt,
                enhancement_context
            )
            
            # Step 6: Store the enhancement for future learning
            logger.info("Step 6: Storing enhancement result for learning")
            await self._store_enhancement_result(
                user_prompt,
                enhanced_result.get('enhanced_prompt', user_prompt),
                visual_analysis,
                enhancement_mode
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Compile comprehensive result
            result = {
                'success': True,
                'original_prompt': user_prompt,
                'enhanced_prompt': enhanced_result.get('enhanced_prompt', user_prompt),
                'enhancement_explanation': enhanced_result.get('explanation', 'Enhanced using visual analysis'),
                'enhancement_mode': enhancement_mode,
                'enhancement_description': config['description'],
                'visual_analysis': visual_analysis,
                'similar_prompts': similar_prompts,
                'confidence_score': enhanced_result.get('confidence', 0.8),
                'processing_time': processing_time,
                'improvement_suggestions': self._generate_improvement_suggestions(
                    visual_analysis, similar_prompts
                ),
                'metadata': {
                    'reference_image': Path(reference_image_path).name,
                    'analysis_type': config['llava_analysis_type'],
                    'semantic_matches_found': len(similar_prompts),
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"Image-to-prompt enhancement completed successfully in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Image-to-prompt enhancement failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'original_prompt': user_prompt,
                'enhanced_prompt': user_prompt,  # Fallback to original
                'enhancement_mode': enhancement_mode,
                'timestamp': datetime.now().isoformat()
            }
    
    async def iterative_refinement(
        self,
        current_image_path: str,
        original_prompt: str,
        user_feedback: str,
        target_image_path: Optional[str] = None,
        refinement_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Refine prompt based on current generation results and user feedback
        
        Args:
            current_image_path: Path to currently generated image
            original_prompt: The original prompt used
            user_feedback: User's feedback (e.g., "make it more dramatic")
            target_image_path: Optional target reference image
            refinement_context: Optional additional context
            
        Returns:
            Dict containing refinement suggestions and analysis
        """
        logger.info("Starting iterative refinement process")
        
        try:
            start_time = datetime.now()
            
            # Step 1: Analyze current generation result
            logger.info("Step 1: Analyzing current generation result")
            current_analysis = await self.analyze_reference_image(
                current_image_path,
                'comprehensive'
            )
            
            # Step 2: Analyze target image if provided
            target_analysis = None
            if target_image_path:
                logger.info("Step 2: Analyzing target reference image")
                target_analysis = await self.analyze_reference_image(
                    target_image_path,
                    'comprehensive'
                )
            
            # Step 3: Process user feedback semantically
            logger.info("Step 3: Processing user feedback")
            feedback_context = await self._interpret_user_feedback(user_feedback)
            
            # Step 4: Generate refinement suggestions
            logger.info("Step 4: Generating refinement suggestions")
            refinement_data = {
                'current_analysis': current_analysis,
                'target_analysis': target_analysis,
                'original_prompt': original_prompt,
                'user_feedback': user_feedback,
                'feedback_context': feedback_context,
                'refinement_type': 'iterative',
                'additional_context': refinement_context or {}
            }
            
            refinement_result = await self._generate_refinement_suggestions(refinement_data)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Compile result
            result = {
                'success': True,
                'current_analysis': current_analysis,
                'target_analysis': target_analysis,
                'feedback_interpretation': feedback_context,
                'refinement_suggestions': refinement_result['suggestions'],
                'recommended_prompt_modifications': refinement_result['modifications'],
                'confidence_score': refinement_result['confidence'],
                'processing_time': processing_time,
                'metadata': {
                    'original_prompt': original_prompt,
                    'user_feedback': user_feedback,
                    'has_target_image': target_image_path is not None,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"Iterative refinement completed successfully in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Iterative refinement failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'user_feedback': user_feedback,
                'original_prompt': original_prompt,
                'timestamp': datetime.now().isoformat()
            }
    
    async def visual_similarity_search(
        self,
        query_image_path: str,
        search_scope: str = 'both',  # 'history', 'database', 'both'
        similarity_threshold: float = 0.7,
        limit: int = 10,
        search_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Find visually similar images and their associated prompts
        
        Args:
            query_image_path: Path to query image
            search_scope: Where to search ('history', 'database', 'both')
            similarity_threshold: Minimum similarity score
            limit: Maximum number of results
            search_filters: Optional filters for search
            
        Returns:
            Dict containing similar images and prompts
        """
        logger.info(f"Starting visual similarity search: {search_scope}")
        
        try:
            start_time = datetime.now()
            
            # Step 1: Analyze query image
            logger.info("Step 1: Analyzing query image")
            query_analysis = await self.analyze_reference_image(
                query_image_path,
                'comprehensive'
            )
            
            # Step 2: Convert visual analysis to searchable text
            logger.info("Step 2: Converting analysis to search terms")
            search_text = self._format_visual_analysis_for_search(
                query_analysis['visual_elements']
            )
            
            # Step 3: Get embedding for visual description
            logger.info("Step 3: Generating search embedding")
            try:
                search_embedding = await self.embedding_service.get_embedding(search_text)
                
                # Search for similar prompts/images
                similar_items = await self.embedding_service.find_similar_prompts(
                    search_embedding,
                    limit=limit * 2,  # Get more results for filtering
                    threshold=similarity_threshold
                )
            except Exception as e:
                logger.warning(f"Embedding search failed: {str(e)}")
                similar_items = []
            
            # Step 4: Enhance results with visual similarity context
            logger.info("Step 4: Enhancing similarity results")
            enhanced_results = await self._enhance_similarity_results(
                similar_items,
                query_analysis,
                limit
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                'success': True,
                'query_image': query_image_path,
                'query_analysis': query_analysis,
                'similar_items': enhanced_results,
                'search_scope': search_scope,
                'total_found': len(enhanced_results),
                'similarity_threshold': similarity_threshold,
                'search_terms': search_text,
                'processing_time': processing_time,
                'metadata': {
                    'query_image_name': Path(query_image_path).name,
                    'search_filters_applied': search_filters is not None,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"Visual similarity search found {len(enhanced_results)} results in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Visual similarity search failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query_image': query_image_path,
                'similar_items': [],
                'timestamp': datetime.now().isoformat()
            }
    
    def _format_visual_analysis_for_embedding(
        self, 
        visual_elements: Dict[str, Any], 
        enhancement_mode: str
    ) -> str:
        """Convert visual analysis to text suitable for embedding based on enhancement mode"""
        
        description_parts = []
        
        # Prioritize elements based on enhancement mode
        if enhancement_mode == 'style_transfer':
            # Focus on style elements
            if visual_elements.get('style'):
                style_info = visual_elements['style']
                description_parts.append(f"Artistic style: {style_info.get('primary_style', '')}")
                if style_info.get('all_styles'):
                    description_parts.append(f"Style characteristics: {', '.join(style_info['all_styles'][:3])}")
            
        elif enhancement_mode == 'mood_matching':
            # Focus on mood and atmosphere
            if visual_elements.get('mood'):
                mood_info = visual_elements['mood']
                description_parts.append(f"Mood and atmosphere: {mood_info.get('primary_mood', '')}")
                if mood_info.get('all_moods'):
                    description_parts.append(f"Emotional qualities: {', '.join(mood_info['all_moods'][:3])}")
            
        elif enhancement_mode == 'composition_guide':
            # Focus on compositional elements
            if visual_elements.get('composition'):
                comp_info = visual_elements['composition']
                description_parts.append(f"Composition: {comp_info.get('primary_composition', '')}")
            
        # Always include core visual elements
        if visual_elements.get('color_palette'):
            color_info = visual_elements['color_palette']
            description_parts.append(f"Colors: {color_info.get('primary_palette', '')}")
        
        if visual_elements.get('lighting'):
            lighting_info = visual_elements['lighting']
            description_parts.append(f"Lighting: {lighting_info.get('primary_lighting', '')}")
        
        if visual_elements.get('objects'):
            objects_info = visual_elements['objects']
            description_parts.append(f"Subject matter: {objects_info.get('primary_subjects', '')}")
        
        # Add keywords for broader matching
        if visual_elements.get('keywords'):
            keywords = visual_elements['keywords'][:10]  # Limit keywords
            description_parts.append(f"Visual keywords: {', '.join(keywords)}")
        
        return ". ".join(description_parts)
    
    def _format_visual_analysis_for_search(self, visual_elements: Dict[str, Any]) -> str:
        """Format visual analysis optimized for semantic search"""
        
        search_terms = []
        
        # Extract high-level descriptors
        for element_type in ['style', 'mood', 'composition', 'lighting', 'color_palette', 'objects']:
            element_data = visual_elements.get(element_type, {})
            
            if isinstance(element_data, dict):
                primary_key = f'primary_{element_type}' if element_type != 'objects' else 'primary_subjects'
                primary_value = element_data.get(primary_key, '')
                if primary_value and primary_value != 'unknown':
                    search_terms.append(primary_value)
        
        # Add keywords
        if visual_elements.get('keywords'):
            search_terms.extend(visual_elements['keywords'][:8])
        
        return ' '.join(search_terms)
    
    async def _interpret_user_feedback(self, feedback: str) -> Dict[str, Any]:
        """Interpret user feedback using semantic analysis"""
        
        logger.info("Interpreting user feedback semantically")
        
        try:
            # Get embedding for feedback
            feedback_embedding = await self.embedding_service.get_embedding(feedback)
            
            # Find similar feedback patterns (if we have historical data)
            similar_feedback = await self.embedding_service.find_similar_prompts(
                feedback_embedding,
                limit=3,
                threshold=0.6
            )
            
            # Analyze feedback intent and category
            feedback_context = {
                'original_feedback': feedback,
                'similar_patterns': similar_feedback,
                'intent_category': self._categorize_feedback_intent(feedback),
                'adjustment_type': self._determine_adjustment_type(feedback),
                'intensity_level': self._determine_intensity_level(feedback),
                'action_words': self._extract_action_words(feedback)
            }
            
            return feedback_context
            
        except Exception as e:
            logger.warning(f"Feedback interpretation failed, using basic analysis: {str(e)}")
            
            # Fallback to basic text analysis
            return {
                'original_feedback': feedback,
                'similar_patterns': [],
                'intent_category': self._categorize_feedback_intent(feedback),
                'adjustment_type': self._determine_adjustment_type(feedback),
                'intensity_level': self._determine_intensity_level(feedback),
                'action_words': self._extract_action_words(feedback)
            }
    
    def _categorize_feedback_intent(self, feedback: str) -> str:
        """Categorize the type of feedback provided"""
        feedback_lower = feedback.lower()
        
        # Intensity adjustments
        if any(word in feedback_lower for word in ['more', 'increase', 'stronger', 'brighter', 'enhance']):
            return 'increase_intensity'
        elif any(word in feedback_lower for word in ['less', 'decrease', 'softer', 'subtle', 'reduce']):
            return 'decrease_intensity'
        
        # Style changes
        elif any(word in feedback_lower for word in ['different', 'change', 'alternative', 'instead']):
            return 'style_change'
        
        # Quality improvements
        elif any(word in feedback_lower for word in ['better', 'improve', 'enhance', 'fix', 'correct']):
            return 'quality_improvement'
        
        # Additions
        elif any(word in feedback_lower for word in ['add', 'include', 'with', 'also']):
            return 'add_elements'
        
        # Removals
        elif any(word in feedback_lower for word in ['remove', 'without', 'no', 'exclude']):
            return 'remove_elements'
        
        else:
            return 'general_modification'
    
    def _determine_adjustment_type(self, feedback: str) -> str:
        """Determine what aspect needs adjustment"""
        feedback_lower = feedback.lower()
        
        if any(word in feedback_lower for word in ['color', 'colors', 'palette', 'hue', 'saturation']):
            return 'color_adjustment'
        elif any(word in feedback_lower for word in ['light', 'lighting', 'bright', 'dark', 'shadow']):
            return 'lighting_adjustment'
        elif any(word in feedback_lower for word in ['dramatic', 'mood', 'atmosphere', 'feeling', 'emotion']):
            return 'mood_adjustment'
        elif any(word in feedback_lower for word in ['style', 'artistic', 'technique', 'approach']):
            return 'style_adjustment'
        elif any(word in feedback_lower for word in ['composition', 'layout', 'arrangement', 'balance']):
            return 'composition_adjustment'
        elif any(word in feedback_lower for word in ['detail', 'detailed', 'sharp', 'clear', 'focus']):
            return 'detail_adjustment'
        else:
            return 'general_adjustment'
    
    def _determine_intensity_level(self, feedback: str) -> str:
        """Determine the intensity of the requested change"""
        feedback_lower = feedback.lower()
        
        if any(word in feedback_lower for word in ['very', 'much', 'way', 'extremely', 'significantly']):
            return 'high'
        elif any(word in feedback_lower for word in ['slightly', 'bit', 'little', 'somewhat', 'minor']):
            return 'low'
        else:
            return 'medium'
    
    def _extract_action_words(self, feedback: str) -> List[str]:
        """Extract action words from feedback"""
        action_words = []
        common_actions = [
            'make', 'add', 'remove', 'change', 'increase', 'decrease', 'enhance', 
            'improve', 'fix', 'adjust', 'modify', 'create', 'generate'
        ]
        
        feedback_words = feedback.lower().split()
        for word in feedback_words:
            if word in common_actions:
                action_words.append(word)
        
        return action_words
    
    async def _store_enhancement_result(
        self,
        original_prompt: str,
        enhanced_prompt: str,
        visual_analysis: Dict[str, Any],
        enhancement_mode: str
    ):
        """Store enhancement results for future learning"""
        
        try:
            # Create enhancement record for storage
            enhancement_record = {
                'original_prompt': original_prompt,
                'enhanced_prompt': enhanced_prompt,
                'enhancement_mode': enhancement_mode,
                'visual_analysis_summary': self._summarize_visual_analysis(visual_analysis),
                'reference_image': visual_analysis.get('file_name', 'unknown'),
                'confidence_score': visual_analysis.get('confidence_score', 0.8),
                'timestamp': datetime.now().isoformat(),
                'type': 'multimodal_enhancement'
            }
            
            # Store enhanced prompt with metadata in embedding service
            await self.embedding_service.store_prompt_with_metadata(
                enhanced_prompt,
                enhancement_record
            )
            
            logger.info("Enhancement result stored for future learning")
            
        except Exception as e:
            logger.warning(f"Failed to store enhancement result: {str(e)}")
            # Don't fail the main operation if storage fails
    
    def _summarize_visual_analysis(self, visual_analysis: Dict[str, Any]) -> str:
        """Create a concise summary of visual analysis for storage"""
        
        elements = visual_analysis.get('visual_elements', {})
        summary_parts = []
        
        # Extract key information from each element
        for element_type, element_data in elements.items():
            if isinstance(element_data, dict):
                primary_key = [k for k in element_data.keys() if k.startswith('primary')][0] if any(k.startswith('primary') for k in element_data.keys()) else None
                if primary_key and element_data[primary_key] != 'unknown':
                    value = element_data[primary_key]
                    if isinstance(value, str) and len(value) > 0:
                        # Truncate long descriptions
                        truncated_value = value[:50] + '...' if len(value) > 50 else value
                        summary_parts.append(f"{element_type}: {truncated_value}")
        
        return "; ".join(summary_parts[:5])  # Limit to 5 key elements
    
    async def _generate_refinement_suggestions(
        self, 
        refinement_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate specific refinement suggestions based on context"""
        
        current_analysis = refinement_data['current_analysis']
        feedback_context = refinement_data['feedback_context']
        original_prompt = refinement_data['original_prompt']
        
        # Analyze what needs to be changed based on feedback
        adjustment_type = feedback_context['adjustment_type']
        intent_category = feedback_context['intent_category']
        intensity_level = feedback_context['intensity_level']
        
        suggestions = []
        modifications = []
        
        # Generate specific suggestions based on feedback analysis
        if adjustment_type == 'color_adjustment':
            current_colors = current_analysis['visual_elements']['color_palette']['primary_palette']
            if intent_category == 'increase_intensity':
                suggestions.append({
                    'type': 'color_enhancement',
                    'description': 'Increase color vibrancy and saturation',
                    'specific_change': f'Current colors: {current_colors}. Enhance with more vibrant, saturated colors.',
                    'confidence': 0.85
                })
                modifications.append('Add terms like "vibrant", "saturated", "rich colors"')
            
        elif adjustment_type == 'mood_adjustment':
            current_mood = current_analysis['visual_elements']['mood']['primary_mood']
            if 'dramatic' in feedback_context['original_feedback'].lower():
                suggestions.append({
                    'type': 'mood_enhancement',
                    'description': 'Increase dramatic impact and emotional intensity',
                    'specific_change': f'Current mood: {current_mood}. Enhance with dramatic lighting and composition.',
                    'confidence': 0.8
                })
                modifications.append('Add terms like "dramatic lighting", "high contrast", "cinematic"')
        
        # Add general suggestions if specific ones weren't generated
        if not suggestions:
            suggestions.append({
                'type': 'general_enhancement',
                'description': f'General {adjustment_type} based on feedback',
                'specific_change': f'Modify prompt to address: {feedback_context["original_feedback"]}',
                'confidence': 0.7
            })
        
        return {
            'suggestions': suggestions,
            'modifications': modifications,
            'confidence': max([s['confidence'] for s in suggestions]) if suggestions else 0.6
        }
    
    async def _enhance_similarity_results(
        self,
        similar_items: List[Dict[str, Any]],
        query_analysis: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Enhance similarity results with additional context and filtering"""
        
        enhanced_results = []
        
        for item in similar_items[:limit]:  # Limit results
            enhanced_item = item.copy()
            
            # Add visual similarity context (simplified scoring)
            enhanced_item['visual_similarity_context'] = {
                'style_compatibility': self._calculate_style_compatibility(
                    query_analysis, item
                ),
                'mood_similarity': self._calculate_mood_similarity(
                    query_analysis, item
                ),
                'overall_match_score': item.get('similarity_score', 0.5)
            }
            
            # Add usage suggestions
            enhanced_item['usage_suggestions'] = self._generate_usage_suggestions(
                query_analysis, item
            )
            
            enhanced_results.append(enhanced_item)
        
        # Sort by overall match score
        enhanced_results.sort(
            key=lambda x: x['visual_similarity_context']['overall_match_score'], 
            reverse=True
        )
        
        return enhanced_results
    
    def _calculate_style_compatibility(
        self, 
        query_analysis: Dict[str, Any], 
        similar_item: Dict[str, Any]
    ) -> str:
        """Calculate style compatibility between query and similar item"""
        # Simplified compatibility scoring
        # In a full implementation, this would use more sophisticated analysis
        
        query_keywords = set(query_analysis['visual_elements']['keywords'])
        
        # Extract keywords from similar item if available
        item_text = similar_item.get('prompt', '').lower()
        
        style_words = {'realistic', 'abstract', 'impressionist', 'digital', 'artistic', 'painted', 'photographic'}
        query_style_words = query_keywords.intersection(style_words)
        
        if any(word in item_text for word in query_style_words):
            return 'high'
        elif len(query_style_words) == 0:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_mood_similarity(
        self, 
        query_analysis: Dict[str, Any], 
        similar_item: Dict[str, Any]
    ) -> str:
        """Calculate mood similarity between query and similar item"""
        # Simplified mood similarity
        query_mood = query_analysis['visual_elements']['mood']['primary_mood'].lower()
        item_text = similar_item.get('prompt', '').lower()
        
        mood_words = {'dramatic', 'peaceful', 'energetic', 'calm', 'vibrant', 'dark', 'bright'}
        
        if any(word in query_mood and word in item_text for word in mood_words):
            return 'high'
        else:
            return 'medium'
    
    def _generate_usage_suggestions(
        self, 
        query_analysis: Dict[str, Any], 
        similar_item: Dict[str, Any]
    ) -> List[str]:
        """Generate suggestions for how to use similar items"""
        
        suggestions = []
        
        # Base suggestion
        suggestions.append("Use as inspiration for similar visual style")
        
        # Specific suggestions based on analysis
        query_style = query_analysis['visual_elements']['style']['primary_style']
        if query_style != 'unknown':
            suggestions.append(f"Adapt for {query_style} aesthetic")
        
        query_mood = query_analysis['visual_elements']['mood']['primary_mood']
        if query_mood != 'neutral':
            suggestions.append(f"Modify to match {query_mood} mood")
        
        # Add compositional suggestion if relevant
        suggestions.append("Consider compositional elements for your image")
        
        return suggestions[:3]  # Limit to 3 suggestions
    
    def _calculate_analysis_confidence(self, analysis_result) -> float:
        """Calculate overall confidence score for analysis quality"""
        
        # Get confidence scores from different analysis elements
        confidence_scores = []
        
        if hasattr(analysis_result, 'style_analysis'):
            confidence_scores.append(analysis_result.style_analysis.get('confidence', 0.5))
        if hasattr(analysis_result, 'mood_analysis'):
            confidence_scores.append(analysis_result.mood_analysis.get('confidence', 0.5))
        if hasattr(analysis_result, 'composition'):
            confidence_scores.append(analysis_result.composition.get('confidence', 0.5))
        
        # Calculate average confidence
        if confidence_scores:
            return sum(confidence_scores) / len(confidence_scores)
        else:
            return 0.5  # Default confidence
    
    def _generate_improvement_suggestions(
        self, 
        visual_analysis: Dict[str, Any], 
        similar_prompts: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate suggestions for improving the enhancement process"""
        
        suggestions = []
        
        # Confidence-based suggestions
        confidence = visual_analysis.get('confidence_score', 0.5)
        if confidence < 0.6:
            suggestions.append("Try using a higher resolution reference image for better analysis")
        
        # Similarity-based suggestions
        if len(similar_prompts) < 2:
            suggestions.append("Consider building more prompt history for better similarity matching")
        elif len(similar_prompts) > 8:
            suggestions.append("Great! Rich prompt history enables better enhancement suggestions")
        
        # Analysis quality suggestions
        keywords_count = len(visual_analysis.get('visual_elements', {}).get('keywords', []))
        if keywords_count < 5:
            suggestions.append("Try a reference image with more distinct visual characteristics")
        
        return suggestions[:2]  # Limit to top 2 suggestions
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check health of all multimodal service components
        
        Returns:
            Dict containing health status of all components
        """
        logger.info("Performing multimodal service health check")
        
        health_status = {
            'multimodal_service': 'checking',
            'llava_service': 'checking',
            'embedding_service': 'checking',
            'prompt_enhancement_service': 'checking',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Check LLaVA service
            llava_health = await self.llava.health_check()
            health_status['llava_service'] = llava_health.get('status', 'unknown')
            
            # Check embedding service
            try:
                # Simple test embedding
                test_embedding = await self.embedding_service.get_embedding("test")
                health_status['embedding_service'] = 'healthy' if test_embedding else 'unhealthy'
            except Exception as e:
                health_status['embedding_service'] = f'unhealthy: {str(e)}'
            
            # Check prompt enhancement service
            health_status['prompt_enhancement_service'] = 'healthy'  # Assume healthy if no errors
            
            # Overall status
            all_healthy = all(
                status == 'healthy' 
                for key, status in health_status.items() 
                if key not in ['timestamp', 'multimodal_service']
            )
            
            health_status['multimodal_service'] = 'healthy' if all_healthy else 'degraded'
            
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            health_status['multimodal_service'] = f'unhealthy: {str(e)}'
            return health_status

# Singleton instance for dependency injection
multimodal_service = MultimodalEnhancementService()
