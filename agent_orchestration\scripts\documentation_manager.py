#!/usr/bin/env python3
"""
Enhanced Documentation Overseer & Knowledge Management Agent
Comprehensive documentation management, organization, and context delivery specialist.

This agent is responsible for:
1. Intelligent knowledge base organization with semantic indexing
2. Automated context file generation for any topic
3. Advanced summarization and content synthesis
4. Real-time knowledge graph maintenance
5. LLM-ready context delivery system
6. Physical and logical file organization optimization
7. Continuous monitoring and proactive documentation generation

IMPORTANT: This agent provides RECOMMENDATIONS ONLY and never executes file moves or deletions.
All directory changes must be explicitly approved and executed by the user.
"""

import asyncio
import json
import os
import sqlite3
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any, Union
from datetime import datetime
import yaml
import hashlib
import logging
from dataclasses import dataclass, asdict
from collections import defaultdict
import traceback
import tempfile
import shutil

# Optional imports for enhanced functionality
try:
    import nltk
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.cluster import KMeans
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False

try:
    import networkx as nx
    GRAPH_AVAILABLE = True
except ImportError:
    GRAPH_AVAILABLE = False

@dataclass
class KnowledgeEntry:
    """Structured knowledge entry for the knowledge base."""
    id: str
    title: str
    content: str
    topic: str
    tags: List[str]
    source_file: str
    embedding: Optional[Any] = None
    last_updated: datetime = None
    related_entries: List[str] = None
    context_level: str = "detailed"  # 'overview', 'detailed', 'technical', 'reference'
    importance_score: float = 0.5
    access_count: int = 0
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()
        if self.related_entries is None:
            self.related_entries = []

@dataclass
class ContextTemplate:
    """Template for generating context files."""
    topic: str
    level: str
    sections: List[str]
    format_type: str = "markdown"  # 'markdown', 'json', 'yaml'
    max_tokens: int = 4000
    include_examples: bool = True
    include_references: bool = True

class AgentResult:
    """Result object for agent execution."""
    def __init__(self, status: str, data: Dict[str, Any], error: str = None):
        self.status = status
        self.data = data
        self.error = error
        self.timestamp = datetime.now().isoformat()

class BaseAgent:
    """Base agent class with logging and utility functions."""
    def __init__(self, context: Dict[str, Any]):
        self.context = context
        self.logger = logging.getLogger(self.__class__.__name__)
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message.""" 
        self.logger.warning(message)
        
    def log_error(self, message: str):
        """Log error message."""
        self.logger.error(message)

class EnhancedDocumentationOverseer(BaseAgent):
    """
    Enhanced Documentation Overseer with Knowledge Management
    
    Capabilities:
    - Semantic content organization and indexing
    - Automated context file generation for any topic
    - Intelligent summarization and synthesis
    - Knowledge graph construction and maintenance
    - LLM-ready context delivery
    - Physical file organization optimization
    - Continuous monitoring and proactive generation
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.agent_name = "enhanced-documentation-overseer"
        self.version = "3.0.0"
        
        # Enhanced components
        self.knowledge_base: Dict[str, KnowledgeEntry] = {}
        self.semantic_model = None
        self.knowledge_graph = {}
        self.topic_clusters: Dict[str, List[str]] = {}
        self.context_templates: Dict[str, ContextTemplate] = {}
        self.file_relationships: Dict[str, List[str]] = {}
        self.dependency_graph: Dict[str, Any] = {}
        self.project_structure_analysis: Dict[str, Any] = {}
        
        # Database connections
        self.db_connections: Dict[str, sqlite3.Connection] = {}
        
        # Initialize components
        self._initialize_semantic_processing()
        self._initialize_databases()
        self._load_context_templates()
        
    def _initialize_semantic_processing(self):
        """Initialize semantic processing capabilities."""
        if SEMANTIC_AVAILABLE:
            try:
                self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.log_info("Semantic processing initialized successfully")
            except Exception as e:
                self.log_warning(f"Could not initialize semantic model: {str(e)}")
                self.semantic_model = None
        else:
            self.log_warning("Semantic processing dependencies not available. Install sentence-transformers, scikit-learn, and numpy for enhanced features.")

    def _initialize_databases(self):
        """Initialize SQLite databases for knowledge management."""
        project_root = Path(self.context.get('project_root', '.'))
        data_dir = project_root / 'data' / 'knowledge_management'
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Knowledge base database
        kb_db_path = data_dir / 'knowledge_base.db'
        self.db_connections['knowledge_base'] = sqlite3.connect(str(kb_db_path))
        self._create_knowledge_base_schema()
        
        # Context database
        context_db_path = data_dir / 'context_management.db'
        self.db_connections['context'] = sqlite3.connect(str(context_db_path))
        self._create_context_schema()
        
        # File organization database
        org_db_path = data_dir / 'file_organization.db'
        self.db_connections['organization'] = sqlite3.connect(str(org_db_path))
        self._create_organization_schema()

    def _create_knowledge_base_schema(self):
        """Create knowledge base database schema."""
        cursor = self.db_connections['knowledge_base'].cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_entries (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                topic TEXT NOT NULL,
                tags TEXT NOT NULL,
                source_file TEXT NOT NULL,
                context_level TEXT NOT NULL,
                importance_score REAL DEFAULT 0.5,
                access_count INTEGER DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entry_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_id TEXT NOT NULL,
                related_entry_id TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                strength REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entry_id) REFERENCES knowledge_entries (id),
                FOREIGN KEY (related_entry_id) REFERENCES knowledge_entries (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS semantic_embeddings (
                entry_id TEXT PRIMARY KEY,
                embedding BLOB,
                model_version TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entry_id) REFERENCES knowledge_entries (id)
            )
        ''')
        
        self.db_connections['knowledge_base'].commit()

    def _create_context_schema(self):
        """Create context management database schema."""
        cursor = self.db_connections['context'].cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS context_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                level TEXT NOT NULL,
                file_path TEXT NOT NULL,
                size INTEGER,
                token_count INTEGER,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS context_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                context_file_id INTEGER NOT NULL,
                query TEXT,
                relevance_score REAL,
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (context_file_id) REFERENCES context_files (id)
            )
        ''')
        
        self.db_connections['context'].commit()

    def _create_organization_schema(self):
        """Create file organization database schema."""
        cursor = self.db_connections['organization'].cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS file_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT NOT NULL UNIQUE,
                file_type TEXT,
                size INTEGER,
                content_hash TEXT,
                topic_cluster TEXT,
                importance_score REAL DEFAULT 0.5,
                last_analyzed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS directory_recommendations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_path TEXT NOT NULL,
                recommended_path TEXT NOT NULL,
                reason TEXT,
                confidence_score REAL,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.db_connections['organization'].commit()

    def _load_context_templates(self):
        """Load predefined context templates."""
        self.context_templates = {
            'overview': ContextTemplate(
                topic="",
                level="overview",
                sections=["Summary", "Key Concepts", "Quick References"],
                max_tokens=1500,
                include_examples=False,
                include_references=True
            ),
            'detailed': ContextTemplate(
                topic="",
                level="detailed",
                sections=["Introduction", "Core Concepts", "Implementation Details", "Examples", "Best Practices"],
                max_tokens=3000,
                include_examples=True,
                include_references=True
            ),
            'technical': ContextTemplate(
                topic="",
                level="technical",
                sections=["Architecture", "API Reference", "Code Examples", "Configuration", "Troubleshooting"],
                max_tokens=4000,
                include_examples=True,
                include_references=True
            ),
            'reference': ContextTemplate(
                topic="",
                level="reference",
                sections=["API Documentation", "Parameters", "Return Values", "Examples", "Related Functions"],
                max_tokens=2000,
                include_examples=True,
                include_references=False
            )
        }

    async def execute(self, context: Dict[str, Any]) -> AgentResult:
        """Execute enhanced documentation overseer tasks."""
        task_info = context.get('task', {})
        if isinstance(task_info, dict):
            task_name = task_info.get('name', 'unknown')
            parameters = task_info.get('parameters', {})
        else:
            task_name = context.get('task_name', context.get('task', 'unknown'))
            parameters = context.get('parameters', context.get('params', {}))
        
        self.log_info(f"Executing enhanced task: {task_name}")
        
        try:
            # Original tasks (enhanced)
            if task_name == "audit_documentation":
                return await self._enhanced_audit_documentation(parameters)
            elif task_name == "directory_management":
                return await self._enhanced_directory_management(parameters)
            elif task_name == "validate_directory_proposal":
                return await self._validate_directory_proposal(parameters)
            elif task_name == "track_file_relationships":
                return await self._track_file_relationships(parameters)
            elif task_name == "project_improvement_strategy":
                return await self._project_improvement_strategy(parameters)
            elif task_name == "adaptive_monitoring":
                return await self._adaptive_monitoring(parameters)
            elif task_name == "sync_with_code":
                return await self._sync_with_code(parameters)
            
            # New knowledge management tasks
            elif task_name == "build_knowledge_base":
                return await self._build_knowledge_base(parameters)
            elif task_name == "generate_context_files":
                return await self._generate_context_files(parameters)
            elif task_name == "semantic_organization":
                return await self._semantic_organization(parameters)
            elif task_name == "knowledge_synthesis":
                return await self._knowledge_synthesis(parameters)
            elif task_name == "llm_context_delivery":
                return await self._llm_context_delivery(parameters)
            elif task_name == "maintain_knowledge_graph":
                return await self._maintain_knowledge_graph(parameters)
            elif task_name == "intelligent_file_organization":
                return await self._intelligent_file_organization(parameters)
            elif task_name == "content_summarization":
                return await self._content_summarization(parameters)
            elif task_name == "topic_clustering":
                return await self._topic_clustering(parameters)
            elif task_name == "context_extraction":
                return await self._context_extraction(parameters)
            elif task_name == "update_knowledge_from_changes":
                return await self._update_knowledge_from_changes(parameters)
            elif task_name == "generate_master_context_index":
                return await self._generate_master_context_index(parameters)
            
            else:
                available_tasks = [
                    "audit_documentation", "directory_management", "validate_directory_proposal",
                    "track_file_relationships", "project_improvement_strategy", "adaptive_monitoring",
                    "sync_with_code", "build_knowledge_base", "generate_context_files",
                    "semantic_organization", "knowledge_synthesis", "llm_context_delivery",
                    "maintain_knowledge_graph", "intelligent_file_organization", "content_summarization",
                    "topic_clustering", "context_extraction", "update_knowledge_from_changes",
                    "generate_master_context_index"
                ]
                return AgentResult(
                    status="error",
                    data={"error": f"Unknown task: {task_name}", "available_tasks": available_tasks},
                    error=f"Unknown task: {task_name}"
                )
                
        except Exception as e:
            self.log_error(f"Enhanced task execution failed: {str(e)}")
            self.log_error(traceback.format_exc())
            return AgentResult(status="error", data={"error": str(e)}, error=str(e))

    async def _build_knowledge_base(self, params: Dict[str, Any]) -> AgentResult:
        """Build comprehensive knowledge base with semantic indexing."""
        self.log_info("Building comprehensive knowledge base")
        
        result = AgentResult(
            status="success",
            data={
                "knowledge_base_stats": {},
                "semantic_index": {},
                "topic_distribution": {},
                "knowledge_entries": [],
                "indexing_metadata": {}
            }
        )
        
        try:
            # Scan all content files
            content_files = await self._scan_all_content_files()
            self.log_info(f"Found {len(content_files)} content files")
            
            # Process each file into knowledge entries
            knowledge_entries = []
            for file_path in content_files:
                entries = await self._extract_knowledge_entries(file_path)
                knowledge_entries.extend(entries)
            
            # Generate semantic embeddings
            if self.semantic_model and params.get("generate_embeddings", True):
                embeddings = await self._generate_embeddings(knowledge_entries)
                result.data["semantic_index"]["embeddings_generated"] = len(embeddings)
            
            # Cluster by topics
            if params.get("cluster_topics", True):
                clusters = await self._cluster_knowledge_entries(knowledge_entries)
                result.data["topic_distribution"] = clusters
            
            # Build knowledge graph relationships
            if params.get("build_relationships", True):
                relationships = await self._build_knowledge_relationships(knowledge_entries)
                result.data["knowledge_graph"] = relationships
            
            # Store knowledge base
            await self._store_knowledge_entries(knowledge_entries)
            self.knowledge_base = {entry.id: entry for entry in knowledge_entries}
            
            result.data["knowledge_base_stats"] = {
                "total_entries": len(knowledge_entries),
                "unique_topics": len(set(entry.topic for entry in knowledge_entries)),
                "total_tags": len(set(tag for entry in knowledge_entries for tag in entry.tags)),
                "files_processed": len(content_files)
            }
            
            # Generate knowledge base index
            await self._generate_knowledge_index(knowledge_entries)
            
            self.log_info(f"Knowledge base built with {len(knowledge_entries)} entries")
            
        except Exception as e:
            self.log_error(f"Failed to build knowledge base: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _generate_context_files(self, params: Dict[str, Any]) -> AgentResult:
        """Generate standardized context files for topics."""
        self.log_info("Generating context files for topics")
        
        topics = params.get("topics", [])
        if not topics:
            # Auto-detect topics from knowledge base
            topics = await self._auto_detect_topics()
        
        result = AgentResult(
            status="success",
            data={
                "context_files_generated": [],
                "topics_processed": topics,
                "context_templates": {},
                "llm_ready_contexts": []
            }
        )
        
        try:
            for topic in topics:
                # Generate comprehensive context for topic
                context_data = await self._generate_topic_context(topic, params)
                
                # Create multiple context levels
                context_levels = params.get("levels", ["overview", "detailed", "technical", "reference"])
                for level in context_levels:
                    context_file = await self._create_context_file(topic, level, context_data)
                    if context_file:
                        result.data["context_files_generated"].append({
                            "topic": topic,
                            "level": level,
                            "file_path": context_file["path"],
                            "size": context_file["size"],
                            "token_estimate": context_file.get("token_estimate", 0),
                            "last_updated": datetime.now().isoformat()
                        })
            
            # Generate master context index
            master_index = await self._generate_master_context_index_internal(result.data["context_files_generated"])
            result.data["master_index"] = master_index
            
            self.log_info(f"Generated context files for {len(topics)} topics")
            
        except Exception as e:
            self.log_error(f"Failed to generate context files: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _llm_context_delivery(self, params: Dict[str, Any]) -> AgentResult:
        """Deliver optimized context for LLM consumption."""
        self.log_info("Preparing LLM-optimized context delivery")
        
        query = params.get("query", "")
        context_type = params.get("context_type", "comprehensive")
        max_tokens = params.get("max_tokens", 4000)
        
        result = AgentResult(
            status="success",
            data={
                "context_delivery": {},
                "relevance_scores": {},
                "context_structure": {},
                "ready_for_llm": True
            }
        )
        
        try:
            # Find relevant knowledge entries
            relevant_entries = await self._find_relevant_entries(query, max_results=10)
            
            # Rank by relevance
            ranked_entries = await self._rank_entries_by_relevance(relevant_entries, query)
            
            # Generate structured context
            structured_context = await self._generate_structured_context(
                ranked_entries, context_type, max_tokens
            )
            
            result.data["context_delivery"] = {
                "primary_context": structured_context["primary"],
                "supporting_context": structured_context["supporting"],
                "related_topics": structured_context["related"],
                "key_concepts": structured_context["concepts"],
                "file_references": structured_context["files"]
            }
            
            # Add metadata for LLM optimization
            result.data["llm_metadata"] = {
                "token_count_estimate": structured_context["token_estimate"],
                "context_completeness": structured_context["completeness_score"],
                "last_updated": datetime.now().isoformat(),
                "source_confidence": structured_context["confidence_score"]
            }
            
            self.log_info(f"LLM context prepared with {len(ranked_entries)} relevant entries")
            
        except Exception as e:
            self.log_error(f"Failed to prepare LLM context: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _intelligent_file_organization(self, params: Dict[str, Any]) -> AgentResult:
        """Intelligently organize files based on content and relationships."""
        self.log_info("Performing intelligent file organization analysis")
        
        result = AgentResult(
            status="success",
            data={
                "organization_strategy": {},
                "proposed_structure": {},
                "migration_plan": {},
                "content_based_groupings": {},
                "optimization_recommendations": []
            }
        )
        
        try:
            # Analyze current file organization
            current_analysis = await self._analyze_current_file_organization()
            
            # Generate content-based groupings
            content_groups = await self._generate_content_based_groupings()
            result.data["content_based_groupings"] = content_groups
            
            # Create optimal directory structure
            optimal_structure = await self._design_optimal_structure(content_groups, params)
            result.data["proposed_structure"] = optimal_structure
            
            # Generate migration plan
            migration_plan = await self._generate_intelligent_migration_plan(
                current_analysis, optimal_structure
            )
            result.data["migration_plan"] = migration_plan
            
            # Add safety recommendations
            result.data["safety_measures"] = [
                "Complete backup before any file operations",
                "Incremental migration in phases",
                "Validation checkpoints after each phase",
                "Rollback procedures for each migration step"
            ]
            
            self.log_info("Intelligent file organization analysis completed")
            
        except Exception as e:
            self.log_error(f"Failed intelligent file organization: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    # Enhanced helper methods
    
    async def _scan_all_content_files(self) -> List[Path]:
        """Scan for all content files that can contribute to knowledge base."""
        content_extensions = {'.md', '.rst', '.txt', '.py', '.js', '.ts', '.json', '.yaml', '.yml', '.adoc'}
        exclude_patterns = {'.git', 'node_modules', '__pycache__', '.venv', 'venv', 'dist', 'build'}
        content_files = []
        
        project_root = Path(self.context.get('project_root', '.'))
        
        for file_path in project_root.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix.lower() in content_extensions and
                not any(exclude in str(file_path) for exclude in exclude_patterns)):
                content_files.append(file_path)
        
        return content_files

    async def _extract_knowledge_entries(self, file_path: Path) -> List[KnowledgeEntry]:
        """Extract knowledge entries from a file."""
        entries = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if file_path.suffix == '.md':
                entries.extend(await self._extract_markdown_entries(file_path, content))
            elif file_path.suffix in {'.py', '.js', '.ts'}:
                entries.extend(await self._extract_code_entries(file_path, content))
            elif file_path.suffix in {'.json', '.yaml', '.yml'}:
                entries.extend(await self._extract_config_entries(file_path, content))
            else:
                # Generic text extraction
                entry = await self._create_generic_entry(file_path, content)
                if entry:
                    entries.append(entry)
                    
        except Exception as e:
            self.log_warning(f"Could not extract knowledge from {file_path}: {str(e)}")
        
        return entries

    async def _extract_markdown_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract structured knowledge from markdown files."""
        entries = []
        
        # Split by headers
        sections = re.split(r'^(#+)\s+(.+)$', content, flags=re.MULTILINE)
        
        current_title = str(file_path.stem)
        current_content = ""
        
        for i in range(0, len(sections)):
            if i % 3 == 1:  # Header level
                header_level = sections[i]
                if i + 1 < len(sections):
                    title = sections[i + 1].strip()
                    if i + 2 < len(sections):
                        section_content = sections[i + 2].strip()
                        
                        if len(section_content) > 100:  # Only substantial content
                            entry = KnowledgeEntry(
                                id=hashlib.md5(f"{file_path}:{title}".encode()).hexdigest(),
                                title=title,
                                content=section_content,
                                topic=await self._infer_topic(title, section_content),
                                tags=await self._extract_tags(section_content),
                                source_file=str(file_path),
                                last_updated=datetime.fromtimestamp(file_path.stat().st_mtime),
                                context_level="detailed"
                            )
                            entries.append(entry)
        
        return entries

    async def _extract_code_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from code files."""
        entries = []
        
        try:
            # Extract docstrings and comments
            if file_path.suffix == '.py':
                entries.extend(await self._extract_python_entries(file_path, content))
            elif file_path.suffix in {'.js', '.ts'}:
                entries.extend(await self._extract_javascript_entries(file_path, content))
                
        except Exception as e:
            self.log_warning(f"Could not extract code entries from {file_path}: {str(e)}")
        
        return entries

    async def _extract_python_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from Python files."""
        entries = []
        
        # Extract module docstring
        module_docstring_match = re.search(r'^"""(.*?)"""', content, re.DOTALL | re.MULTILINE)
        if module_docstring_match:
            docstring = module_docstring_match.group(1).strip()
            if len(docstring) > 50:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:module".encode()).hexdigest(),
                    title=f"{file_path.stem} Module",
                    content=docstring,
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["python", "module", "documentation"],
                    source_file=str(file_path),
                    context_level="overview"
                )
                entries.append(entry)
        
        # Extract class and function docstrings
        class_pattern = r'class\s+(\w+).*?:\s*"""(.*?)"""'
        for match in re.finditer(class_pattern, content, re.DOTALL):
            class_name, docstring = match.groups()
            if len(docstring.strip()) > 30:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:class:{class_name}".encode()).hexdigest(),
                    title=f"{class_name} Class",
                    content=docstring.strip(),
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["python", "class", class_name.lower()],
                    source_file=str(file_path),
                    context_level="technical"
                )
                entries.append(entry)
        
        function_pattern = r'def\s+(\w+).*?:\s*"""(.*?)"""'
        for match in re.finditer(function_pattern, content, re.DOTALL):
            func_name, docstring = match.groups()
            if len(docstring.strip()) > 30:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:function:{func_name}".encode()).hexdigest(),
                    title=f"{func_name} Function",
                    content=docstring.strip(),
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["python", "function", func_name.lower()],
                    source_file=str(file_path),
                    context_level="reference"
                )
                entries.append(entry)
        
        return entries

    async def _extract_javascript_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from JavaScript/TypeScript files."""
        entries = []
        
        # Extract JSDoc comments
        jsdoc_pattern = r'/\*\*(.*?)\*/'
        for match in re.finditer(jsdoc_pattern, content, re.DOTALL):
            comment = match.group(1).strip()
            # Clean up JSDoc formatting
            comment = re.sub(r'^\s*\*\s?', '', comment, flags=re.MULTILINE)
            
            if len(comment) > 50:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:jsdoc:{match.start()}".encode()).hexdigest(),
                    title=f"Documentation from {file_path.name}",
                    content=comment,
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["javascript", "typescript", "documentation"],
                    source_file=str(file_path),
                    context_level="technical"
                )
                entries.append(entry)
        
        return entries

    async def _extract_config_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from configuration files."""
        entries = []
        
        try:
            if file_path.suffix == '.json':
                data = json.loads(content)
            elif file_path.suffix in {'.yaml', '.yml'}:
                data = yaml.safe_load(content)
            else:
                return entries
            
            # Extract meaningful configuration information
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (dict, list)) and len(str(value)) > 100:
                        entry = KnowledgeEntry(
                            id=hashlib.md5(f"{file_path}:config:{key}".encode()).hexdigest(),
                            title=f"Configuration: {key}",
                            content=json.dumps(value, indent=2) if isinstance(value, (dict, list)) else str(value),
                            topic=f"configuration-{file_path.stem}",
                            tags=["configuration", key.lower(), file_path.suffix[1:]],
                            source_file=str(file_path),
                            context_level="reference"
                        )
                        entries.append(entry)
                        
        except Exception as e:
            self.log_warning(f"Could not parse config file {file_path}: {str(e)}")
        
        return entries

    async def _create_generic_entry(self, file_path: Path, content: str) -> Optional[KnowledgeEntry]:
        """Create a generic knowledge entry for text files."""
        if len(content.strip()) < 100:
            return None
        
        # Take first 500 characters as preview
        preview = content[:500] + "..." if len(content) > 500 else content
        
        return KnowledgeEntry(
            id=hashlib.md5(f"{file_path}:generic".encode()).hexdigest(),
            title=f"Content from {file_path.name}",
            content=preview,
            topic=await self._infer_topic_from_filename(file_path),
            tags=["text", "generic", file_path.suffix[1:] if file_path.suffix else "unknown"],
            source_file=str(file_path),
            context_level="overview"
        )

    async def _infer_topic(self, title: str, content: str) -> str:
        """Infer topic from title and content."""
        # Simple keyword-based topic inference
        tech_keywords = {
            'react': 'frontend-react',
            'vue': 'frontend-vue',
            'angular': 'frontend-angular',
            'python': 'backend-python',
            'javascript': 'frontend-javascript',
            'typescript': 'frontend-typescript',
            'node': 'backend-nodejs',
            'api': 'backend-api',
            'database': 'database',
            'sql': 'database',
            'docker': 'devops',
            'kubernetes': 'devops',
            'test': 'testing',
            'security': 'security',
            'performance': 'performance',
            'ui': 'frontend-ui',
            'ux': 'frontend-ux'
        }
        
        text = (title + " " + content).lower()
        
        for keyword, topic in tech_keywords.items():
            if keyword in text:
                return topic
        
        return "general"

    async def _infer_topic_from_filename(self, file_path: Path) -> str:
        """Infer topic from filename and path."""
        path_str = str(file_path).lower()
        
        if 'frontend' in path_str or 'ui' in path_str:
            return 'frontend'
        elif 'backend' in path_str or 'server' in path_str:
            return 'backend'
        elif 'test' in path_str:
            return 'testing'
        elif 'doc' in path_str:
            return 'documentation'
        elif 'config' in path_str:
            return 'configuration'
        else:
            return 'general'

    async def _extract_tags(self, content: str) -> List[str]:
        """Extract relevant tags from content."""
        tags = []
        
        # Common technical terms
        tech_terms = [
            'api', 'rest', 'graphql', 'websocket', 'http', 'https',
            'react', 'vue', 'angular', 'svelte', 'javascript', 'typescript',
            'python', 'java', 'csharp', 'golang', 'rust',
            'docker', 'kubernetes', 'aws', 'azure', 'gcp',
            'database', 'sql', 'nosql', 'mongodb', 'postgresql',
            'security', 'authentication', 'authorization', 'oauth',
            'testing', 'unit test', 'integration test', 'e2e'
        ]
        
        content_lower = content.lower()
        for term in tech_terms:
            if term in content_lower:
                tags.append(term.replace(' ', '-'))
        
        return tags[:10]  # Limit to 10 tags

    async def _generate_embeddings(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Generate semantic embeddings for knowledge entries."""
        if not self.semantic_model:
            return {}
        
        embeddings = {}
        texts = [f"{entry.title} {entry.content}" for entry in knowledge_entries]
        
        try:
            vectors = self.semantic_model.encode(texts)
            for i, entry in enumerate(knowledge_entries):
                entry.embedding = vectors[i]
                embeddings[entry.id] = vectors[i].tolist()  # Convert to list for JSON serialization
                
                # Store in database
                await self._store_embedding(entry.id, vectors[i])
                
        except Exception as e:
            self.log_error(f"Error generating embeddings: {str(e)}")
        
        return embeddings

    async def _store_embedding(self, entry_id: str, embedding: Any):
        """Store embedding in database."""
        try:
            cursor = self.db_connections['knowledge_base'].cursor()
            embedding_bytes = embedding.tobytes() if hasattr(embedding, 'tobytes') else bytes(embedding)
            
            cursor.execute('''
                INSERT OR REPLACE INTO semantic_embeddings (entry_id, embedding, model_version)
                VALUES (?, ?, ?)
            ''', (entry_id, embedding_bytes, "all-MiniLM-L6-v2"))
            
            self.db_connections['knowledge_base'].commit()
        except Exception as e:
            self.log_error(f"Failed to store embedding for {entry_id}: {str(e)}")

    async def _cluster_knowledge_entries(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Cluster knowledge entries by topic similarity."""
        if not self.semantic_model or not SEMANTIC_AVAILABLE:
            # Fallback to simple keyword-based clustering
            return await self._simple_topic_clustering(knowledge_entries)
        
        try:
            # Get embeddings
            embeddings = [entry.embedding for entry in knowledge_entries if entry.embedding is not None]
            if not embeddings:
                return await self._simple_topic_clustering(knowledge_entries)
            
            # Perform K-means clustering
            n_clusters = min(10, len(embeddings) // 5 + 1)  # Adaptive number of clusters
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings)
            
            # Group entries by cluster
            clusters = defaultdict(list)
            for i, entry in enumerate([e for e in knowledge_entries if e.embedding is not None]):
                cluster_id = f"cluster_{cluster_labels[i]}"
                clusters[cluster_id].append({
                    "id": entry.id,
                    "title": entry.title,
                    "topic": entry.topic
                })
            
            return dict(clusters)
            
        except Exception as e:
            self.log_error(f"Error in semantic clustering: {str(e)}")
            return await self._simple_topic_clustering(knowledge_entries)

    async def _simple_topic_clustering(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Simple topic-based clustering fallback."""
        clusters = defaultdict(list)
        
        for entry in knowledge_entries:
            clusters[entry.topic].append({
                "id": entry.id,
                "title": entry.title,
                "topic": entry.topic
            })
        
        return dict(clusters)

    async def _build_knowledge_relationships(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Build relationships between knowledge entries."""
        relationships = {
            "nodes": [],
            "edges": [],
            "metadata": {
                "total_entries": len(knowledge_entries),
                "relationship_types": ["similar", "references", "depends_on"]
            }
        }
        
        # Add nodes
        for entry in knowledge_entries:
            relationships["nodes"].append({
                "id": entry.id,
                "title": entry.title,
                "topic": entry.topic,
                "importance": entry.importance_score
            })
        
        # Build edges based on content similarity and references
        await self._build_similarity_edges(knowledge_entries, relationships)
        await self._build_reference_edges(knowledge_entries, relationships)
        
        return relationships

    async def _build_similarity_edges(self, knowledge_entries: List[KnowledgeEntry], relationships: Dict[str, Any]):
        """Build edges based on content similarity."""
        if not self.semantic_model or not SEMANTIC_AVAILABLE:
            return
        
        try:
            embeddings = [entry.embedding for entry in knowledge_entries if entry.embedding is not None]
            if len(embeddings) < 2:
                return
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(embeddings)
            
            # Add edges for highly similar entries
            for i, entry_i in enumerate([e for e in knowledge_entries if e.embedding is not None]):
                for j, entry_j in enumerate([e for e in knowledge_entries if e.embedding is not None]):
                    if i != j and similarity_matrix[i][j] > 0.7:  # High similarity threshold
                        relationships["edges"].append({
                            "source": entry_i.id,
                            "target": entry_j.id,
                            "type": "similar",
                            "weight": float(similarity_matrix[i][j])
                        })
                        
        except Exception as e:
            self.log_error(f"Error building similarity edges: {str(e)}")

    async def _build_reference_edges(self, knowledge_entries: List[KnowledgeEntry], relationships: Dict[str, Any]):
        """Build edges based on explicit references."""
        for entry in knowledge_entries:
            # Look for references to other entries
            for other_entry in knowledge_entries:
                if entry.id != other_entry.id:
                    # Check if entry content references other entry
                    if (other_entry.title.lower() in entry.content.lower() or
                        any(tag in entry.content.lower() for tag in other_entry.tags)):
                        relationships["edges"].append({
                            "source": entry.id,
                            "target": other_entry.id,
                            "type": "references",
                            "weight": 0.5
                        })

    async def _store_knowledge_entries(self, knowledge_entries: List[KnowledgeEntry]):
        """Store knowledge entries in database."""
        cursor = self.db_connections['knowledge_base'].cursor()
        
        for entry in knowledge_entries:
            cursor.execute('''
                INSERT OR REPLACE INTO knowledge_entries 
                (id, title, content, topic, tags, source_file, context_level, importance_score, access_count, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                entry.id, entry.title, entry.content, entry.topic,
                json.dumps(entry.tags), entry.source_file, entry.context_level,
                entry.importance_score, entry.access_count, entry.last_updated
            ))
        
        self.db_connections['knowledge_base'].commit()

    async def _generate_knowledge_index(self, knowledge_entries: List[KnowledgeEntry]):
        """Generate comprehensive knowledge index."""
        index = {
            "version": self.version,
            "generated_at": datetime.now().isoformat(),
            "total_entries": len(knowledge_entries),
            "topics": {},
            "tags": {},
            "files": {}
        }
        
        # Index by topics
        for entry in knowledge_entries:
            if entry.topic not in index["topics"]:
                index["topics"][entry.topic] = []
            index["topics"][entry.topic].append({
                "id": entry.id,
                "title": entry.title,
                "importance": entry.importance_score
            })
        
        # Index by tags
        for entry in knowledge_entries:
            for tag in entry.tags:
                if tag not in index["tags"]:
                    index["tags"][tag] = []
                index["tags"][tag].append(entry.id)
        
        # Index by files
        for entry in knowledge_entries:
            if entry.source_file not in index["files"]:
                index["files"][entry.source_file] = []
            index["files"][entry.source_file].append(entry.id)
        
        # Save index
        project_root = Path(self.context.get('project_root', '.'))
        contexts_dir = project_root / 'contexts'
        contexts_dir.mkdir(exist_ok=True)
        
        index_path = contexts_dir / 'knowledge_index.json'
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index, f, indent=2, ensure_ascii=False)

    async def _auto_detect_topics(self) -> List[str]:
        """Auto-detect topics from knowledge base."""
        if not self.knowledge_base:
            return ["general", "documentation", "code", "configuration"]
        
        topics = set()
        for entry in self.knowledge_base.values():
            topics.add(entry.topic)
        
        return list(topics)

    async def _generate_topic_context(self, topic: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive context data for a topic."""
        context_data = {
            "topic": topic,
            "overview": "",
            "key_concepts": [],
            "related_files": [],
            "dependencies": [],
            "examples": [],
            "references": [],
            "last_updated": datetime.now().isoformat()
        }
        
        # Find all entries related to this topic
        related_entries = [entry for entry in self.knowledge_base.values() if entry.topic == topic]
        
        if related_entries:
            # Generate overview
            context_data["overview"] = await self._synthesize_topic_overview(related_entries)
            
            # Extract key concepts
            context_data["key_concepts"] = await self._extract_key_concepts(related_entries)
            
            # Find related files
            context_data["related_files"] = list(set(entry.source_file for entry in related_entries))
            
            # Find examples
            context_data["examples"] = await self._extract_examples(related_entries)
            
            # Find dependencies
            context_data["dependencies"] = await self._extract_dependencies(related_entries)
        
        return context_data

    async def _synthesize_topic_overview(self, entries: List[KnowledgeEntry]) -> str:
        """Synthesize an overview from multiple knowledge entries."""
        if not entries:
            return "No information available for this topic."
        
        # Sort by importance and take top entries
        sorted_entries = sorted(entries, key=lambda x: x.importance_score, reverse=True)
        top_entries = sorted_entries[:3]
        
        overview_parts = []
        for entry in top_entries:
            # Take first sentence or paragraph
            first_sentence = entry.content.split('.')[0] + '.'
            if len(first_sentence) > 200:
                first_sentence = entry.content[:200] + "..."
            overview_parts.append(f"**{entry.title}**: {first_sentence}")
        
        return "\n\n".join(overview_parts)

    async def _extract_key_concepts(self, entries: List[KnowledgeEntry]) -> List[str]:
        """Extract key concepts from entries."""
        concepts = set()
        
        for entry in entries:
            # Extract from tags
            concepts.update(entry.tags)
            
            # Extract from title
            title_words = re.findall(r'\b[A-Za-z]{3,}\b', entry.title)
            concepts.update(word.lower() for word in title_words)
        
        # Filter and return most relevant concepts
        filtered_concepts = [c for c in concepts if len(c) > 2 and c not in {'the', 'and', 'for', 'with'}]
        return sorted(filtered_concepts)[:10]

    async def _extract_examples(self, entries: List[KnowledgeEntry]) -> List[Dict[str, str]]:
        """Extract code examples and usage examples from entries."""
        examples = []
        
        for entry in entries:
            # Look for code blocks
            code_blocks = re.findall(r'``````', entry.content, re.DOTALL)
            for code in code_blocks:
                if len(code.strip()) > 20:  # Substantial code
                    examples.append({
                        "type": "code",
                        "content": code.strip(),
                        "source": entry.title
                    })
            
            # Look for example sections
            example_sections = re.findall(r'[Ee]xample:?\s*(.*?)(?:\n\n|\Z)', entry.content, re.DOTALL)
            for example in example_sections:
                if len(example.strip()) > 20:
                    examples.append({
                        "type": "usage",
                        "content": example.strip(),
                        "source": entry.title
                    })
        
        return examples[:5]  # Limit to 5 examples

    async def _extract_dependencies(self, entries: List[KnowledgeEntry]) -> List[str]:
        """Extract dependencies mentioned in entries."""
        dependencies = set()
        
        for entry in entries:
            # Look for import statements
            imports = re.findall(r'import\s+(\w+)', entry.content)
            dependencies.update(imports)
            
            # Look for require statements
            requires = re.findall(r'require\([\'"]([^\'"]+)[\'"]\)', entry.content)
            dependencies.update(requires)
            
            # Look for explicit dependency mentions
            dep_patterns = [
                r'depends on (\w+)',
                r'requires (\w+)',
                r'uses (\w+)',
                r'built with (\w+)'
            ]
            
            for pattern in dep_patterns:
                matches = re.findall(pattern, entry.content, re.IGNORECASE)
                dependencies.update(matches)
        
        return list(dependencies)[:10]

    async def _create_context_file(self, topic: str, level: str, context_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a context file for a specific topic and detail level."""
        try:
            # Create context directory if it doesn't exist
            project_root = Path(self.context.get('project_root', '.'))
            context_dir = project_root / 'contexts'
            context_dir.mkdir(exist_ok=True)
            
            # Generate filename
            filename = f"{topic.lower().replace(' ', '_').replace('-', '_')}_{level}.md"
            file_path = context_dir / filename
            
            # Generate content based on level
            content = await self._generate_context_content(topic, level, context_data)
            
            # Write file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Estimate token count (rough approximation)
            token_estimate = len(content.split()) * 1.3  # Approximate tokens
            
            # Store in database
            await self._store_context_file_record(topic, level, str(file_path), len(content), int(token_estimate))
            
            return {
                "path": str(file_path),
                "size": len(content),
                "topic": topic,
                "level": level,
                "token_estimate": int(token_estimate)
            }
            
        except Exception as e:
            self.log_error(f"Failed to create context file for {topic}/{level}: {str(e)}")
            return None

    async def _store_context_file_record(self, topic: str, level: str, file_path: str, size: int, token_count: int):
        """Store context file record in database."""
        try:
            cursor = self.db_connections['context'].cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO context_files (topic, level, file_path, size, token_count)
                VALUES (?, ?, ?, ?, ?)
            ''', (topic, level, file_path, size, token_count))
            self.db_connections['context'].commit()
        except Exception as e:
            self.log_error(f"Failed to store context file record: {str(e)}")

    async def _generate_context_content(self, topic: str, level: str, context_data: Dict[str, Any]) -> str:
        """Generate context content based on the specified level."""
        template = self.context_templates.get(level, self.context_templates['detailed'])
        
        if level == "overview":
            return await self._generate_overview_context(topic, context_data, template)
        elif level == "detailed":
            return await self._generate_detailed_context(topic, context_data, template)
        elif level == "technical":
            return await self._generate_technical_context(topic, context_data, template)
        elif level == "reference":
            return await self._generate_reference_context(topic, context_data, template)
        else:
            return await self._generate_overview_context(topic, context_data, template)

    async def _generate_overview_context(self, topic: str, context_data: Dict[str, Any], template: ContextTemplate) -> str:
        """Generate overview-level context content."""
        content = f"""# {topic.title()} - Overview Context

## Summary
{context_data.get('overview', 'Overview not available')}

## Key Concepts
{chr(10).join(f"- **{concept}**" for concept in context_data.get('key_concepts', [])[:5])}

## Quick References
{chr(10).join(f"- `{Path(ref).name}`" for ref in context_data.get('related_files', [])[:5])}

## Dependencies
{chr(10).join(f"- {dep}" for dep in context_data.get('dependencies', [])[:3])}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_detailed_context(self, topic: str, context_data: Dict[str, Any], template: ContextTemplate) -> str:
        """Generate detailed-level context content."""
        content = f"""# {topic.title()} - Detailed Context

## Introduction
{context_data.get('overview', 'No overview available')}

## Core Concepts
{chr(10).join(f"### {concept.title()}" + chr(10) + f"Key concept in {topic}" for concept in context_data.get('key_concepts', [])[:8])}

## Implementation Details
Based on analysis of related files:
{chr(10).join(f"- **{Path(file).name}**: {Path(file).suffix[1:].upper()} file" for file in context_data.get('related_files', [])[:10])}

## Examples
{await self._format_examples(context_data.get('examples', []))}

## Dependencies
{chr(10).join(f"- **{dep}**: Required dependency" for dep in context_data.get('dependencies', []))}

## Best Practices
- Follow established patterns in existing code
- Maintain consistency with project structure
- Document significant changes
- Test thoroughly before deployment

## Related Files
{chr(10).join(f"- `{file}`" for file in context_data.get('related_files', []))}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated detailed context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_technical_context(self, topic: str, context_data: Dict[str, Any], template: ContextTemplate) -> str:
        """Generate technical-level context content."""
        content = f"""# {topic.title()} - Technical Context

## Architecture Overview
Technical implementation details for {topic}.

{context_data.get('overview', '')}

## API Reference
{await self._format_api_references(context_data)}

## Code Examples
{await self._format_examples(context_data.get('examples', []), detailed=True)}

## Configuration
Dependencies and configuration requirements:
{chr(10).join(f"- **{dep}**" for dep in context_data.get('dependencies', []))}

## File Structure
{await self._format_file_structure(context_data.get('related_files', []))}


## Technical Specifications
- **Topic**: {topic}
- **Files**: {len(context_data.get('related_files', []))} related files
- **Concepts**: {len(context_data.get('key_concepts', []))} key concepts
- **Examples**: {len(context_data.get('examples', []))} code examples

## Troubleshooting
Common issues and solutions related to {topic}:
- Check file paths and imports
- Verify dependency versions
- Review configuration settings
- Ensure proper error handling

## Related Files
{chr(10).join(f"- `{file}`" for file in context_data.get('related_files', []))}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated technical context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_reference_context(self, topic: str, context_data: Dict[str, Any], template: ContextTemplate) -> str:
        """Generate reference-level context content."""
        content = f"""# {topic.title()} - Reference Context

## API Documentation
Quick reference for {topic} related functionality.

## Key Functions and Methods
{await self._format_api_references(context_data)}

## Parameters and Configuration
{chr(10).join(f"- **{concept}**: Key parameter or configuration option" for concept in context_data.get('key_concepts', []))}

## Return Values and Types
Refer to specific implementation files for detailed type information.

## Code Examples
{await self._format_examples(context_data.get('examples', []), reference=True)}

## Dependencies
{chr(10).join(f"- `{dep}`" for dep in context_data.get('dependencies', []))}

## Source Files
{chr(10).join(f"- `{file}`" for file in context_data.get('related_files', []))}

---
*This is an auto-generated reference context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _format_examples(self, examples: List[Dict[str, str]], detailed: bool = False, reference: bool = False) -> str:
        """Format examples for context files."""
        if not examples:
            return "No examples available."
        
        formatted = []
        for i, example in enumerate(examples[:3 if reference else 5]):
            if example.get('type') == 'code':
                formatted.append(f"""### Example {i+1}: {example.get('source', 'Unknown')}
