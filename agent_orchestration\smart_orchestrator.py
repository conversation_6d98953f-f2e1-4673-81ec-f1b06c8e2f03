#!/usr/bin/env python3
"""
Smart Agent Orchestration System
================================

Advanced orchestration system with dynamic agent allocation, load balancing,
intelligent task routing, and adaptive resource management.

Features:
- Dynamic agent allocation based on workload
- Intelligent task prioritization and routing
- Load balancing across agent instances
- Resource-aware task scheduling
- Cross-agent collaboration workflows
- Adaptive performance optimization

Author: System Enhancement Module
Version: 2.0
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict, field
from typing import Dict, List, Optional, Any, Callable, Tuple
from pathlib import Path
import yaml
import uuid
from enum import Enum
from collections import deque, defaultdict
import heapq
import threading
from concurrent.futures import ThreadPoolExecutor, Future
import subprocess
import statistics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/smart_orchestrator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class AgentState(Enum):
    """Agent state enumeration"""
    IDLE = "idle"
    BUSY = "busy"
    OVERLOADED = "overloaded"
    OFFLINE = "offline"
    MAINTENANCE = "maintenance"

@dataclass
class Task:
    """Task definition with metadata"""
    task_id: str
    agent_id: str
    task_name: str
    parameters: Dict[str, Any]
    priority: TaskPriority
    created_at: datetime
    deadline: Optional[datetime] = None
    estimated_duration: int = 300  # seconds
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    status: TaskStatus = TaskStatus.PENDING
    assigned_agent_instance: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    
    def __lt__(self, other):
        """For priority queue ordering"""
        if self.deadline and other.deadline:
            return (self.priority.value, self.deadline) > (other.priority.value, other.deadline)
        return self.priority.value > other.priority.value

@dataclass
class AgentInstance:
    """Agent instance with performance tracking"""
    instance_id: str
    agent_id: str
    state: AgentState
    current_task: Optional[str] = None
    load_score: float = 0.0
    performance_score: float = 100.0
    last_activity: datetime = field(default_factory=datetime.now)
    total_tasks_completed: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    error_rate: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)
    capabilities: List[str] = field(default_factory=list)
    max_concurrent_tasks: int = 1
    current_tasks: List[str] = field(default_factory=list)

@dataclass
class WorkloadMetrics:
    """System workload metrics"""
    total_pending_tasks: int
    total_running_tasks: int
    average_queue_time: float
    average_execution_time: float
    system_utilization: float
    agent_distribution: Dict[str, int]
    bottleneck_agents: List[str]
    predicted_completion_time: Optional[datetime]

class IntelligentTaskRouter:
    """Intelligent task routing with ML-like capabilities"""
    
    def __init__(self):
        self.routing_history = deque(maxlen=1000)
        self.agent_performance_history = defaultdict(list)
        self.task_type_patterns = defaultdict(list)
        
    def route_task(self, task: Task, available_agents: List[AgentInstance]) -> Optional[AgentInstance]:
        """Route task to the best available agent"""
        if not available_agents:
            return None
            
        # Filter agents that can handle this task
        capable_agents = [
            agent for agent in available_agents
            if self._can_handle_task(agent, task)
        ]
        
        if not capable_agents:
            return None
            
        # Score each agent for this specific task
        agent_scores = []
        for agent in capable_agents:
            score = self._calculate_agent_score(agent, task)
            agent_scores.append((score, agent))
            
        # Sort by score (highest first)
        agent_scores.sort(reverse=True)
        
        # Select the best agent
        best_agent = agent_scores[0][1]
        
        # Record routing decision for learning
        self._record_routing_decision(task, best_agent, agent_scores[0][0])
        
        return best_agent
        
    def _can_handle_task(self, agent: AgentInstance, task: Task) -> bool:
        """Check if agent can handle the task"""
        # Check if agent is available
        if agent.state not in [AgentState.IDLE, AgentState.BUSY]:
            return False
            
        # Check concurrent task limit
        if len(agent.current_tasks) >= agent.max_concurrent_tasks:
            return False
            
        # Check agent capabilities
        if task.agent_id != agent.agent_id:
            return False
            
        # Check resource requirements
        required_resources = task.resource_requirements
        for resource, requirement in required_resources.items():
            if resource in agent.resource_usage:
                available = 100 - agent.resource_usage[resource]
                if available < requirement:
                    return False
                    
        return True
        
    def _calculate_agent_score(self, agent: AgentInstance, task: Task) -> float:
        """Calculate suitability score for agent-task pairing"""
        score = 0.0
        
        # Base performance score (0-100)
        score += agent.performance_score * 0.3
        
        # Load balancing factor (prefer less loaded agents)
        load_factor = max(0, 100 - agent.load_score)
        score += load_factor * 0.2
        
        # Experience factor (agents with experience in similar tasks)
        experience_score = self._get_experience_score(agent, task)
        score += experience_score * 0.2
        
        # Resource efficiency factor
        resource_efficiency = self._calculate_resource_efficiency(agent, task)
        score += resource_efficiency * 0.15
        
        # Deadline pressure factor
        deadline_factor = self._calculate_deadline_factor(agent, task)
        score += deadline_factor * 0.1
        
        # Error rate penalty
        error_penalty = agent.error_rate * 50  # Convert to 0-50 range
        score -= error_penalty * 0.05
        
        return max(0, score)
        
    def _get_experience_score(self, agent: AgentInstance, task: Task) -> float:
        """Get experience score based on similar task history"""
        similar_tasks = [
            perf for perf in self.agent_performance_history[agent.instance_id]
            if perf.get('task_name') == task.task_name
        ]
        
        if not similar_tasks:
            return 50.0  # Neutral score for no experience
            
        # Calculate average performance on similar tasks
        avg_performance = statistics.mean([t.get('success', 0) * 100 for t in similar_tasks])
        return min(100, avg_performance)
        
    def _calculate_resource_efficiency(self, agent: AgentInstance, task: Task) -> float:
        """Calculate resource efficiency score"""
        if not task.resource_requirements:
            return 100.0
            
        efficiency_scores = []
        for resource, requirement in task.resource_requirements.items():
            current_usage = agent.resource_usage.get(resource, 0)
            available = 100 - current_usage
            
            if available >= requirement:
                # Higher score for agents with more available resources
                efficiency = (available - requirement) / available * 100
                efficiency_scores.append(efficiency)
            else:
                efficiency_scores.append(0)
                
        return statistics.mean(efficiency_scores) if efficiency_scores else 100.0
        
    def _calculate_deadline_factor(self, agent: AgentInstance, task: Task) -> float:
        """Calculate deadline urgency factor"""
        if not task.deadline:
            return 50.0  # Neutral score
            
        time_remaining = (task.deadline - datetime.now()).total_seconds()
        estimated_time = task.estimated_duration
        
        if time_remaining <= 0:
            return 0.0  # Past deadline
        elif time_remaining < estimated_time:
            return 25.0  # Tight deadline
        elif time_remaining < estimated_time * 2:
            return 75.0  # Moderate deadline pressure
        else:
            return 100.0  # Comfortable deadline
            
    def _record_routing_decision(self, task: Task, agent: AgentInstance, score: float):
        """Record routing decision for future learning"""
        decision = {
            'task_id': task.task_id,
            'agent_instance': agent.instance_id,
            'agent_id': agent.agent_id,
            'task_name': task.task_name,
            'score': score,
            'timestamp': datetime.now(),
            'task_priority': task.priority.value
        }
        self.routing_history.append(decision)
        
    def update_performance_feedback(self, task: Task, success: bool, execution_time: float):
        """Update performance feedback for learning"""
        # Find the routing decision
        for decision in reversed(self.routing_history):
            if decision['task_id'] == task.task_id:
                agent_instance = decision['agent_instance']
                
                performance_record = {
                    'task_id': task.task_id,
                    'task_name': task.task_name,
                    'success': success,
                    'execution_time': execution_time,
                    'estimated_time': task.estimated_duration,
                    'timestamp': datetime.now()
                }
                
                self.agent_performance_history[agent_instance].append(performance_record)
                break

class ResourceManager:
    """Manages system resources and agent scaling"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.resource_pools = config.get('global', {}).get('performance', {}).get('resource_pools', {})
        self.scaling_thresholds = {
            'cpu_threshold': 80.0,
            'memory_threshold': 85.0,
            'queue_length_threshold': 10,
            'response_time_threshold': 5000  # ms
        }
        
    def should_scale_up(self, workload_metrics: WorkloadMetrics, agent_instances: Dict[str, List[AgentInstance]]) -> List[str]:
        """Determine which agent types should be scaled up"""
        scale_up_agents = []
        
        # Check overall system utilization
        if workload_metrics.system_utilization > self.scaling_thresholds['cpu_threshold']:
            # Find bottleneck agents
            for agent_id in workload_metrics.bottleneck_agents:
                if self._can_scale_up(agent_id, agent_instances):
                    scale_up_agents.append(agent_id)
                    
        # Check queue length
        if workload_metrics.total_pending_tasks > self.scaling_thresholds['queue_length_threshold']:
            # Scale up agents with the most queued tasks
            for agent_id, count in workload_metrics.agent_distribution.items():
                if count > 3 and self._can_scale_up(agent_id, agent_instances):
                    scale_up_agents.append(agent_id)
                    
        return list(set(scale_up_agents))  # Remove duplicates
        
    def should_scale_down(self, workload_metrics: WorkloadMetrics, agent_instances: Dict[str, List[AgentInstance]]) -> List[str]:
        """Determine which agent instances should be scaled down"""
        scale_down_instances = []
        
        # Check for underutilized agents
        if workload_metrics.system_utilization < 30.0:  # Low utilization threshold
            for agent_id, instances in agent_instances.items():
                if len(instances) > 1:  # Don't scale down to zero
                    # Find the least utilized instance
                    idle_instances = [
                        inst for inst in instances 
                        if inst.state == AgentState.IDLE and not inst.current_tasks
                    ]
                    
                    if idle_instances:
                        # Sort by performance score (remove worst performing)
                        idle_instances.sort(key=lambda x: x.performance_score)
                        scale_down_instances.append(idle_instances[0].instance_id)
                        
        return scale_down_instances
        
    def _can_scale_up(self, agent_id: str, agent_instances: Dict[str, List[AgentInstance]]) -> bool:
        """Check if agent can be scaled up"""
        current_instances = len(agent_instances.get(agent_id, []))
        max_instances = self.config.get('agents', {}).get(agent_id, {}).get('max_instances', 3)
        
        return current_instances < max_instances
        
    def allocate_resources(self, task: Task, agent: AgentInstance) -> bool:
        """Allocate resources for a task"""
        required_resources = task.resource_requirements
        
        # Check if resources are available
        for resource, requirement in required_resources.items():
            current_usage = agent.resource_usage.get(resource, 0)
            if current_usage + requirement > 100:
                return False
                
        # Allocate resources
        for resource, requirement in required_resources.items():
            agent.resource_usage[resource] = agent.resource_usage.get(resource, 0) + requirement
            
        return True
        
    def release_resources(self, task: Task, agent: AgentInstance):
        """Release resources after task completion"""
        required_resources = task.resource_requirements
        
        for resource, requirement in required_resources.items():
            current_usage = agent.resource_usage.get(resource, 0)
            agent.resource_usage[resource] = max(0, current_usage - requirement)

class SmartOrchestrator:
    """Main smart orchestration system"""
    
    def __init__(self, config_path: str = "agent_orchestration/agent_config_enhanced.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        
        # Core components
        self.task_queue = []  # Priority queue
        self.task_registry: Dict[str, Task] = {}
        self.agent_instances: Dict[str, List[AgentInstance]] = defaultdict(list)
        self.task_router = IntelligentTaskRouter()
        self.resource_manager = ResourceManager(self.config)
        
        # Performance tracking
        self.performance_metrics = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0.0,
            'average_queue_time': 0.0,
            'system_utilization': 0.0
        }
        
        # Threading
        self.executor = ThreadPoolExecutor(max_workers=self.config.get('global', {}).get('performance', {}).get('max_concurrent_agents', 4))
        self.orchestration_active = False
        
        # Initialize agent instances
        self._initialize_agent_instances()
        
    def _load_config(self) -> Dict:
        """Load orchestration configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}
            
    def _initialize_agent_instances(self):
        """Initialize agent instances based on configuration"""
        agents_config = self.config.get('agents', {})
        
        for agent_id, agent_config in agents_config.items():
            # Create initial instances
            initial_instances = agent_config.get('initial_instances', 1)
            
            for i in range(initial_instances):
                instance = AgentInstance(
                    instance_id=f"{agent_id}_{uuid.uuid4().hex[:8]}",
                    agent_id=agent_id,
                    state=AgentState.IDLE,
                    capabilities=agent_config.get('capabilities', []),
                    max_concurrent_tasks=agent_config.get('max_concurrent_tasks', 1)
                )
                
                self.agent_instances[agent_id].append(instance)
                
        logger.info(f"Initialized {sum(len(instances) for instances in self.agent_instances.values())} agent instances")
        
    def start_orchestration(self):
        """Start the orchestration system"""
        self.orchestration_active = True
        logger.info("Smart orchestration system started")
        
        # Start orchestration loop in background thread
        threading.Thread(target=self._orchestration_loop, daemon=True).start()
        
        # Start resource monitoring
        threading.Thread(target=self._resource_monitoring_loop, daemon=True).start()
        
    def stop_orchestration(self):
        """Stop the orchestration system"""
        self.orchestration_active = False
        self.executor.shutdown(wait=True)
        logger.info("Smart orchestration system stopped")
        
    def submit_task(self, task: Task) -> str:
        """Submit a task for execution"""
        task.task_id = task.task_id or str(uuid.uuid4())
        task.created_at = datetime.now()
        task.status = TaskStatus.QUEUED
        
        # Add to registry and queue
        self.task_registry[task.task_id] = task
        heapq.heappush(self.task_queue, task)
        
        logger.info(f"Task {task.task_id} submitted: {task.task_name} (priority: {task.priority.name})")
        return task.task_id
        
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """Get current status of a task"""
        task = self.task_registry.get(task_id)
        return task.status if task else None
        
    def get_task_result(self, task_id: str) -> Optional[Any]:
        """Get result of a completed task"""
        task = self.task_registry.get(task_id)
        return task.result if task and task.status == TaskStatus.COMPLETED else None
        
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task"""
        task = self.task_registry.get(task_id)
        if not task:
            return False
            
        if task.status in [TaskStatus.PENDING, TaskStatus.QUEUED]:
            task.status = TaskStatus.CANCELLED
            # Remove from queue (complex operation for heapq)
            self.task_queue = [t for t in self.task_queue if t.task_id != task_id]
            heapq.heapify(self.task_queue)
            return True
        elif task.status == TaskStatus.RUNNING:
            # Signal cancellation to running task
            task.status = TaskStatus.CANCELLED
            return True
            
        return False
        
    def get_workload_metrics(self) -> WorkloadMetrics:
        """Get current workload metrics"""
        pending_tasks = len([t for t in self.task_registry.values() if t.status == TaskStatus.QUEUED])
        running_tasks = len([t for t in self.task_registry.values() if t.status == TaskStatus.RUNNING])
        
        # Calculate averages
        completed_tasks = [t for t in self.task_registry.values() if t.status == TaskStatus.COMPLETED]
        
        avg_queue_time = 0.0
        avg_execution_time = 0.0
        
        if completed_tasks:
            queue_times = [
                (t.started_at - t.created_at).total_seconds() 
                for t in completed_tasks 
                if t.started_at
            ]
            execution_times = [
                (t.completed_at - t.started_at).total_seconds() 
                for t in completed_tasks 
                if t.started_at and t.completed_at
            ]
            
            avg_queue_time = statistics.mean(queue_times) if queue_times else 0.0
            avg_execution_time = statistics.mean(execution_times) if execution_times else 0.0
            
        # System utilization
        total_instances = sum(len(instances) for instances in self.agent_instances.values())
        busy_instances = sum(
            len([i for i in instances if i.state == AgentState.BUSY]) 
            for instances in self.agent_instances.values()
        )
        system_utilization = (busy_instances / total_instances * 100) if total_instances > 0 else 0.0
        
        # Agent distribution
        agent_distribution = {}
        for agent_id in self.agent_instances.keys():
            agent_tasks = len([
                t for t in self.task_registry.values() 
                if t.agent_id == agent_id and t.status in [TaskStatus.QUEUED, TaskStatus.RUNNING]
            ])
            agent_distribution[agent_id] = agent_tasks
            
        # Bottleneck agents (agents with high queue)
        bottleneck_agents = [
            agent_id for agent_id, count in agent_distribution.items() 
            if count > 5
        ]
        
        return WorkloadMetrics(
            total_pending_tasks=pending_tasks,
            total_running_tasks=running_tasks,
            average_queue_time=avg_queue_time,
            average_execution_time=avg_execution_time,
            system_utilization=system_utilization,
            agent_distribution=agent_distribution,
            bottleneck_agents=bottleneck_agents,
            predicted_completion_time=self._predict_completion_time()
        )
        
    def _orchestration_loop(self):
        """Main orchestration loop"""
        while self.orchestration_active:
            try:
                self._process_task_queue()
                self._update_agent_states()
                self._handle_completed_tasks()
                time.sleep(1)  # Process every second
            except Exception as e:
                logger.error(f"Error in orchestration loop: {e}")
                time.sleep(5)  # Wait before retrying
                
    def _process_task_queue(self):
        """Process pending tasks in the queue"""
        while self.task_queue and self.orchestration_active:
            # Get highest priority task
            task = heapq.heappop(self.task_queue)
            
            if task.status == TaskStatus.CANCELLED:
                continue
                
            # Find available agents for this task
            available_agents = self._get_available_agents(task.agent_id)
            
            if not available_agents:
                # No agents available, put task back in queue
                heapq.heappush(self.task_queue, task)
                break
                
            # Route task to best agent
            selected_agent = self.task_router.route_task(task, available_agents)
            
            if not selected_agent:
                # No suitable agent found
                heapq.heappush(self.task_queue, task)
                break
                
            # Allocate resources
            if not self.resource_manager.allocate_resources(task, selected_agent):
                # Resources not available
                heapq.heappush(self.task_queue, task)
                break
                
            # Execute task
            self._execute_task(task, selected_agent)
            
    def _get_available_agents(self, agent_id: str) -> List[AgentInstance]:
        """Get available agents of a specific type"""
        instances = self.agent_instances.get(agent_id, [])
        return [
            instance for instance in instances
            if instance.state in [AgentState.IDLE, AgentState.BUSY] and
            len(instance.current_tasks) < instance.max_concurrent_tasks
        ]
        
    def _execute_task(self, task: Task, agent: AgentInstance):
        """Execute a task on an agent"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        task.assigned_agent_instance = agent.instance_id
        
        # Update agent state
        agent.current_tasks.append(task.task_id)
        agent.state = AgentState.BUSY
        agent.current_task = task.task_id
        agent.last_activity = datetime.now()
        
        # Submit task for execution
        future = self.executor.submit(self._run_task, task, agent)
        task.execution_future = future
        
        logger.info(f"Task {task.task_id} started on agent {agent.instance_id}")
        
    def _run_task(self, task: Task, agent: AgentInstance) -> Any:
        """Actually run the task (this would call the agent script)"""
        try:
            start_time = time.time()
            
            # Build command to execute agent script
            script_path = self.config.get('agents', {}).get(task.agent_id, {}).get('entrypoint', '')
            if not script_path:
                raise ValueError(f"No entrypoint configured for agent {task.agent_id}")
                
            # Prepare command arguments
            cmd = ['python', script_path, task.task_name]
            
            # Add parameters as JSON
            if task.parameters:
                cmd.extend(['--params', json.dumps(task.parameters)])
                
            # Execute the command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=task.estimated_duration + 60,  # Add buffer time
                cwd=Path.cwd()
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                # Task completed successfully
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = {
                    'stdout': result.stdout,
                    'execution_time': execution_time,
                    'agent_instance': agent.instance_id
                }
                
                # Update agent performance
                agent.total_tasks_completed += 1
                agent.total_execution_time += execution_time
                agent.average_execution_time = agent.total_execution_time / agent.total_tasks_completed
                
                # Update performance feedback
                self.task_router.update_performance_feedback(task, True, execution_time)
                
                logger.info(f"Task {task.task_id} completed successfully in {execution_time:.2f}s")
                
            else:
                # Task failed
                raise Exception(f"Agent script failed: {result.stderr}")
                
        except Exception as e:
            # Task failed
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            
            # Update agent error rate
            agent.total_tasks_completed += 1  # Count as attempt
            current_errors = agent.error_rate * (agent.total_tasks_completed - 1)
            agent.error_rate = (current_errors + 1) / agent.total_tasks_completed
            
            # Update performance feedback
            execution_time = time.time() - start_time if 'start_time' in locals() else 0
            self.task_router.update_performance_feedback(task, False, execution_time)
            
            logger.error(f"Task {task.task_id} failed: {e}")
            
            # Handle retries
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                
                # Delay before retry
                retry_delay = self.config.get('global', {}).get('error_handling', {}).get('retry_delay', 5)
                time.sleep(retry_delay * (2 ** task.retry_count))  # Exponential backoff
                
                # Re-queue task
                heapq.heappush(self.task_queue, task)
                logger.info(f"Task {task.task_id} queued for retry {task.retry_count}/{task.max_retries}")
                
        finally:
            # Clean up agent state
            if task.task_id in agent.current_tasks:
                agent.current_tasks.remove(task.task_id)
                
            if not agent.current_tasks:
                agent.state = AgentState.IDLE
                agent.current_task = None
                
            # Release resources
            self.resource_manager.release_resources(task, agent)
            
            # Update performance metrics
            self._update_performance_metrics(task)
            
    def _update_agent_states(self):
        """Update agent states based on current conditions"""
        for agent_id, instances in self.agent_instances.items():
            for agent in instances:
                # Update load score based on current tasks
                max_tasks = agent.max_concurrent_tasks
                current_load = len(agent.current_tasks)
                agent.load_score = (current_load / max_tasks) * 100
                
                # Update performance score based on recent performance
                if agent.total_tasks_completed > 0:
                    success_rate = 1.0 - agent.error_rate
                    efficiency_factor = min(1.0, agent.estimated_duration / max(agent.average_execution_time, 1))
                    agent.performance_score = (success_rate * 0.7 + efficiency_factor * 0.3) * 100
                    
    def _handle_completed_tasks(self):
        """Handle completed tasks and cleanup"""
        completed_tasks = [
            task for task in self.task_registry.values()
            if hasattr(task, 'execution_future') and task.execution_future.done()
        ]
        
        for task in completed_tasks:
            try:
                # Get result from future
                task.execution_future.result()
            except Exception as e:
                # Already handled in _run_task
                pass
            finally:
                # Clean up future reference
                delattr(task, 'execution_future')
                
    def _update_performance_metrics(self, task: Task):
        """Update system performance metrics"""
        if task.status == TaskStatus.COMPLETED:
            self.performance_metrics['tasks_completed'] += 1
            if task.started_at and task.completed_at:
                execution_time = (task.completed_at - task.started_at).total_seconds()
                self.performance_metrics['total_execution_time'] += execution_time
        elif task.status == TaskStatus.FAILED:
            self.performance_metrics['tasks_failed'] += 1
            
    def _predict_completion_time(self) -> Optional[datetime]:
        """Predict when all current tasks will be completed"""
        pending_tasks = [t for t in self.task_registry.values() if t.status in [TaskStatus.QUEUED, TaskStatus.RUNNING]]
        
        if not pending_tasks:
            return datetime.now()
            
        # Simple prediction based on average execution time and available agents
        total_estimated_time = sum(t.estimated_duration for t in pending_tasks)
        total_available_agents = sum(len(instances) for instances in self.agent_instances.values())
        
        if total_available_agents > 0:
            predicted_seconds = total_estimated_time / total_available_agents
            return datetime.now() + timedelta(seconds=predicted_seconds)
            
        return None
        
    def _resource_monitoring_loop(self):
        """Monitor resources and handle scaling"""
        while self.orchestration_active:
            try:
                workload_metrics = self.get_workload_metrics()
                
                # Check for scaling up
                scale_up_agents = self.resource_manager.should_scale_up(workload_metrics, self.agent_instances)
                for agent_id in scale_up_agents:
                    self._scale_up_agent(agent_id)
                    
                # Check for scaling down
                scale_down_instances = self.resource_manager.should_scale_down(workload_metrics, self.agent_instances)
                for instance_id in scale_down_instances:
                    self._scale_down_instance(instance_id)
                    
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                time.sleep(60)
                
    def _scale_up_agent(self, agent_id: str):
        """Scale up an agent type"""
        try:
            agent_config = self.config.get('agents', {}).get(agent_id, {})
            
            instance = AgentInstance(
                instance_id=f"{agent_id}_{uuid.uuid4().hex[:8]}",
                agent_id=agent_id,
                state=AgentState.IDLE,
                capabilities=agent_config.get('capabilities', []),
                max_concurrent_tasks=agent_config.get('max_concurrent_tasks', 1)
            )
            
            self.agent_instances[agent_id].append(instance)
            logger.info(f"Scaled up agent {agent_id}, new instance: {instance.instance_id}")
            
        except Exception as e:
            logger.error(f"Failed to scale up agent {agent_id}: {e}")
            
    def _scale_down_instance(self, instance_id: str):
        """Scale down a specific agent instance"""
        try:
            for agent_id, instances in self.agent_instances.items():
                for i, instance in enumerate(instances):
                    if instance.instance_id == instance_id:
                        if instance.state == AgentState.IDLE and not instance.current_tasks:
                            instances.pop(i)
                            logger.info(f"Scaled down agent instance: {instance_id}")
                            return
                            
        except Exception as e:
            logger.error(f"Failed to scale down instance {instance_id}: {e}")

def main():
    """Main entry point for smart orchestrator"""
    orchestrator = SmartOrchestrator()
    orchestrator.start_orchestration()
    
    try:
        # Example: Submit some test tasks
        test_task = Task(
            task_id=None,
            agent_id='dependency-orchestrator',
            task_name='environment_audit',
            parameters={'include_security': True},
            priority=TaskPriority.NORMAL,
            created_at=datetime.now(),
            estimated_duration=120
        )
        
        task_id = orchestrator.submit_task(test_task)
        logger.info(f"Submitted test task: {task_id}")
        
        # Keep orchestrator running
        while True:
            time.sleep(60)
            
            # Print periodic status
            metrics = orchestrator.get_workload_metrics()
            logger.info(f"System Status: {metrics.total_pending_tasks} pending, "
                       f"{metrics.total_running_tasks} running, "
                       f"{metrics.system_utilization:.1f}% utilization")
            
    except KeyboardInterrupt:
        logger.info("Shutting down orchestrator...")
        orchestrator.stop_orchestration()

if __name__ == "__main__":
    main()
