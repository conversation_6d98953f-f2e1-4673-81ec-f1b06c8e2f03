#!/usr/bin/env python3
"""
UX Quality Assurance Agent
Advanced user experience testing and quality assessment specialist

This agent handles:
1. Frontend usability testing and analysis (Desktop-focused)
2. Performance monitoring and optimization
3. Accessibility compliance checking
4. Cross-browser compatibility testing (Desktop browsers only)
5. User journey analysis and optimization

Note: Configured for desktop-only personal PC project - mobile testing disabled.
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re
import time

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class UXQualityAssuranceAgent(BaseAgent):
    """
    UX Quality Assurance Agent implementation.
    
    Provides comprehensive user experience testing and quality assessment.
    Specializes in frontend performance, accessibility, and usability analysis.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Frontend paths
        self.frontend_path = self.project_root / "frontend"
        self.components_path = self.frontend_path / "src" / "components"
        self.pages_path = self.frontend_path / "src" / "pages"
        self.styles_path = self.frontend_path / "src" / "styles"
        
        # Testing and quality standards
        self.accessibility_standards = ['WCAG-AA', 'WCAG-AAA']
        self.performance_thresholds = {
            "first_contentful_paint": 1.8,  # seconds
            "largest_contentful_paint": 2.5,
            "cumulative_layout_shift": 0.1,
            "first_input_delay": 100  # milliseconds
        }
        
        # Browser compatibility targets (Desktop only - Personal PC project)
        self.browser_targets = [
            "Chrome >= 90", "Firefox >= 88", "Safari >= 14", 
            "Edge >= 90"
        ]
        
        # Project configuration - Personal PC project (no mobile support required)
        self.project_type = "desktop_only"
        self.skip_mobile_testing = True
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific UX quality assurance task."""
        task_name = self.task_name
        
        if task_name == "audit_accessibility":
            return await self.audit_accessibility()
        elif task_name == "performance_analysis":
            return await self.performance_analysis()
        elif task_name == "usability_testing":
            return await self.usability_testing()
        elif task_name == "cross_browser_testing":
            return await self.cross_browser_testing()
        elif task_name == "responsive_design_check":
            if self.skip_mobile_testing:
                self.logger.info("📱 Skipping mobile responsive design check (Desktop-only project)")
                return {"skipped": True, "reason": "Desktop-only personal PC project"}
            return await self.responsive_design_check()
        elif task_name == "seo_optimization":
            return await self.seo_optimization()
        else:
            return await self.comprehensive_ux_audit()
    
    async def audit_accessibility(self) -> Dict[str, Any]:
        """Audit frontend for accessibility compliance."""
        self.logger.info("♿ Auditing accessibility compliance...")
        
        accessibility_results = {
            "wcag_compliance": {},
            "accessibility_issues": [],
            "color_contrast_issues": [],
            "keyboard_navigation_issues": [],
            "screen_reader_issues": [],
            "recommendations": [],
            "compliance_score": 0.0
        }
        
        # Analyze HTML structure for accessibility
        if self.pages_path.exists() or self.components_path.exists():
            html_analysis = await self._analyze_html_accessibility()
            accessibility_results.update(html_analysis)
        
        # Analyze CSS for accessibility
        if self.styles_path.exists():
            css_analysis = await self._analyze_css_accessibility()
            accessibility_results["css_accessibility"] = css_analysis
        
        # Calculate compliance score
        compliance_score = await self._calculate_accessibility_score(accessibility_results)
        accessibility_results["compliance_score"] = compliance_score
        
        # Generate recommendations
        recommendations = await self._generate_accessibility_recommendations(accessibility_results)
        accessibility_results["recommendations"] = recommendations
        
        return {
            "success": True,
            "accessibility_audit": accessibility_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _analyze_html_accessibility(self) -> Dict[str, Any]:
        """Analyze HTML files for accessibility issues."""
        html_analysis = {
            "missing_alt_text": [],
            "missing_labels": [],
            "improper_headings": [],
            "missing_landmarks": [],
            "keyboard_issues": []
        }
        
        # Search for React/Next.js component files
        component_files = []
        if self.components_path.exists():
            component_files.extend(list(self.components_path.glob("**/*.tsx")))
            component_files.extend(list(self.components_path.glob("**/*.jsx")))
        
        if self.pages_path.exists():
            component_files.extend(list(self.pages_path.glob("**/*.tsx")))
            component_files.extend(list(self.pages_path.glob("**/*.jsx")))
        
        for comp_file in component_files:
            try:
                content = comp_file.read_text(encoding='utf-8')
                
                # Check for images without alt text
                img_tags = re.findall(r'<img[^>]*>', content)
                for img_tag in img_tags:
                    if 'alt=' not in img_tag:
                        html_analysis["missing_alt_text"].append({
                            "file": str(comp_file.relative_to(self.project_root)),
                            "line": content[:content.find(img_tag)].count('\n') + 1
                        })
                
                # Check for form inputs without labels
                input_patterns = [
                    r'<input[^>]*>',
                    r'<textarea[^>]*>',
                    r'<select[^>]*>'
                ]
                for pattern in input_patterns:
                    inputs = re.findall(pattern, content)
                    for input_tag in inputs:
                        if 'aria-label=' not in input_tag and 'id=' not in input_tag:
                            html_analysis["missing_labels"].append({
                                "file": str(comp_file.relative_to(self.project_root)),
                                "element": input_tag[:50] + "..."
                            })
                
                # Check heading structure
                headings = re.findall(r'<h([1-6])[^>]*>', content)
                if headings:
                    heading_levels = [int(h) for h in headings]
                    # Simple check for skipped heading levels
                    for i, level in enumerate(heading_levels[1:], 1):
                        if level > heading_levels[i-1] + 1:
                            html_analysis["improper_headings"].append({
                                "file": str(comp_file.relative_to(self.project_root)),
                                "issue": f"Heading level skip from h{heading_levels[i-1]} to h{level}"
                            })
                
                # Check for semantic landmarks
                landmarks = ['nav', 'main', 'aside', 'header', 'footer', 'section']
                found_landmarks = []
                for landmark in landmarks:
                    if f'<{landmark}' in content or f'role="{landmark}"' in content:
                        found_landmarks.append(landmark)
                
                if len(found_landmarks) < 2 and len(content) > 1000:  # Large components should have landmarks
                    html_analysis["missing_landmarks"].append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "found_landmarks": found_landmarks
                    })
                
            except Exception as e:
                self.logger.warning(f"Error analyzing accessibility in {comp_file}: {e}")
        
        return html_analysis
    
    async def _analyze_css_accessibility(self) -> Dict[str, Any]:
        """Analyze CSS files for accessibility issues."""
        css_analysis = {
            "color_contrast_warnings": [],
            "font_size_issues": [],
            "focus_indicators": [],
            "responsive_issues": []
        }
        
        try:
            # Find CSS/SCSS files
            css_files = []
            if self.styles_path.exists():
                css_files.extend(list(self.styles_path.glob("**/*.css")))
                css_files.extend(list(self.styles_path.glob("**/*.scss")))
                css_files.extend(list(self.styles_path.glob("**/*.sass")))
            
            for css_file in css_files:
                try:
                    content = css_file.read_text(encoding='utf-8')
                    
                    # Check for very small font sizes
                    font_sizes = re.findall(r'font-size:\s*(\d+(?:\.\d+)?)(px|em|rem)', content)
                    for size, unit in font_sizes:
                        size_val = float(size)
                        if unit == 'px' and size_val < 14:
                            css_analysis["font_size_issues"].append({
                                "file": str(css_file.relative_to(self.project_root)),
                                "size": f"{size}{unit}",
                                "recommendation": "Use at least 14px for body text"
                            })
                        elif unit in ['em', 'rem'] and size_val < 0.875:
                            css_analysis["font_size_issues"].append({
                                "file": str(css_file.relative_to(self.project_root)),
                                "size": f"{size}{unit}",
                                "recommendation": "Use at least 0.875em/rem for body text"
                            })
                    
                    # Check for focus indicators
                    if ':focus' not in content and 'focus-visible' not in content:
                        css_analysis["focus_indicators"].append({
                            "file": str(css_file.relative_to(self.project_root)),
                            "issue": "No focus indicators found"
                        })
                    
                    # Check for potential color contrast issues (basic detection)
                    color_rules = re.findall(r'color:\s*([#\w\(\),\s]+);', content)
                    bg_rules = re.findall(r'background(?:-color)?:\s*([#\w\(\),\s]+);', content)
                    
                    if len(color_rules) > 0 and len(bg_rules) > 0:
                        # This is a simplified check - real implementation would calculate actual contrast ratios
                        css_analysis["color_contrast_warnings"].append({
                            "file": str(css_file.relative_to(self.project_root)),
                            "recommendation": "Verify color contrast ratios meet WCAG standards"
                        })
                
                except Exception as e:
                    self.logger.warning(f"Error analyzing CSS file {css_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing CSS accessibility: {e}")
        
        return css_analysis
    
    async def _calculate_accessibility_score(self, results: Dict[str, Any]) -> float:
        """Calculate overall accessibility compliance score."""
        total_issues = 0
        total_checks = 0
        
        # Count issues from different categories
        categories = [
            "missing_alt_text", "missing_labels", "improper_headings", 
            "missing_landmarks", "keyboard_issues"
        ]
        
        for category in categories:
            issues = results.get(category, [])
            total_issues += len(issues)
            total_checks += max(1, len(issues))  # At least 1 check per category
        
        # Calculate score (higher is better)
        if total_checks > 0:
            score = max(0.0, 1.0 - (total_issues / (total_checks * 2)))  # Normalize
        else:
            score = 1.0
        
        return round(score, 2)
    
    async def _generate_accessibility_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate accessibility improvement recommendations."""
        recommendations = []
        
        # Alt text recommendations
        missing_alt = results.get("missing_alt_text", [])
        if missing_alt:
            recommendations.append({
                "type": "accessibility",
                "priority": "high",
                "title": "Add alt text to images",
                "description": f"Found {len(missing_alt)} images without alt text",
                "implementation": "Add descriptive alt attributes to all images",
                "wcag_criteria": "1.1.1 Non-text Content"
            })
        
        # Label recommendations
        missing_labels = results.get("missing_labels", [])
        if missing_labels:
            recommendations.append({
                "type": "accessibility",
                "priority": "high",
                "title": "Add labels to form controls",
                "description": f"Found {len(missing_labels)} form elements without proper labels",
                "implementation": "Add aria-label or associate with label elements",
                "wcag_criteria": "4.1.2 Name, Role, Value"
            })
        
        # Heading structure recommendations
        improper_headings = results.get("improper_headings", [])
        if improper_headings:
            recommendations.append({
                "type": "accessibility",
                "priority": "medium",
                "title": "Fix heading structure",
                "description": f"Found {len(improper_headings)} heading structure issues",
                "implementation": "Ensure proper heading hierarchy (h1 > h2 > h3...)",
                "wcag_criteria": "2.4.6 Headings and Labels"
            })
        
        # Font size recommendations
        css_analysis = results.get("css_accessibility", {})
        font_issues = css_analysis.get("font_size_issues", [])
        if font_issues:
            recommendations.append({
                "type": "accessibility",
                "priority": "medium",
                "title": "Increase minimum font sizes",
                "description": f"Found {len(font_issues)} instances of small font sizes",
                "implementation": "Use minimum 14px (0.875rem) for body text",
                "wcag_criteria": "1.4.4 Resize text"
            })
        
        return recommendations
    
    async def performance_analysis(self) -> Dict[str, Any]:
        """Analyze frontend performance metrics."""
        self.logger.info("🚀 Analyzing frontend performance...")
        
        performance_results = {
            "core_web_vitals": {},
            "bundle_analysis": {},
            "loading_performance": {},
            "runtime_performance": {},
            "optimization_opportunities": [],
            "performance_score": 0.0
        }
        
        # Analyze bundle sizes if build exists
        build_analysis = await self._analyze_build_performance()
        performance_results["bundle_analysis"] = build_analysis
        
        # Analyze component performance patterns
        component_analysis = await self._analyze_component_performance()
        performance_results["component_performance"] = component_analysis
        
        # Check for performance anti-patterns
        anti_patterns = await self._detect_performance_antipatterns()
        performance_results["anti_patterns"] = anti_patterns
        
        # Generate optimization recommendations
        optimizations = await self._generate_performance_recommendations(performance_results)
        performance_results["optimization_opportunities"] = optimizations
        
        return {
            "success": True,
            "performance_analysis": performance_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _analyze_build_performance(self) -> Dict[str, Any]:
        """Analyze build output for performance insights."""
        build_analysis = {
            "bundle_sizes": {},
            "chunk_analysis": {},
            "asset_optimization": {},
            "compression_opportunities": []
        }
        
        # Look for Next.js build output
        build_path = self.frontend_path / ".next"
        if build_path.exists():
            # Analyze build files
            static_path = build_path / "static"
            if static_path.exists():
                js_files = list(static_path.glob("**/*.js"))
                css_files = list(static_path.glob("**/*.css"))
                
                total_js_size = sum(f.stat().st_size for f in js_files)
                total_css_size = sum(f.stat().st_size for f in css_files)
                
                build_analysis["bundle_sizes"] = {
                    "total_js": total_js_size,
                    "total_css": total_css_size,
                    "js_files_count": len(js_files),
                    "css_files_count": len(css_files)
                }
                
                # Check for large bundles
                large_files = []
                for js_file in js_files:
                    size = js_file.stat().st_size
                    if size > 250 * 1024:  # 250KB threshold
                        large_files.append({
                            "file": js_file.name,
                            "size": size,
                            "size_mb": round(size / (1024 * 1024), 2)
                        })
                
                build_analysis["large_bundles"] = large_files
        
        return build_analysis
    
    async def _analyze_component_performance(self) -> Dict[str, Any]:
        """Analyze component code for performance issues."""
        component_analysis = {
            "heavy_components": [],
            "optimization_opportunities": [],
            "state_management_issues": []
        }
        
        if not self.components_path.exists():
            return component_analysis
        
        component_files = list(self.components_path.glob("**/*.tsx"))
        component_files.extend(list(self.components_path.glob("**/*.jsx")))
        
        for comp_file in component_files:
            try:
                content = comp_file.read_text(encoding='utf-8')
                
                # Check component size (lines of code)
                line_count = len(content.splitlines())
                if line_count > 200:
                    component_analysis["heavy_components"].append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "lines": line_count,
                        "recommendation": "Consider splitting into smaller components"
                    })
                
                # Check for missing React.memo
                if 'export default function' in content or 'export const' in content:
                    if 'React.memo' not in content and 'memo(' not in content:
                        if 'props' in content:
                            component_analysis["optimization_opportunities"].append({
                                "file": str(comp_file.relative_to(self.project_root)),
                                "issue": "Component with props not memoized",
                                "solution": "Wrap with React.memo"
                            })
                
                # Check for multiple useState calls
                usestate_count = content.count('useState')
                if usestate_count > 5:
                    component_analysis["state_management_issues"].append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "issue": f"Multiple useState calls ({usestate_count})",
                        "solution": "Consider useReducer or state consolidation"
                    })
                
            except Exception as e:
                self.logger.warning(f"Error analyzing component {comp_file}: {e}")
        
        return component_analysis
    
    async def _detect_performance_antipatterns(self) -> List[Dict[str, Any]]:
        """Detect common performance anti-patterns."""
        anti_patterns = []
        
        if not self.components_path.exists():
            return anti_patterns
        
        component_files = list(self.components_path.glob("**/*.tsx"))
        component_files.extend(list(self.components_path.glob("**/*.jsx")))
        
        for comp_file in component_files:
            try:
                content = comp_file.read_text(encoding='utf-8')
                
                # Anti-pattern: Inline object creation in JSX
                inline_objects = re.findall(r'\{\s*\{[^}]+\}\s*\}', content)
                if len(inline_objects) > 3:
                    anti_patterns.append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "pattern": "inline_object_creation",
                        "severity": "medium",
                        "description": f"Found {len(inline_objects)} inline object creations",
                        "solution": "Move objects outside render or use useMemo"
                    })
                
                # Anti-pattern: Array index as key
                if 'key={index}' in content or 'key={i}' in content:
                    anti_patterns.append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "pattern": "array_index_key",
                        "severity": "high",
                        "description": "Using array index as React key",
                        "solution": "Use stable, unique identifiers as keys"
                    })
                
                # Anti-pattern: Unnecessary re-renders
                if content.count('useEffect') > 5:
                    anti_patterns.append({
                        "file": str(comp_file.relative_to(self.project_root)),
                        "pattern": "excessive_effects",
                        "severity": "medium",
                        "description": "Many useEffect hooks detected",
                        "solution": "Review effect dependencies and consolidate where possible"
                    })
                
            except Exception as e:
                self.logger.warning(f"Error detecting anti-patterns in {comp_file}: {e}")
        
        return anti_patterns
    
    async def _generate_performance_recommendations(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # Bundle size recommendations
        bundle_analysis = results.get("bundle_analysis", {})
        large_bundles = bundle_analysis.get("large_bundles", [])
        if large_bundles:
            recommendations.append({
                "type": "performance",
                "priority": "high",
                "title": "Optimize large JavaScript bundles",
                "description": f"Found {len(large_bundles)} large bundle files",
                "implementation": "Implement code splitting and tree shaking",
                "impact": "Reduce initial page load time"
            })
        
        # Component performance recommendations
        component_perf = results.get("component_performance", {})
        heavy_components = component_perf.get("heavy_components", [])
        if heavy_components:
            recommendations.append({
                "type": "performance",
                "priority": "medium",
                "title": "Refactor large components",
                "description": f"Found {len(heavy_components)} components with high complexity",
                "implementation": "Split large components into smaller, focused components",
                "impact": "Improve maintainability and rendering performance"
            })
        
        # Anti-pattern recommendations
        anti_patterns = results.get("anti_patterns", [])
        high_severity_patterns = [p for p in anti_patterns if p.get("severity") == "high"]
        if high_severity_patterns:
            recommendations.append({
                "type": "performance",
                "priority": "high",
                "title": "Fix performance anti-patterns",
                "description": f"Found {len(high_severity_patterns)} critical performance issues",
                "implementation": "Address array index keys and unnecessary re-renders",
                "impact": "Significantly improve rendering performance"
            })
        
        return recommendations
    
    async def usability_testing(self) -> Dict[str, Any]:
        """Conduct usability testing analysis."""
        self.logger.info("👥 Conducting usability testing analysis...")
        
        usability_results = {
            "navigation_analysis": {},
            "form_usability": {},
            "content_readability": {},
            "interaction_patterns": {},
            "desktop_usability": {},  # Changed from mobile_usability for desktop-only project
            "user_flow_issues": []
        }
        
        # This would implement actual usability testing
        return {
            "success": True,
            "usability_testing": usability_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def cross_browser_testing(self) -> Dict[str, Any]:
        """Test cross-browser compatibility."""
        self.logger.info("🌐 Testing cross-browser compatibility...")
        
        compatibility_results = {
            "browser_support": {},
            "css_compatibility": {},
            "javascript_compatibility": {},
            "polyfill_requirements": [],
            "compatibility_score": 0.0
        }
        
        # This would implement actual cross-browser testing
        return {
            "success": True,
            "cross_browser_testing": compatibility_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def responsive_design_check(self) -> Dict[str, Any]:
        """Check responsive design implementation (Desktop-focused for personal PC project)."""
        if self.skip_mobile_testing:
            self.logger.info("📱 Skipping mobile responsive design check (Desktop-only project)")
            return {
                "skipped": True, 
                "reason": "Desktop-only personal PC project",
                "desktop_optimization": "Focused on desktop experience only"
            }
            
        self.logger.info("📱 Checking responsive design...")
        
        responsive_results = {
            "breakpoint_analysis": {},
            "desktop_optimization": {},
            "layout_issues": [],
            "responsive_score": 0.0
        }
        
        # This would implement actual responsive design checking
        return {
            "success": True,
            "responsive_design": responsive_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def seo_optimization(self) -> Dict[str, Any]:
        """Analyze SEO optimization."""
        self.logger.info("🔍 Analyzing SEO optimization...")
        
        seo_results = {
            "meta_tags_analysis": {},
            "heading_structure": {},
            "content_optimization": {},
            "technical_seo": {},
            "seo_score": 0.0
        }
        
        # This would implement actual SEO analysis
        return {
            "success": True,
            "seo_optimization": seo_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def comprehensive_ux_audit(self) -> Dict[str, Any]:
        """Conduct comprehensive UX quality audit (Desktop-focused for personal PC project)."""
        self.logger.info("🔍 Conducting comprehensive UX audit (Desktop-only project)...")
        
        # Run multiple audits (skip mobile-related tests)
        accessibility_audit = await self.audit_accessibility()
        performance_audit = await self.performance_analysis()
        
        # Skip responsive design check for desktop-only project
        if self.skip_mobile_testing:
            self.logger.info("📱 Skipping mobile responsive testing for desktop-only project")
        
        comprehensive_results = {
            "project_type": "desktop_only",
            "mobile_testing_skipped": self.skip_mobile_testing,
            "accessibility": accessibility_audit.get("accessibility_audit", {}),
            "performance": performance_audit.get("performance_analysis", {}),
            "overall_score": 0.0,
            "priority_recommendations": [],
            "audit_summary": {
                "focus": "Desktop user experience optimization",
                "scope": "PC-based personal project"
            }
        }
        
        # Calculate overall score
        acc_score = accessibility_audit.get("accessibility_audit", {}).get("compliance_score", 0)
        perf_score = performance_audit.get("performance_analysis", {}).get("performance_score", 0)
        comprehensive_results["overall_score"] = (acc_score + perf_score) / 2
        
        # Combine priority recommendations
        acc_recs = accessibility_audit.get("accessibility_audit", {}).get("recommendations", [])
        perf_recs = performance_audit.get("performance_analysis", {}).get("optimization_opportunities", [])
        
        all_recs = acc_recs + perf_recs
        priority_recs = [rec for rec in all_recs if rec.get("priority") == "high"]
        comprehensive_results["priority_recommendations"] = priority_recs[:10]  # Top 10
        
        return {
            "success": True,
            "comprehensive_audit": comprehensive_results,
            "timestamp": datetime.now().isoformat()
        }

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = UXQualityAssuranceAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "agent": {"name": "ux-quality-assurance"},
        "task": {"name": "comprehensive_ux_audit", "parameters": {}},
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "knowledge_bases": {}
    }
    
    import asyncio
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2))
