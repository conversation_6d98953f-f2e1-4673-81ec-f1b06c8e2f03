#!/usr/bin/env python3
"""
Enhanced Image Expert Agent
Advanced AI image generation analysis and optimization specialist

This agent provides:
1. Precise image analysis for generation replication
2. Comprehensive style guide creation
3. Advanced prompt enhancement with web research
4. Multi-model optimization (Flux, SDXL, SD, etc.)
5. Real-time knowledge updates
6. Hardware-specific optimization
7. AI detection and metadata extraction
8. Performance analytics and monitoring
9. Brand style guide generation
10. Advanced style analysis engine
"""

import asyncio
import json
import os
import subprocess
import sys
import sqlite3
import hashlib
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import base64
from PIL import Image, ImageStat, ImageEnhance, ImageFilter
from PIL.ExifTags import TAGS
import io
import colorsys
import statistics
import requests
from dataclasses import dataclass
import cv2
import webcolors

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import EnhancedBaseAgent

@dataclass
class StyleAnalysis:
    """Data structure for comprehensive style analysis."""
    artistic_movement: str
    color_palette: Dict[str, Any]
    composition_score: float
    texture_complexity: float
    lighting_type: str
    subject_categories: List[str]
    technical_quality: float
    generation_difficulty: str

@dataclass
class GenerationParameters:
    """Optimized generation parameters for different models."""
    model_type: str
    resolution: Tuple[int, int]
    cfg_scale: float
    steps: int
    scheduler: str
    prompt_structure: str
    negative_prompt: str
    additional_params: Dict[str, Any]

class EnhancedImageExpertAgent(EnhancedBaseAgent):
    """
    Enhanced Image Expert Agent implementation.
    
    Provides unparalleled image analysis, generation guidance, and optimization capabilities.
    Specializes in AI image generation analysis, style replication, and prompt enhancement.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Enhanced paths
        self.images_path = self.project_root / "images"
        self.output_path = self.project_root / "output"
        self.temp_path = self.project_root / "temp"
        self.cache_path = self.project_root / "cache"
        self.style_guides_path = self.project_root / "style_guides"
        
        # Ensure directories exist
        for path in [self.images_path, self.output_path, self.temp_path, self.cache_path, self.style_guides_path]:
            path.mkdir(exist_ok=True)
        
        # Enhanced supported formats
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.gif', '.exr', '.hdr']
        
        # Model configurations
        self.model_configs = {
            "flux": {
                "optimal_resolution": [(1024, 1024), (768, 1344), (1344, 768)],
                "cfg_range": (1.0, 7.0),
                "steps_range": (4, 50),
                "prompt_style": "detailed_descriptive",
                "scheduler": "euler",
                "strength_range": (0.7, 1.0)
            },
            "sdxl": {
                "optimal_resolution": [(1024, 1024), (896, 1152), (1152, 896)],
                "cfg_range": (5.0, 15.0),
                "steps_range": (25, 50),
                "prompt_style": "artistic_focused",
                "scheduler": "dpmpp_2m_karras",
                "strength_range": (0.75, 0.95)
            },
            "stable_diffusion": {
                "optimal_resolution": [(512, 768), (768, 512), (512, 512)],
                "cfg_range": (7.0, 20.0),
                "steps_range": (20, 100),
                "prompt_style": "keyword_based",
                "scheduler": "dpmpp_2m",
                "strength_range": (0.7, 0.9)
            }
        }
        
        # Initialize enhanced components
        self.db_path = self.project_root / "image_analysis.db"
        self.init_enhanced_database()
        
        # Style analysis patterns
        self.artistic_movements = {
            "photorealistic": ["realistic", "photographic", "detailed", "sharp"],
            "impressionist": ["soft", "loose", "atmospheric", "painterly"],
            "abstract": ["geometric", "non-representational", "conceptual"],
            "surreal": ["dreamlike", "impossible", "fantastical", "bizarre"],
            "minimalist": ["simple", "clean", "sparse", "negative space"],
            "baroque": ["ornate", "dramatic", "rich", "elaborate"],
            "cyberpunk": ["neon", "futuristic", "dystopian", "tech"],
            "anime": ["manga", "japanese", "stylized", "cel-shaded"],
            "watercolor": ["transparent", "flowing", "organic", "soft edges"],
            "oil_painting": ["textured", "blended", "classical", "brushstrokes"]
        }
        
    def init_enhanced_database(self):
        """Initialize enhanced database structure."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Style analysis table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS style_analysis (
                id INTEGER PRIMARY KEY,
                image_path TEXT UNIQUE,
                image_hash TEXT,
                artistic_movement TEXT,
                color_palette TEXT,
                composition_analysis TEXT,
                texture_analysis TEXT,
                lighting_analysis TEXT,
                subject_analysis TEXT,
                generation_params TEXT,
                similarity_matches TEXT,
                quality_score REAL,
                generation_difficulty TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Generation performance table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generation_performance (
                id INTEGER PRIMARY KEY,
                model_type TEXT,
                prompt TEXT,
                prompt_hash TEXT,
                parameters TEXT,
                success_rate REAL,
                quality_score REAL,
                user_rating INTEGER,
                generation_time REAL,
                hardware_config TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Style guides table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS style_guides (
                id INTEGER PRIMARY KEY,
                guide_name TEXT UNIQUE,
                brand_identity TEXT,
                visual_elements TEXT,
                color_specifications TEXT,
                typography_style TEXT,
                composition_guidelines TEXT,
                generation_templates TEXT,
                usage_statistics TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Prompt enhancement cache
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prompt_cache (
                id INTEGER PRIMARY KEY,
                original_prompt TEXT,
                prompt_hash TEXT UNIQUE,
                model_type TEXT,
                enhanced_prompt TEXT,
                enhancement_type TEXT,
                research_data TEXT,
                performance_score REAL,
                usage_count INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_used DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific enhanced image expert task."""
        task_name = self.task_name
        
        task_map = {
            "analyze_for_generation": self.analyze_image_for_generation,
            "create_style_guide": self.create_comprehensive_style_guide,
            "enhance_prompt": self.enhance_prompt_with_research,
            "optimize_for_model": self.optimize_for_model,
            "extract_ai_metadata": self.extract_generation_metadata,
            "analyze_brand_consistency": self.analyze_brand_consistency,
            "suggest_variations": self.suggest_image_variations,
            "batch_analyze": self.batch_analyze_images,
            "update_knowledge": self.update_knowledge_base,
            "performance_analysis": self.analyze_generation_performance,
            "detect_ai_generation": self.detect_ai_generation,
            "create_brand_guide": self.generate_brand_style_guide
        }
        
        if task_name in task_map:
            return await task_map[task_name]()
        else:
            return await self.comprehensive_image_analysis()
    
    async def analyze_image_for_generation(self, image_path: Optional[Path] = None) -> Dict[str, Any]:
        """Advanced image analysis for precise generation replication."""
        if not image_path:
            image_path = Path(self.parameters.get("image_path", ""))
            if not image_path.exists():
                # Find first available image if none specified
                image_files = await self._find_image_files()
                if not image_files:
                    return {"success": False, "error": "No images found for analysis"}
                image_path = image_files[0]
        
        self.logger.info(f"🎯 Analyzing image for generation replication: {image_path}")
        
        analysis_result = {
            "image_info": {
                "path": str(image_path.relative_to(self.project_root)),
                "size": image_path.stat().st_size,
                "hash": self._calculate_image_hash(image_path)
            },
            "style_analysis": {},
            "technical_parameters": {},
            "generation_instructions": {},
            "model_recommendations": {},
            "prompt_components": {},
            "replication_confidence": 0.0
        }
        
        try:
            with Image.open(image_path) as img:
                # Comprehensive style detection
                style_analysis = await self._comprehensive_style_analysis(img, image_path)
                analysis_result["style_analysis"] = style_analysis
                
                # Technical parameter extraction
                technical_params = await self._extract_technical_parameters(img, image_path)
                analysis_result["technical_parameters"] = technical_params
                
                # Generate replication prompts
                prompt_components = await self._generate_replication_prompts(img, style_analysis, technical_params)
                analysis_result["prompt_components"] = prompt_components
                
                # Model-specific optimization
                model_recommendations = await self._analyze_optimal_models(style_analysis, technical_params)
                analysis_result["model_recommendations"] = model_recommendations
                
                # Calculate replication confidence
                confidence = await self._calculate_replication_confidence(style_analysis, technical_params)
                analysis_result["replication_confidence"] = confidence
                
                # Save to database
                await self._save_analysis_to_db(image_path, analysis_result)
                
        except Exception as e:
            self.logger.error(f"Error in advanced image analysis: {e}")
            analysis_result["error"] = str(e)
            return {"success": False, "analysis": analysis_result}
        
        return {
            "success": True,
            "analysis": analysis_result,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _comprehensive_style_analysis(self, img: Image.Image, image_path: Path) -> Dict[str, Any]:
        """Comprehensive style analysis using multiple advanced techniques."""
        style_analysis = {
            "artistic_movement": "",
            "confidence_scores": {},
            "color_analysis": {},
            "composition_analysis": {},
            "texture_analysis": {},
            "lighting_analysis": {},
            "subject_analysis": {},
            "technical_style": {},
            "generation_difficulty": "medium",
            "style_keywords": [],
            "reference_artists": [],
            "historical_period": ""
        }
        
        try:
            # Artistic movement identification
            movement_analysis = await self._identify_artistic_movement(img)
            style_analysis["artistic_movement"] = movement_analysis["primary_movement"]
            style_analysis["confidence_scores"] = movement_analysis["confidence_scores"]
            
            # Advanced color analysis
            color_analysis = await self._advanced_color_analysis(img)
            style_analysis["color_analysis"] = color_analysis
            
            # Composition analysis
            composition = await self._analyze_composition(img)
            style_analysis["composition_analysis"] = composition
            
            # Texture pattern analysis
            texture = await self._analyze_texture_patterns(img)
            style_analysis["texture_analysis"] = texture
            
            # Lighting setup analysis
            lighting = await self._analyze_lighting_setup(img)
            style_analysis["lighting_analysis"] = lighting
            
            # Subject matter analysis
            subjects = await self._analyze_subject_matter(img)
            style_analysis["subject_analysis"] = subjects
            
            # Technical style identification
            tech_style = await self._identify_technical_style(img)
            style_analysis["technical_style"] = tech_style
            
            # Generation difficulty assessment
            difficulty = await self._assess_generation_complexity(img, style_analysis)
            style_analysis["generation_difficulty"] = difficulty
            
            # Generate style keywords
            keywords = await self._generate_style_keywords(style_analysis)
            style_analysis["style_keywords"] = keywords
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive style analysis: {e}")
        
        return style_analysis
    
    async def _identify_artistic_movement(self, img: Image.Image) -> Dict[str, Any]:
        """Identify the artistic movement/style of the image."""
        movement_scores = {}
        
        # Analyze image characteristics for each movement
        img_stats = ImageStat.Stat(img)
        
        # Color variance analysis
        color_variance = sum(img_stats.var) / len(img_stats.var) if img_stats.var else 0
        
        # Edge detection for composition analysis
        img_array = np.array(img.convert('RGB'))
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY) if len(img_array.shape) == 3 else img_array
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # Brightness analysis
        brightness = sum(img_stats.mean) / len(img_stats.mean) if img_stats.mean else 128
        
        # Score each artistic movement
        if color_variance < 500 and edge_density > 0.1:
            movement_scores["photorealistic"] = 0.8
        
        if color_variance > 1000 and edge_density < 0.05:
            movement_scores["impressionist"] = 0.7
        
        if brightness > 200 or brightness < 50:
            movement_scores["dramatic"] = 0.6
        
        if edge_density < 0.02:
            movement_scores["minimalist"] = 0.5
        
        # Determine primary movement
        primary_movement = max(movement_scores.items(), key=lambda x: x[1])[0] if movement_scores else "contemporary"
        
        return {
            "primary_movement": primary_movement,
            "confidence_scores": movement_scores
        }
    
    async def _advanced_color_analysis(self, img: Image.Image) -> Dict[str, Any]:
        """Perform advanced color analysis of the image."""
        color_analysis = {
            "dominant_colors": [],
            "color_harmony": "",
            "temperature": "neutral",
            "saturation_level": "medium",
            "contrast_level": "medium",
            "color_palette_hex": [],
            "color_relationships": {}
        }
        
        try:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Get color statistics
            img_stats = ImageStat.Stat(img)
            
            # Dominant color extraction using quantization
            img_small = img.resize((50, 50))
            img_array = np.array(img_small)
            pixels = img_array.reshape(-1, 3)
            
            # K-means clustering for dominant colors
            from sklearn.cluster import KMeans
            kmeans = KMeans(n_clusters=5, random_state=42)
            kmeans.fit(pixels)
            
            dominant_colors = []
            for color in kmeans.cluster_centers_:
                hex_color = '#%02x%02x%02x' % (int(color[0]), int(color[1]), int(color[2]))
                dominant_colors.append(hex_color)
            
            color_analysis["dominant_colors"] = dominant_colors
            color_analysis["color_palette_hex"] = dominant_colors
            
            # Color temperature analysis
            avg_r, avg_g, avg_b = img_stats.mean
            if avg_r > avg_b * 1.2:
                color_analysis["temperature"] = "warm"
            elif avg_b > avg_r * 1.2:
                color_analysis["temperature"] = "cool"
            
            # Saturation analysis
            hsv_img = img.convert('HSV')
            hsv_stats = ImageStat.Stat(hsv_img)
            avg_saturation = hsv_stats.mean[1] if len(hsv_stats.mean) > 1 else 128
            
            if avg_saturation > 180:
                color_analysis["saturation_level"] = "high"
            elif avg_saturation < 80:
                color_analysis["saturation_level"] = "low"
            
            # Contrast analysis
            img_gray = img.convert('L')
            gray_stats = ImageStat.Stat(img_gray)
            contrast = gray_stats.stddev[0] if gray_stats.stddev else 0
            
            if contrast > 80:
                color_analysis["contrast_level"] = "high"
            elif contrast < 30:
                color_analysis["contrast_level"] = "low"
        
        except Exception as e:
            self.logger.debug(f"Error in color analysis: {e}")
        
        return color_analysis
    
    async def _analyze_composition(self, img: Image.Image) -> Dict[str, Any]:
        """Analyze composition elements of the image."""
        composition = {
            "rule_of_thirds": False,
            "symmetry_type": "none",
            "focal_points": [],
            "visual_weight_distribution": "balanced",
            "leading_lines": False,
            "depth_indicators": [],
            "framing_elements": False
        }
        
        try:
            # Convert to array for analysis
            img_array = np.array(img.convert('RGB'))
            height, width = img_array.shape[:2]
            
            # Rule of thirds analysis
            thirds_h = height // 3
            thirds_w = width // 3
            
            # Simple edge detection to find focal points
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            
            # Check rule of thirds intersections for high activity
            intersections = [
                (thirds_w, thirds_h), (2 * thirds_w, thirds_h),
                (thirds_w, 2 * thirds_h), (2 * thirds_w, 2 * thirds_h)
            ]
            
            for x, y in intersections:
                if x < width and y < height:
                    region = edges[max(0, y-10):min(height, y+10), max(0, x-10):min(width, x+10)]
                    if np.sum(region) > 1000:  # High edge activity
                        composition["rule_of_thirds"] = True
                        break
            
            # Symmetry analysis
            left_half = img_array[:, :width//2]
            right_half = np.fliplr(img_array[:, width//2:])
            
            if left_half.shape == right_half.shape:
                similarity = np.mean(np.abs(left_half - right_half))
                if similarity < 50:
                    composition["symmetry_type"] = "vertical"
            
            # Depth indicators
            blur_variance = cv2.Laplacian(gray, cv2.CV_64F).var()
            if blur_variance > 500:
                composition["depth_indicators"].append("shallow_depth_of_field")
            
        except Exception as e:
            self.logger.debug(f"Error in composition analysis: {e}")
        
        return composition
    
    async def _generate_replication_prompts(self, img: Image.Image, style_analysis: Dict, 
                                          technical_params: Dict) -> Dict[str, Any]:
        """Generate precise prompts for replicating the image."""
        prompt_components = {
            "base_description": "",
            "style_modifiers": [],
            "technical_modifiers": [],
            "quality_enhancers": [],
            "negative_prompt_suggestions": [],
            "complete_prompts": {},
            "alternative_approaches": []
        }
        
        try:
            # Base description from subject analysis
            subjects = style_analysis.get("subject_analysis", {})
            base_desc_parts = []
            
            if subjects.get("main_subjects"):
                base_desc_parts.extend(subjects["main_subjects"])
            
            if subjects.get("scene_type"):
                base_desc_parts.append(subjects["scene_type"])
            
            prompt_components["base_description"] = ", ".join(base_desc_parts)
            
            # Style modifiers
            style_modifiers = []
            artistic_movement = style_analysis.get("artistic_movement", "")
            if artistic_movement and artistic_movement in self.artistic_movements:
                style_modifiers.extend(self.artistic_movements[artistic_movement])
            
            # Color modifiers
            color_analysis = style_analysis.get("color_analysis", {})
            if color_analysis.get("temperature") == "warm":
                style_modifiers.append("warm lighting")
            elif color_analysis.get("temperature") == "cool":
                style_modifiers.append("cool lighting")
            
            if color_analysis.get("saturation_level") == "high":
                style_modifiers.append("vibrant colors")
            elif color_analysis.get("saturation_level") == "low":
                style_modifiers.append("muted colors")
            
            prompt_components["style_modifiers"] = style_modifiers
            
            # Technical modifiers
            tech_modifiers = []
            if technical_params.get("estimated_cfg") > 12:
                tech_modifiers.append("highly detailed")
            
            if technical_params.get("quality_indicators", {}).get("sharpness") == "high":
                tech_modifiers.append("sharp focus")
            
            prompt_components["technical_modifiers"] = tech_modifiers
            
            # Quality enhancers
            quality_enhancers = [
                "masterpiece", "best quality", "highly detailed",
                "professional photography" if style_analysis.get("artistic_movement") == "photorealistic" else "professional artwork"
            ]
            prompt_components["quality_enhancers"] = quality_enhancers
            
            # Negative prompt suggestions
            negative_suggestions = [
                "low quality", "blurry", "pixelated", "artifacts",
                "watermark", "signature", "text"
            ]
            
            if style_analysis.get("artistic_movement") == "photorealistic":
                negative_suggestions.extend(["cartoon", "anime", "painting"])
            
            prompt_components["negative_prompt_suggestions"] = negative_suggestions
            
            # Complete prompts for different models
            base_prompt = prompt_components["base_description"]
            style_str = ", ".join(style_modifiers[:3])  # Top 3 style modifiers
            quality_str = ", ".join(quality_enhancers[:2])  # Top 2 quality enhancers
            
            prompt_components["complete_prompts"] = {
                "flux": f"{base_prompt}, {style_str}, {quality_str}",
                "sdxl": f"{quality_str}, {base_prompt}, {style_str}",
                "sd15": f"({base_prompt}:1.2), {style_str}, {quality_str}"
            }
            
        except Exception as e:
            self.logger.error(f"Error generating replication prompts: {e}")
        
        return prompt_components
    
    async def enhance_prompt_with_research(self, base_prompt: Optional[str] = None, 
                                         target_style: Optional[str] = None, 
                                         model_type: str = "flux") -> Dict[str, Any]:
        """Enhanced prompt generation with online research and optimization."""
        if not base_prompt:
            base_prompt = self.parameters.get("prompt", "")
        
        if not target_style:
            target_style = self.parameters.get("style", "photorealistic")
        
        self.logger.info(f"🚀 Enhancing prompt with research for {model_type}")
        
        # Check cache first
        cached_result = await self._check_prompt_cache(base_prompt, model_type)
        if cached_result:
            return cached_result
        
        enhancement_result = {
            "original_prompt": base_prompt,
            "model_type": model_type,
            "target_style": target_style,
            "enhanced_versions": {},
            "research_insights": {},
            "optimization_notes": {},
            "performance_predictions": {}
        }
        
        try:
            # Research current best practices (simulated - would use web search in production)
            research_results = await self._research_prompt_techniques(model_type)
            enhancement_result["research_insights"] = research_results
            
            # Analyze current prompt structure
            prompt_analysis = await self._analyze_prompt_structure(base_prompt)
            enhancement_result["optimization_notes"] = prompt_analysis
            
            # Generate enhanced versions
            enhanced_versions = {
                "basic_enhanced": await self._basic_prompt_enhancement(base_prompt, research_results),
                "style_optimized": await self._style_specific_enhancement(base_prompt, target_style, research_results),
                "model_optimized": await self._model_specific_enhancement(base_prompt, model_type, research_results),
                "advanced_structured": await self._advanced_structured_enhancement(base_prompt, research_results),
                "creative_variation": await self._creative_prompt_variation(base_prompt, target_style)
            }
            
            enhancement_result["enhanced_versions"] = enhanced_versions
            
            # Generate negative prompts
            negative_prompts = await self._generate_negative_prompts(base_prompt, model_type)
            enhancement_result["negative_prompts"] = negative_prompts
            
            # Parameter suggestions
            param_suggestions = await self._suggest_generation_parameters(base_prompt, model_type)
            enhancement_result["parameter_suggestions"] = param_suggestions
            
            # Cache the result
            await self._cache_prompt_enhancement(base_prompt, model_type, enhancement_result)
            
        except Exception as e:
            self.logger.error(f"Error in prompt enhancement: {e}")
            enhancement_result["error"] = str(e)
        
        return {
            "success": True,
            "enhancement": enhancement_result,
            "timestamp": datetime.now().isoformat()
        }
    
    async def create_comprehensive_style_guide(self, reference_images: Optional[List[Path]] = None) -> Dict[str, Any]:
        """Create detailed style guide from reference images."""
        if not reference_images:
            # Get images from parameters or find in project
            image_paths = self.parameters.get("image_paths", [])
            if image_paths:
                reference_images = [Path(p) for p in image_paths]
            else:
                reference_images = (await self._find_image_files())[:10]  # Limit to 10 for analysis
        
        self.logger.info(f"📚 Creating comprehensive style guide from {len(reference_images)} images")
        
        style_guide = {
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "num_reference_images": len(reference_images),
                "analysis_version": "2.0"
            },
            "brand_identity": {},
            "visual_elements": {},
            "color_palette": {},
            "typography_style": {},
            "composition_rules": {},
            "generation_templates": {},
            "model_specific_params": {},
            "consistency_metrics": {}
        }
        
        if not reference_images:
            return {"success": False, "error": "No reference images provided"}
        
        try:
            # Analyze each reference image
            image_analyses = []
            for img_path in reference_images:
                if img_path.exists():
                    analysis_result = await self.analyze_image_for_generation(img_path)
                    if analysis_result.get("success"):
                        image_analyses.append(analysis_result["analysis"])
            
            if not image_analyses:
                return {"success": False, "error": "No images could be analyzed"}
            
            # Extract common patterns
            style_guide["brand_identity"] = await self._extract_brand_identity(image_analyses)
            style_guide["visual_elements"] = await self._identify_visual_patterns(image_analyses)
            style_guide["color_palette"] = await self._generate_consolidated_color_palette(image_analyses)
            style_guide["composition_rules"] = await self._extract_composition_patterns(image_analyses)
            style_guide["generation_templates"] = await self._create_generation_templates(image_analyses)
            style_guide["model_specific_params"] = await self._optimize_for_all_models(image_analyses)
            style_guide["consistency_metrics"] = await self._calculate_consistency_metrics(image_analyses)
            
            # Save style guide
            await self._save_style_guide(style_guide)
            
        except Exception as e:
            self.logger.error(f"Error creating style guide: {e}")
            return {"success": False, "error": str(e)}
        
        return {
            "success": True,
            "style_guide": style_guide,
            "timestamp": datetime.now().isoformat()
        }
    
    # Additional utility methods for completeness
    def _calculate_image_hash(self, image_path: Path) -> str:
        """Calculate SHA-256 hash of image file."""
        hash_sha256 = hashlib.sha256()
        with open(image_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    async def _find_image_files(self) -> List[Path]:
        """Find all image files in the project with enhanced search."""
        image_files = []
        search_paths = [
            self.images_path, self.output_path,
            self.project_root / "data", self.project_root / "assets",
            self.project_root / "static", self.project_root / "public",
            self.project_root / "samples"
        ]
        
        for search_path in search_paths:
            if search_path.exists():
                for format_ext in self.supported_formats:
                    image_files.extend(search_path.rglob(f"*{format_ext}"))
                    image_files.extend(search_path.rglob(f"*{format_ext.upper()}"))
        
        return list(set(image_files))  # Remove duplicates
    
    async def comprehensive_image_analysis(self) -> Dict[str, Any]:
        """Default comprehensive analysis combining all features."""
        self.logger.info("🔍 Performing comprehensive image analysis")
        
        # Find images to analyze
        image_files = await self._find_image_files()
        if not image_files:
            return {"success": False, "error": "No images found for analysis"}
        
        # Perform multi-faceted analysis
        results = {
            "total_images_analyzed": len(image_files),
            "individual_analyses": [],
            "aggregate_insights": {},
            "style_recommendations": [],
            "optimization_opportunities": []
        }
        
        # Analyze up to 20 images for performance
        for image_path in image_files[:20]:
            try:
                analysis = await self.analyze_image_for_generation(image_path)
                if analysis.get("success"):
                    results["individual_analyses"].append(analysis["analysis"])
            except Exception as e:
                self.logger.warning(f"Failed to analyze {image_path}: {e}")
        
        # Generate aggregate insights
        if results["individual_analyses"]:
            results["aggregate_insights"] = await self._generate_aggregate_insights(results["individual_analyses"])
            results["style_recommendations"] = await self._generate_style_recommendations(results["individual_analyses"])
        
        return {
            "success": True,
            "analysis": results,
            "timestamp": datetime.now().isoformat()
        }

# Placeholder implementations for complex methods (to be fully implemented based on specific needs)
    async def _extract_technical_parameters(self, img: Image.Image, image_path: Path) -> Dict[str, Any]:
        """Extract technical parameters from image."""
        return {
            "dimensions": (img.width, img.height),
            "aspect_ratio": img.width / img.height if img.height > 0 else 1.0,
            "file_size": image_path.stat().st_size,
            "estimated_cfg": 7.5,  # Would be extracted from metadata if available
            "estimated_steps": 30,
            "quality_indicators": {"sharpness": "medium", "noise_level": "low"}
        }
    
    async def _research_prompt_techniques(self, model_type: str) -> Dict[str, Any]:
        """Research latest prompt techniques (placeholder for web integration)."""
        return {
            "latest_techniques": ["attention_weighting", "prompt_scheduling", "negative_emphasis"],
            "effective_keywords": ["masterpiece", "best quality", "highly detailed"],
            "model_specific_tips": {
                "flux": ["use natural language", "avoid excessive parentheses"],
                "sdxl": ["front-load important concepts", "use artistic style references"],
                "sd15": ["use attention weights", "shorter prompts work better"]
            }
        }
    
    # ... Additional utility methods would continue here
    
    async def _save_analysis_to_db(self, image_path: Path, analysis: Dict[str, Any]):
        """Save analysis results to database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO style_analysis 
            (image_path, image_hash, artistic_movement, color_palette, composition_analysis, 
             texture_analysis, lighting_analysis, subject_analysis, generation_params, quality_score, generation_difficulty)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            str(image_path.relative_to(self.project_root)),
            analysis["image_info"]["hash"],
            analysis["style_analysis"].get("artistic_movement", ""),
            json.dumps(analysis["style_analysis"].get("color_analysis", {})),
            json.dumps(analysis["style_analysis"].get("composition_analysis", {})),
            json.dumps(analysis["style_analysis"].get("texture_analysis", {})),
            json.dumps(analysis["style_analysis"].get("lighting_analysis", {})),
            json.dumps(analysis["style_analysis"].get("subject_analysis", {})),
            json.dumps(analysis.get("technical_parameters", {})),
            analysis.get("replication_confidence", 0.0),
            analysis["style_analysis"].get("generation_difficulty", "medium")
        ))
        
        conn.commit()
        conn.close()

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = EnhancedImageExpertAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "agent": {"name": "enhanced-image-expert"},
        "task": {"name": "comprehensive_analysis", "parameters": {}},
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "knowledge_bases": {}
    }
    
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2, default=str))
