"""
Path Resolution Utility

Handles model path resolution with fallback support for both L: and G: drive locations.
Provides centralized logic for finding the correct model directories.
"""

import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Tuple

logger = logging.getLogger(__name__)

class ModelPathResolver:
    """
    Resolves model paths with fallback support for multiple locations
    """
    
    def __init__(self):
        # Define possible model base paths in order of preference
        self.model_base_paths = [
            "L:/ComfyUI/models",  # Primary location (actual models)
            "G:/ZComfyUI/ComfyPort/ComfyUI/models",  # Secondary location (linked)
        ]
        
        # Cache resolved paths to avoid repeated filesystem checks
        self._path_cache: Dict[str, Optional[str]] = {}
        self._base_path_cache: Optional[str] = None
    
    def get_working_model_base_path(self) -> Optional[str]:
        """
        Get the first working model base path
        """
        if self._base_path_cache is not None:
            return self._base_path_cache
        
        for base_path in self.model_base_paths:
            if self._is_valid_model_base_path(base_path):
                logger.info(f"Using model base path: {base_path}")
                self._base_path_cache = base_path
                return base_path
            else:
                logger.debug(f"Model base path not valid: {base_path}")
        
        logger.error("No valid model base path found!")
        return None
    
    def _is_valid_model_base_path(self, base_path: str) -> bool:
        """
        Check if a model base path is valid by looking for expected subdirectories
        and actual model files
        """
        try:
            base_path_obj = Path(base_path)
            if not base_path_obj.exists():
                return False
            
            # Check for expected model subdirectories
            expected_dirs = ['checkpoints', 'diffusion_models', 'unet', 'loras', 'vae']
            existing_dirs = [d for d in expected_dirs if (base_path_obj / d).exists()]
            
            if len(existing_dirs) < 3:  # Require at least 3 model directories
                return False
            
            # Check if there are actual model files (not just placeholder files)
            model_extensions = ['.safetensors', '.ckpt', '.pth', '.gguf']
            
            for dir_name in existing_dirs:
                dir_path = base_path_obj / dir_name
                model_files = [
                    f for f in dir_path.iterdir() 
                    if f.is_file() and f.suffix.lower() in model_extensions
                    and not f.name.startswith('put_') and f.stat().st_size > 1024  # Exclude placeholder files
                ]
                
                if model_files:  # Found actual model files
                    logger.debug(f"Found {len(model_files)} model files in {dir_path}")
                    return True
            
            logger.debug(f"No model files found in {base_path}")
            return False
            
        except Exception as e:
            logger.error(f"Error checking model base path {base_path}: {e}")
            return False
    
    def resolve_model_directory(self, model_type: str) -> Optional[str]:
        """
        Resolve the path for a specific model type (e.g., 'checkpoints', 'loras')
        """
        cache_key = f"model_dir_{model_type}"
        if cache_key in self._path_cache:
            return self._path_cache[cache_key]
        
        base_path = self.get_working_model_base_path()
        if not base_path:
            self._path_cache[cache_key] = None
            return None
        
        model_dir_path = os.path.join(base_path, model_type)
        
        if os.path.exists(model_dir_path):
            logger.debug(f"Resolved {model_type} path: {model_dir_path}")
            self._path_cache[cache_key] = model_dir_path
            return model_dir_path
        else:
            logger.warning(f"Model directory does not exist: {model_dir_path}")
            self._path_cache[cache_key] = None
            return None
    
    def get_all_model_directories(self) -> Dict[str, Optional[str]]:
        """
        Get all model directory paths
        """
        model_types = [
            'checkpoints', 'unet', 'diffusion_models', 'clip', 'text_encoders',
            'loras', 'vae', 'upscale_models', 'controlnet', 'embeddings',
            'hypernetworks', 'style_models', 'clip_vision', 'configs'
        ]
        
        directories = {}
        for model_type in model_types:
            directories[model_type] = self.resolve_model_directory(model_type)
        
        return directories
    
    def scan_model_files(self, model_type: str, extensions: List[str] = None) -> List[Tuple[str, str]]:
        """
        Scan for model files in a specific directory
        Returns list of (filename, full_path) tuples
        """
        if extensions is None:
            extensions = ['.safetensors', '.ckpt', '.pth', '.gguf']
        
        model_dir = self.resolve_model_directory(model_type)
        if not model_dir:
            logger.warning(f"Cannot scan {model_type}: directory not found")
            return []
        
        try:
            model_dir_path = Path(model_dir)
            model_files = []
            
            for file_path in model_dir_path.iterdir():
                if (file_path.is_file() and 
                    file_path.suffix.lower() in extensions and
                    not file_path.name.startswith('put_') and  # Exclude placeholder files
                    file_path.stat().st_size > 1024):  # Exclude very small files
                    
                    model_files.append((file_path.name, str(file_path)))
            
            logger.info(f"Found {len(model_files)} {model_type} files in {model_dir}")
            return model_files
            
        except Exception as e:
            logger.error(f"Error scanning {model_type} directory {model_dir}: {e}")
            return []
    
    def get_path_info(self) -> Dict[str, any]:
        """
        Get diagnostic information about path resolution
        """
        base_path = self.get_working_model_base_path()
        all_dirs = self.get_all_model_directories()
        
        # Count model files in each directory
        file_counts = {}
        for model_type, dir_path in all_dirs.items():
            if dir_path:
                files = self.scan_model_files(model_type)
                file_counts[model_type] = len(files)
            else:
                file_counts[model_type] = 0
        
        return {
            'working_base_path': base_path,
            'attempted_paths': self.model_base_paths,
            'resolved_directories': all_dirs,
            'file_counts': file_counts,
            'total_models': sum(file_counts.values())
        }
    
    def clear_cache(self):
        """Clear the path resolution cache"""
        self._path_cache.clear()
        self._base_path_cache = None
        logger.debug("Path resolution cache cleared")

# Global instance
model_path_resolver = ModelPathResolver()
