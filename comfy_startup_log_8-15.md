
G:\ZComfyUI\ComfyPort>.\python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --enable-cors-header="http://localhost:3003,http://127.0.0.1:3003,http://localhost:8000,http://127.0.0.1:8000"
Adding extra search path models L:\ComfyUI\models
Adding extra search path checkpoints L:\ComfyUI\models\checkpoints
Adding extra search path clip L:\ComfyUI\models\clip
Adding extra search path clip_vision L:\ComfyUI\models\clip_vision
Adding extra search path configs L:\ComfyUI\models\configs
Adding extra search path controlnet L:\ComfyUI\models\controlnet
Adding extra search path diffusion_models L:\ComfyUI\models\diffusion_models
Adding extra search path diffusion_models L:\ComfyUI\models\unet
Adding extra search path embeddings L:\ComfyUI\models\embeddings
Adding extra search path loras L:\ComfyUI\models\loras
Adding extra search path upscale_models L:\ComfyUI\models\upscale_models
Adding extra search path vae L:\ComfyUI\models\vae
[START] Security scan
WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)
[DONE] Security scan
## ComfyUI-Manager: installing dependencies done.
** ComfyUI startup time: 2025-08-15 11:28:39.537
** Platform: Windows
** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
** Python executable: G:\ZComfyUI\ComfyPort\python_embeded\python.exe
** ComfyUI Path: G:\ZComfyUI\ComfyPort\ComfyUI
** ComfyUI Base Folder Path: G:\ZComfyUI\ComfyPort\ComfyUI
** User directory: G:\ZComfyUI\ComfyPort\ComfyUI\user
** ComfyUI-Manager config path: G:\ZComfyUI\ComfyPort\ComfyUI\user\default\ComfyUI-Manager\config.ini
** Log path: G:\ZComfyUI\ComfyPort\ComfyUI\user\comfyui.log
WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)
WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)

Prestartup times for custom nodes:
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\rgthree-comfy
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-easy-use
   8.9 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-Manager

Checkpoint files will always be loaded safely.
Total VRAM 16376 MB, total RAM 65246 MB
pytorch version: 2.8.0+cu128
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4070 Ti SUPER : cudaMallocAsync
Using pytorch attention
Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
ComfyUI version: 0.3.50
ComfyUI frontend version: 1.24.4
[Prompt Server] web root: G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\comfyui_frontend_package\static
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\comfy_api_nodes\canary.py", line 5, in <module>
    raise Exception("INSTALL NEW VERSION OF PYAV TO USE API NODES.")
Exception: INSTALL NEW VERSION OF PYAV TO USE API NODES.

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\comfy_api_nodes\canary.py module for custom nodes: INSTALL NEW VERSION OF PYAV TO USE API NODES.
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfy-mtb] | INFO -> loaded 103 nodes successfuly
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfy-mtb] | INFO -> Some nodes (5) could not be loaded. This can be ignored, but go to http://127.0.0.1:8188/mtb if you want more information.
Loaded Apply Style Model Adjust node - Use this for better control over style vs prompt balance
Adding G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes to sys.path
Efficiency Nodes: Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...Success!
Loaded Efficiency nodes from G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\efficiency-nodes-comfyui
Loaded ControlNetPreprocessors nodes from G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux
Could not find AdvancedControlNet nodes
Could not find AnimateDiff nodes
Could not find IPAdapter nodes
Could not find VideoHelperSuite nodes
### Loading: ComfyUI-Impact-Pack (V8.21)

----------------------------------------------------------------------------
[Impact Pack] The SAM2 functionality is unavailable because the `facebook/sam2` dependency is not installed.

Installation command:
G:\ZComfyUI\ComfyPort\python_embeded\python.exe -m pip install git+https://github.com/facebookresearch/sam2
----------------------------------------------------------------------------

### Loading: ComfyUI-Impact-Pack (V8.21)

----------------------------------------------------------------------------
[Impact Pack] The SAM2 functionality is unavailable because the `facebook/sam2` dependency is not installed.

Installation command:
G:\ZComfyUI\ComfyPort\python_embeded\python.exe -m pip install git+https://github.com/facebookresearch/sam2
----------------------------------------------------------------------------

Loaded ImpactPack nodes from G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[Impact Pack] custom_wildcards path not found: C:\Comfyui\custom_nodes\ComfyUI-Impact-Pack\custom_wildcards. Using default path.
[Impact Pack] Wildcards loading done.
[Impact Pack] Wildcards loading done.
[Crystools INFO] Crystools version: 1.22.0
[Crystools INFO] CPU: 13th Gen Intel(R) Core(TM) i5-13600KF - Arch: AMD64 - OS: Windows 11
[Crystools INFO] Pynvml (Nvidia) initialized.
[Crystools INFO] GPU/s:
[Crystools INFO] 0) NVIDIA GeForce RTX 4070 Ti SUPER
[Crystools INFO] NVIDIA Driver: 580.97
[ComfyUI-Easy-Use] server: v1.3.1 Loaded
[ComfyUI-Easy-Use] web root: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 Loaded
G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-fluxpromptenhancer\__init__.py:4: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
ComfyUI-GGUF: Allowing full torch compile
ComfyUI-GGUF: Allowing full torch compile
Total VRAM 16376 MB, total RAM 65246 MB
pytorch version: 2.8.0+cu128
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4070 Ti SUPER : cudaMallocAsync
### Loading: ComfyUI-Impact-Pack (V8.21)

----------------------------------------------------------------------------
[Impact Pack] The SAM2 functionality is unavailable because the `facebook/sam2` dependency is not installed.

Installation command:
G:\ZComfyUI\ComfyPort\python_embeded\python.exe -m pip install git+https://github.com/facebookresearch/sam2
----------------------------------------------------------------------------

[Impact Pack] Wildcards loading done.
### Loading: ComfyUI-Inspire-Pack (V1.21)
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inspyrenet-rembg\__init__.py", line 1, in <module>
    from .Inspyrenet_Rembg import InspyrenetRembg, InspyrenetRembgAdvanced
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inspyrenet-rembg\Inspyrenet_Rembg.py", line 4, in <module>
    from transparent_background import Remover
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\__init__.py", line 1, in <module>
    from transparent_background.Remover import Remover, console
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\Remover.py", line 26, in <module>
    from transparent_background.InSPyReNet import InSPyReNet_SwinB
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\InSPyReNet.py", line 17, in <module>
    from transparent_background.backbones.SwinTransformer import SwinB
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\backbones\SwinTransformer.py", line 13, in <module>
    from timm.layers import DropPath, to_2tuple, trunc_normal_
ModuleNotFoundError: No module named 'timm.layers'

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inspyrenet-rembg module for custom nodes: No module named 'timm.layers'
----------Jake Upgrade Nodes Loaded----------
### Loading: ComfyUI-Manager (V3.32.5)
[ComfyUI-Manager] network_mode: public
### ComfyUI Version: v0.3.50-2-gafa0a452 | Released on '2025-08-13'
Skip G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\Comfyui-nodes-flux module for custom nodes due to the lack of NODE_CLASS_MAPPINGS or NODES_LIST (need one).
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
Skip G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ollamapromptenhance module for custom nodes due to the lack of NODE_CLASS_MAPPINGS or NODES_LIST (need one).
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-reactor\__init__.py", line 23, in <module>
    from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-reactor\nodes.py", line 17, in <module>
    from insightface.app.common import Face
ModuleNotFoundError: No module named 'insightface'

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-reactor module for custom nodes: No module named 'insightface'
Error loading AILab_SegmentV2.py: No module named 'groundingdino'
[ComfyUI-RMBG] v2.5.0 | 27 nodes Loaded
ComfyUI found: G:\ZComfyUI\ComfyPort\ComfyUI
'G:\ZComfyUI\ComfyPort\ComfyUI' added to sys.path
FETCH ComfyRegistry Data: 5/94
Warning: Could not load sageattention: No module named 'sageattention'
sageattention package is not installed
Workspace manager - Openning file hash dict
### Loading: Workspace Manager (V1.0.0)
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ycyy-inspyrenet\__init__.py", line 1, in <module>
    from .inspyrenet import InspyrenetRembg, InspyrenetRembgAdvanced
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ycyy-inspyrenet\inspyrenet.py", line 4, in <module>
    from transparent_background import Remover
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\__init__.py", line 1, in <module>
    from transparent_background.Remover import Remover, console
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\Remover.py", line 26, in <module>
    from transparent_background.InSPyReNet import InSPyReNet_SwinB
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\InSPyReNet.py", line 17, in <module>
    from transparent_background.backbones.SwinTransformer import SwinB
  File "G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages\transparent_background\backbones\SwinTransformer.py", line 13, in <module>
    from timm.layers import DropPath, to_2tuple, trunc_normal_
ModuleNotFoundError: No module named 'timm.layers'

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ycyy-inspyrenet module for custom nodes: No module named 'timm.layers'
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider']

### [START] ComfyUI AlekPet Nodes v1.0.77 ###
Node -> ArgosTranslateNode: ArgosTranslateCLIPTextEncodeNode, ArgosTranslateTextNode [Loading]
Node -> ChatGLMNode: ChatGLM4TranslateCLIPTextEncodeNode, ChatGLM4TranslateTextNode, ChatGLM4InstructNode, ChatGLM4InstructMediaNode [Loading]
Node -> DeepTranslatorNode: DeepTranslatorCLIPTextEncodeNode, DeepTranslatorTextNode [Loading]
Node -> ExtrasNode: PreviewTextNode, HexToHueNode, ColorsCorrectNode [Loading]
Node -> GoogleTranslateNode: GoogleTranslateCLIPTextEncodeNode, GoogleTranslateTextNode  [Failed]
Node -> IDENode: IDENode [Loading]
Node -> PainterNode: PainterNode [Loading]
Node -> PoseNode: PoseNode [Loading]

* Nodes have been temporarily disabled due to the error or specially *
GoogleTranslateNode -> Using http2=True, but the 'h2' package is not installed. Make sure to install httpx using `pip install httpx[http2]`.

### [END] ComfyUI AlekPet Nodes ###
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_fill-nodes\__init__.py", line 5, in <module>
    from .nodes.FL_GeminiImageEditor import FL_GeminiImageEditor
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_fill-nodes\nodes\FL_GeminiImageEditor.py", line 11, in <module>
    from google import genai
ImportError: cannot import name 'genai' from 'google' (unknown location)

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_fill-nodes module for custom nodes: cannot import name 'genai' from 'google' (unknown location)
Warning: GPTQModel not installed.
Warning: optimum not installed. GPTQ models (NF4 variants) will be disabled.
**********************************************************************
Warning: Some HiDream models disabled...
**********************************************************************
HiDream: Successfully registered with ComfyUI memory management
--------------------------------------------------
HiDream Sampler Node Initialized
Available Models: ['full', 'dev', 'fast']
--------------------------------------------------
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_instantid\__init__.py", line 1, in <module>
    from .InstantID import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_instantid\InstantID.py", line 13, in <module>
    from insightface.app import FaceAnalysis
ModuleNotFoundError: No module named 'insightface'

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_instantid module for custom nodes: No module named 'insightface'
Efficiency Nodes: Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...Success!
FETCH ComfyRegistry Data: 10/94
WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
WAS Node Suite: Finished. Loaded 218 nodes successfully.

        "Do one thing every day that scares you." - Eleanor Roosevelt


[rgthree-comfy] Loaded 48 magnificent nodes. 🎉

[!] WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)
 Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
      [!] WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)
[!] WARNING: Ignoring invalid distribution ~umpy (G:\ZComfyUI\ComfyPort\python_embeded\Lib\site-packages)
Traceback (most recent call last):
  File "G:\ZComfyUI\ComfyPort\ComfyUI\nodes.py", line 2129, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\sd-ppp\__init__.py", line 54, in <module>
    from .sdppp_python.sdppp import SDPPP
  File "G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\sd-ppp\sdppp_python\sdppp.py", line 1, in <module>
    import socketio
ModuleNotFoundError: No module named 'socketio'

Cannot import G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\sd-ppp module for custom nodes: No module named 'socketio'
FETCH ComfyRegistry Data: 15/94
Searge-SDXL v4.3.1 in G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\SeargeSDXL
WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
WAS Node Suite: Finished. Loaded 220 nodes successfully.

        "Every strike brings me closer to the next home run." - Babe Ruth

WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
WAS Node Suite: Finished. Loaded 220 nodes successfully.

        "Art is the breath of life." - Liza Donnelly


Import times for custom nodes:
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ollamapromptenhance
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-Apply_Style_Model_Adjust
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\websocket_image_save.py
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ControlNet-LLLite-ComfyUI
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\Comfyui-nodes-flux
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUi_NNLatentUpscale
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-reactor
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI_restart_sampling
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-mxtoolkit
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-GGUF-main
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ycyy-inspyrenet
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_imageprocessing
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_instantid
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-detail-daemon
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_automation_controls
   0.0 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_fill-nodes
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-fluxsettingsnode
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inpaint-nodes
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\efficiency-nodes-comfyui
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-workspace-manager
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfy-image-saver
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\x-flux-comfyui
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\cg-use-everywhere
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-GGUF
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_lg_tools
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-advanced-controlnet
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-custom-scripts
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_essentials
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-layerdiffuse
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-image-saver
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-kjnodes
   0.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-jakeupgrade
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-IPAdapter-Flux
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-rmbg
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inspire-pack
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ic-light
   0.1 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_controlnet_aux
   0.2 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_HiDream-Sampler
   0.2 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-crystools
   0.3 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-art-venture
   0.3 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-logicutils
   0.3 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\rgthree-comfy
   0.3 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_layerstyle
   0.4 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-Manager
   0.5 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-to-python-extension
   0.5 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\SeargeSDXL
   0.5 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-ollama-llms
   0.6 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-fluxpromptenhancer
   1.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-advancedliveportrait
   1.2 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\was-node-suite-comfyui
   1.2 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\was-ns
   1.6 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui_custom_nodes_alekpet
   1.7 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\sd-ppp
   2.0 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894
   2.5 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
   2.9 seconds (IMPORT FAILED): G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-inspyrenet-rembg
   3.4 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfy-mtb
   7.7 seconds: G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfyui-easy-use

WARNING: some comfy_api_nodes/ nodes did not import correctly. This may be because they are missing some dependencies.

IMPORT FAILED: nodes_ideogram.py
IMPORT FAILED: nodes_openai.py
IMPORT FAILED: nodes_minimax.py
IMPORT FAILED: nodes_veo2.py
IMPORT FAILED: nodes_kling.py
IMPORT FAILED: nodes_bfl.py
IMPORT FAILED: nodes_luma.py
IMPORT FAILED: nodes_recraft.py
IMPORT FAILED: nodes_pixverse.py
IMPORT FAILED: nodes_stability.py
IMPORT FAILED: nodes_pika.py
IMPORT FAILED: nodes_runway.py
IMPORT FAILED: nodes_tripo.py
IMPORT FAILED: nodes_moonvalley.py
IMPORT FAILED: nodes_rodin.py
IMPORT FAILED: nodes_gemini.py

This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
Please run the update script: update/update_comfyui.bat

FETCH ComfyRegistry Data: 20/94
Context impl SQLiteImpl.
Will assume non-transactional DDL.
No target revision found.
Starting server

To see the GUI go to: http://127.0.0.1:8188
FETCH ComfyRegistry Data: 25/94
[G:\ZComfyUI\ComfyPort\ComfyUI\custom_nodes\comfy-mtb] | INFO -> Found multiple match, we will pick the last L:\ComfyUI\models\upscale_models
['G:\\ZComfyUI\\ComfyPort\\ComfyUI\\models\\upscale_models', 'L:\\ComfyUI\\models\\upscale_models']
[Inspire Pack] IPAdapterPlus is not installed.
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
[]
[]
FETCH ComfyRegistry Data: 30/94
FETCH ComfyRegistry Data: 35/94