"""
API Routes for Semantic Features and Embedding Services
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Dict, Optional
import logging

from ..services.embedding_service import embedding_service
from ..services.prompt_enhancement_service import PromptEnhancementService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/semantic", tags=["semantic"])

# Pydantic models for request/response
class PromptEmbeddingRequest(BaseModel):
    prompt: str
    model_used: Optional[str] = None
    quality_score: Optional[float] = 0.0
    tags: Optional[List[str]] = None
    category: Optional[str] = None

    model_config = {
        "protected_namespaces": (),
    }

class SimilarPromptResponse(BaseModel):
    id: int
    prompt: str
    similarity: float
    quality_score: float
    user_rating: int
    generation_count: int
    model_used: Optional[str]
    tags: List[str]
    category: Optional[str]

    model_config = {
        "protected_namespaces": (),
    }

class SemanticSearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 10
    min_similarity: Optional[float] = 0.2

class GenerationResultRequest(BaseModel):
    generation_id: str
    prompt: str
    model_used: str
    parameters: Dict
    output_path: Optional[str] = None
    user_rating: Optional[int] = 0
    is_favorite: Optional[bool] = False

    model_config = {
        "protected_namespaces": (),
    }

class QualityUpdateRequest(BaseModel):
    prompt: str
    quality_score: Optional[float] = None
    user_rating: Optional[int] = None
    increment_usage: Optional[bool] = True

@router.post("/prompts/store")
async def store_prompt_embedding(request: PromptEmbeddingRequest):
    """Store a prompt with its embedding for semantic search"""
    try:
        prompt_id = await embedding_service.store_prompt_embedding(
            prompt=request.prompt,
            model_used=request.model_used,
            quality_score=request.quality_score,
            tags=request.tags,
            category=request.category
        )
        return {"success": True, "prompt_id": prompt_id}
    except Exception as e:
        logger.error(f"Failed to store prompt embedding: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/similar", response_model=List[SimilarPromptResponse])
async def find_similar_prompts(
    query: str = Query(..., description="Query prompt to find similar prompts for"),
    limit: int = Query(5, description="Maximum number of similar prompts to return"),
    min_similarity: float = Query(0.3, description="Minimum similarity threshold"),
    exclude_exact: bool = Query(True, description="Exclude exact matches")
):
    """Find prompts similar to the query prompt"""
    try:
        similar_prompts = await embedding_service.find_similar_prompts(
            query_prompt=query,
            limit=limit,
            min_similarity=min_similarity,
            exclude_exact=exclude_exact
        )
        
        return [
            SimilarPromptResponse(
                id=prompt['id'],
                prompt=prompt['prompt'],
                similarity=prompt['similarity'],
                quality_score=prompt['quality_score'],
                user_rating=prompt['user_rating'],
                generation_count=prompt['generation_count'],
                model_used=prompt['model_used'],
                tags=prompt['tags'],
                category=prompt['category']
            )
            for prompt in similar_prompts
        ]
    except Exception as e:
        logger.error(f"Failed to find similar prompts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search/history")
async def semantic_search_history(request: SemanticSearchRequest):
    """Search generation history semantically"""
    try:
        results = await embedding_service.semantic_search_history(
            query=request.query,
            limit=request.limit,
            min_similarity=request.min_similarity
        )
        return {"success": True, "results": results}
    except Exception as e:
        logger.error(f"Failed to search history semantically: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generations/store")
async def store_generation_result(request: GenerationResultRequest):
    """Store a generation result with its embedding"""
    try:
        await embedding_service.store_generation_result(
            generation_id=request.generation_id,
            prompt=request.prompt,
            model_used=request.model_used,
            parameters=request.parameters,
            output_path=request.output_path,
            user_rating=request.user_rating,
            is_favorite=request.is_favorite
        )
        return {"success": True}
    except Exception as e:
        logger.error(f"Failed to store generation result: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/prompts/quality")
async def update_prompt_quality(request: QualityUpdateRequest):
    """Update quality metrics for a prompt"""
    try:
        await embedding_service.update_prompt_quality(
            prompt=request.prompt,
            quality_score=request.quality_score,
            user_rating=request.user_rating,
            increment_usage=request.increment_usage
        )
        return {"success": True}
    except Exception as e:
        logger.error(f"Failed to update prompt quality: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/trending")
async def get_trending_prompts(limit: int = Query(10, description="Number of trending prompts to return")):
    """Get trending prompts based on recent usage and ratings"""
    try:
        trending = await embedding_service.get_trending_prompts(limit=limit)
        return {"success": True, "prompts": trending}
    except Exception as e:
        logger.error(f"Failed to get trending prompts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/categorize")
async def auto_categorize_prompt(prompt: str = Query(..., description="Prompt to categorize")):
    """Automatically categorize a prompt based on similar prompts"""
    try:
        category = await embedding_service.auto_categorize_prompt(prompt)
        return {"success": True, "category": category}
    except Exception as e:
        logger.error(f"Failed to categorize prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/suggestions")
async def get_prompt_suggestions(
    prompt: str = Query(..., description="Partial prompt to get suggestions for"),
    limit: int = Query(5, description="Number of suggestions to return")
):
    """Get prompt suggestions based on semantic similarity"""
    try:
        enhancement_service = PromptEnhancementService()
        suggestions = await enhancement_service.get_prompt_suggestions(prompt, limit)
        return {"success": True, "suggestions": suggestions}
    except Exception as e:
        logger.error(f"Failed to get prompt suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/analyze")
async def analyze_prompt_quality(prompt: str = Query(..., description="Prompt to analyze")):
    """Analyze prompt quality and suggest improvements"""
    try:
        enhancement_service = PromptEnhancementService()
        analysis = await enhancement_service.analyze_prompt_quality(prompt)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        logger.error(f"Failed to analyze prompt quality: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/prompts/context")
async def get_enhancement_context(prompt: str = Query(..., description="Prompt to get enhancement context for")):
    """Get context for enhancing a prompt based on similar successful prompts"""
    try:
        context = await embedding_service.get_enhanced_context_for_prompt(prompt)
        return {"success": True, "context": context}
    except Exception as e:
        logger.error(f"Failed to get enhancement context: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def semantic_health_check():
    """Health check for semantic services"""
    try:
        # Test embedding service
        test_embedding = await embedding_service.get_embedding("test")
        return {
            "success": True,
            "embedding_service": "operational",
            "embedding_dimensions": len(test_embedding),
            "database": "connected"
        }
    except Exception as e:
        logger.error(f"Semantic health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
