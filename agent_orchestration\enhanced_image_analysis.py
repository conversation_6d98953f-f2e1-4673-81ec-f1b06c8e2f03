#!/usr/bin/env python3
"""
Enhanced Image Expert Analysis using Enhanced Base Agent
Leverages all the advanced capabilities of the enhanced base agent system
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# Add framework and scripts to path
sys.path.insert(0, str(Path(__file__).parent))
sys.path.insert(0, str(Path(__file__).parent / 'scripts'))

from scripts.image_expert import EnhancedImageExpertAgent

async def main():
    """Execute enhanced image analysis with all advanced capabilities."""
    print("[ENHANCED] Image Expert Agent - Advanced Analysis Execution")
    print("=" * 70)
    print(f"Target Folder: G:\\ZComfyUI\\comfyui-custom-frontend\\frontend\\Image_Analysis\\")
    print(f"Verified Workflows: G:\\ZComfyUI\\comfyui-custom-frontend\\verified_workflows\\")
    print(f"Enhanced Features: 25+ MCP Tools, AI Analysis, ML Integration")
    print(f"Execution Time: {datetime.now().isoformat()}")
    print("=" * 70)
    
    # Create comprehensive context for enhanced base agent
    enhanced_context = {
        "agent": {
            "name": "enhanced-image-expert",
            "version": "3.0",
            "type": "image_analysis_specialist",
            "capabilities": [
                "advanced_image_analysis",
                "workflow_extraction",
                "style_guide_generation", 
                "semantic_search",
                "ml_analysis",
                "autonomous_problem_solving"
            ]
        },
        "task": {
            "name": "comprehensive_image_analysis_with_workflow_extraction",
            "priority": "high",
            "parameters": {
                "target_folder": "G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis/",
                "verified_workflows_folder": "G:/ZComfyUI/comfyui-custom-frontend/verified_workflows/",
                "analysis_depth": "comprehensive",
                "enable_ml_analysis": True,
                "enable_semantic_search": True,
                "enable_autonomous_mode": True,
                "workflow_extraction_advanced": True,
                "style_learning_enabled": True,
                "performance_optimization": True
            }
        },
        "config": {
            "data_dir": str(Path(__file__).parent / "data"),
            "cache_dir": str(Path(__file__).parent / "data" / "cache"),
            "databases": ["image_analysis.db"],
            "project_root": "G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis/",
            "safety_mode": "supervised",
            "rollback_enabled": True,
            "performance_tracking": True,
            "ml_models_enabled": True,
            "semantic_embedding_model": "all-MiniLM-L6-v2"
        },
        "knowledge_bases": {
            "art_styles": str(Path(__file__).parent / "knowledge_bases" / "art_styles_reference.md"),
            "generation_techniques": str(Path(__file__).parent / "knowledge_bases" / "generation_techniques.md"),
            "contemporary_techniques": str(Path(__file__).parent / "knowledge_bases" / "contemporary_digital_techniques.md")
        },
        "hardware": {
            "gpu": "RTX_4070_Ti_SUPER",
            "memory": "64GB",
            "optimization_target": "performance"
        },
        "models": {
            "primary": ["Flux Dev", "SDXL Base", "SD 1.5"],
            "specialized": ["ControlNet", "IPAdapter", "AnimateDiff"]
        },
        "output_specifications": {
            "generate_enhanced_analysis": True,
            "create_ml_insights": True,
            "semantic_categorization": True,
            "predictive_recommendations": True,
            "workflow_optimization_suggestions": True
        }
    }
    
    # Create data directory structure
    data_dir = Path(__file__).parent / "data"
    cache_dir = data_dir / "cache"
    data_dir.mkdir(exist_ok=True)
    cache_dir.mkdir(exist_ok=True)
    
    try:
        print("\n[INIT] Initializing Enhanced Image Expert with advanced capabilities...")
        
        # Create enhanced agent instance
        image_expert = EnhancedImageExpertAgent(enhanced_context)
        
        print(f"[READY] Agent initialized with:")
        print(f"  - MCP Tools: {len(image_expert.mcp_tools) if hasattr(image_expert, 'mcp_tools') else 'Loading...'}")
        print(f"  - Knowledge Bases: {len(enhanced_context['knowledge_bases'])}")
        print(f"  - ML Models: {'Enabled' if enhanced_context['config']['ml_models_enabled'] else 'Disabled'}")
        print(f"  - Autonomous Mode: {'Enabled' if enhanced_context['task']['parameters']['enable_autonomous_mode'] else 'Disabled'}")
        
        print("\n[EXECUTE] Starting comprehensive enhanced analysis...")
        
        # Execute with enhanced capabilities using the enhanced base agent method
        result = await image_expert.execute_task()
        
        # Process and display enhanced results
        if result.status == "success":
            print(f"\n[SUCCESS] Enhanced image analysis completed!")
            
            # Display enhanced statistics
            if hasattr(result, 'data') and result.data:
                enhanced_stats = result.data.get("enhanced_statistics", {})
                print(f"\n[ENHANCED STATS] Advanced Analysis Results:")
                print(f"  - Total images processed: {enhanced_stats.get('total_images', 'N/A')}")
                print(f"  - Workflows extracted: {enhanced_stats.get('workflows_extracted', 'N/A')}")
                print(f"  - ML confidence score: {enhanced_stats.get('ml_confidence', 'N/A')}")
                print(f"  - Semantic categories: {enhanced_stats.get('semantic_categories', 'N/A')}")
                print(f"  - Style patterns detected: {enhanced_stats.get('style_patterns', 'N/A')}")
                print(f"  - Performance optimizations: {enhanced_stats.get('optimizations_applied', 'N/A')}")
                
                # Display ML insights
                ml_insights = result.data.get("ml_insights", {})
                if ml_insights:
                    print(f"\n[AI INSIGHTS] Machine Learning Analysis:")
                    for insight_type, insights in ml_insights.items():
                        if insights:
                            print(f"  - {insight_type}: {insights}")
                
                # Display autonomous recommendations
                autonomous_recs = result.data.get("autonomous_recommendations", [])
                if autonomous_recs:
                    print(f"\n[AUTONOMOUS] AI-Generated Recommendations:")
                    for i, rec in enumerate(autonomous_recs[:5], 1):
                        print(f"  {i}. {rec}")
                
                # Display enhanced output files
                output_files = result.data.get("enhanced_output_files", [])
                if output_files:
                    print(f"\n[OUTPUTS] Enhanced Analysis Files:")
                    for file_path in output_files:
                        print(f"  - {file_path}")
            
        else:
            print(f"[ERROR] Enhanced analysis failed: {result.error}")
            if hasattr(result, 'data') and result.data:
                print(f"Error details: {json.dumps(result.data, indent=2)}")
        
        # Display performance metrics
        if hasattr(image_expert, 'performance_data') and image_expert.performance_data:
            print(f"\n[PERFORMANCE] Execution Metrics:")
            perf_data = image_expert.performance_data
            for metric, value in perf_data.items():
                print(f"  - {metric}: {value}")
        
        print(f"\n[COMPLETE] Enhanced Image Expert analysis finished")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n[CRITICAL ERROR] Enhanced analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)