#!/usr/bin/env python3
"""
Dependency Orchestrator Agent

Elite dependency resolution specialist for complex multi-environment projects
with focus on preventing conflicts and optimizing system performance.
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re

# Import base agent functionality
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class DependencyOrchestratorAgent(BaseAgent):
    """
    Dependency Orchestrator Agent implementation.
    
    Handles environment orchestration, dependency analysis, conflict resolution,
    and proactive optimization across the ComfyUI Frontend project.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Environment paths
        self.frontend_path = self.project_root / "frontend"
        self.backend_path = self.project_root / "backend"
        self.package_json_path = self.frontend_path / "package.json"
        self.requirements_path = self.backend_path / "requirements.txt"
        self.backend_venv_path = self.project_root / "Backvenv_backup"
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific dependency orchestrator task."""
        task_name = self.task_name
        
        if task_name == "resolve_conflicts":
            return await self.resolve_conflicts()
        elif task_name == "environment_audit":
            return await self.environment_audit()
        elif task_name == "setup_validation":
            return await self.setup_validation()
        else:
            raise ValueError(f"Unknown task: {task_name}")
    
    async def resolve_conflicts(self) -> Dict[str, Any]:
        """Analyze and resolve dependency conflicts across environments."""
        self.log_info("Starting dependency conflict resolution")
        
        strict_mode = self.parameters.get('strict', False)
        report_mode = self.parameters.get('report_mode', 'summary')
        target_env = self.parameters.get('target_environment', 'all')
        auto_fix = self.parameters.get('auto_fix', True)
        
        conflicts = []
        resolutions = []
        
        # Check existing solutions in knowledge base
        existing_solutions = self.find_existing_solutions("dependency conflicts version mismatch")
        if existing_solutions:
            self.log_info(f"Found {len(existing_solutions)} existing solutions in knowledge base")
            for solution in existing_solutions[:3]:  # Top 3 most relevant
                self.add_recommendation(
                    f"Consider existing solution from {solution['filename']}",
                    solution['solution_block'],
                    'high'
                )
        
        # Analyze frontend dependencies
        if target_env in ['frontend', 'all']:
            frontend_conflicts = await self.analyze_frontend_dependencies()
            conflicts.extend(frontend_conflicts)
            
            # Apply fixes if requested
            if auto_fix and frontend_conflicts:
                frontend_fixes = await self.fix_frontend_conflicts(frontend_conflicts)
                resolutions.extend(frontend_fixes)
        
        # Analyze backend dependencies
        if target_env in ['backend', 'all']:
            backend_conflicts = await self.analyze_backend_dependencies()
            conflicts.extend(backend_conflicts)
            
            # Apply fixes if requested
            if auto_fix and backend_conflicts:
                backend_fixes = await self.fix_backend_conflicts(backend_conflicts)
                resolutions.extend(backend_fixes)
        
        # Cross-environment analysis
        if target_env == 'all':
            cross_env_issues = await self.analyze_cross_environment_compatibility()
            conflicts.extend(cross_env_issues)
        
        # Generate findings and recommendations
        for conflict in conflicts:
            self.add_finding(
                'dependency_conflict',
                conflict.get('severity', 'medium'),
                conflict.get('title', 'Dependency conflict detected'),
                conflict.get('description', ''),
                conflict.get('resolution', 'Manual review required')
            )
        
        # Set metrics
        self.set_metric('total_conflicts', len(conflicts))
        self.set_metric('resolved_conflicts', len(resolutions))
        self.set_metric('auto_fix_rate', len(resolutions) / max(len(conflicts), 1))
        self.set_metric('strict_mode', strict_mode)
        
        # Update knowledge base with new findings
        if conflicts and auto_fix:
            problem_desc = f"Dependency conflicts in {target_env} environment"
            solution_desc = f"Resolved {len(resolutions)} conflicts:\n" + \
                          "\n".join([f"- {r['description']}" for r in resolutions])
            
            self.update_knowledge_base(
                "Dependency_Resolution_Solutions.md",
                problem_desc,
                solution_desc
            )
        
        return {
            'conflicts_found': len(conflicts),
            'conflicts_resolved': len(resolutions),
            'conflicts_details': conflicts if report_mode in ['full', 'detailed'] else conflicts[:5],
            'resolutions_applied': resolutions,
            'environment_analyzed': target_env,
            'strict_mode_enabled': strict_mode,
            'auto_fix_enabled': auto_fix
        }
    
    async def environment_audit(self) -> Dict[str, Any]:
        """Comprehensive audit of all project environments."""
        self.log_info("Starting comprehensive environment audit")
        
        include_security = self.parameters.get('include_security', True)
        performance_analysis = self.parameters.get('performance_analysis', False)
        
        audit_results = {}
        
        # Audit frontend environment
        frontend_audit = await self.audit_frontend_environment()
        audit_results['frontend'] = frontend_audit
        
        # Audit backend environment
        backend_audit = await self.audit_backend_environment()
        audit_results['backend'] = backend_audit
        
        # Security audit if requested
        if include_security:
            security_audit = await self.security_audit()
            audit_results['security'] = security_audit
        
        # Performance analysis if requested
        if performance_analysis:
            performance_audit = await self.performance_analysis()
            audit_results['performance'] = performance_audit
        
        # Generate overall health score
        health_score = self.calculate_environment_health(audit_results)
        self.set_metric('environment_health_score', health_score)
        
        # Generate recommendations
        self.generate_audit_recommendations(audit_results)
        
        return {
            'audit_results': audit_results,
            'health_score': health_score,
            'security_included': include_security,
            'performance_included': performance_analysis
        }
    
    async def setup_validation(self) -> Dict[str, Any]:
        """Validate complete environment setup for new developers."""
        self.log_info("Starting setup validation for new developers")
        
        create_backup = self.parameters.get('create_backup', True)
        test_connections = self.parameters.get('test_connections', True)
        
        validation_results = []
        
        # Create backup if requested
        if create_backup:
            backup_result = await self.create_environment_backup()
            validation_results.append(backup_result)
        
        # Validate Node.js environment
        node_validation = await self.validate_nodejs_environment()
        validation_results.append(node_validation)
        
        # Validate Python environment
        python_validation = await self.validate_python_environment()
        validation_results.append(python_validation)
        
        # Validate project structure
        structure_validation = await self.validate_project_structure()
        validation_results.append(structure_validation)
        
        # Test connections if requested
        if test_connections:
            connection_tests = await self.test_service_connections()
            validation_results.extend(connection_tests)
        
        # Generate setup guide for any missing components
        setup_guide = await self.generate_setup_guide(validation_results)
        
        # Calculate overall setup completeness
        passed_checks = sum(1 for result in validation_results if result.get('success', False))
        total_checks = len(validation_results)
        completeness = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
        
        self.set_metric('setup_completeness', completeness)
        self.set_metric('passed_checks', passed_checks)
        self.set_metric('total_checks', total_checks)
        
        return {
            'validation_results': validation_results,
            'setup_completeness': completeness,
            'setup_guide': setup_guide,
            'backup_created': create_backup,
            'connections_tested': test_connections
        }
    
    # Helper methods for dependency analysis
    
    async def analyze_frontend_dependencies(self) -> List[Dict[str, Any]]:
        """Analyze frontend dependencies for conflicts."""
        conflicts = []
        
        if not self.package_json_path.exists():
            conflicts.append({
                'severity': 'critical',
                'title': 'package.json not found',
                'description': f'Frontend package.json not found at {self.package_json_path}',
                'resolution': 'Ensure frontend directory is properly set up'
            })
            return conflicts
        
        try:
            # Read package.json
            with open(self.package_json_path, 'r') as f:
                package_data = json.load(f)
            
            # Check for common conflict patterns
            dependencies = package_data.get('dependencies', {})
            dev_dependencies = package_data.get('devDependencies', {})
            
            # Check for version conflicts between deps and devDeps
            common_packages = set(dependencies.keys()) & set(dev_dependencies.keys())
            for package in common_packages:
                if dependencies[package] != dev_dependencies[package]:
                    conflicts.append({
                        'severity': 'medium',
                        'title': f'Version mismatch for {package}',
                        'description': f'Dependency version: {dependencies[package]}, DevDependency version: {dev_dependencies[package]}',
                        'resolution': f'Align versions for {package}'
                    })
            
            # Check for outdated React/Next.js versions
            if 'next' in dependencies:
                next_version = dependencies['next'].replace('^', '').replace('~', '')
                if next_version.startswith('13.'):
                    self.add_recommendation(
                        'Consider upgrading Next.js',
                        f'Current version {next_version}, consider upgrading to Next.js 14 for better performance',
                        'medium'
                    )
            
        except Exception as e:
            conflicts.append({
                'severity': 'high',
                'title': 'Error reading package.json',
                'description': f'Failed to parse package.json: {str(e)}',
                'resolution': 'Fix package.json syntax errors'
            })
        
        return conflicts
    
    async def analyze_backend_dependencies(self) -> List[Dict[str, Any]]:
        """Analyze backend dependencies for conflicts."""
        conflicts = []
        
        # Check if requirements.txt exists
        if not self.requirements_path.exists():
            conflicts.append({
                'severity': 'critical',
                'title': 'requirements.txt not found',
                'description': f'Backend requirements.txt not found at {self.requirements_path}',
                'resolution': 'Create requirements.txt file'
            })
            return conflicts
        
        try:
            # Read requirements.txt
            with open(self.requirements_path, 'r') as f:
                requirements = f.read().strip().split('\n')
            
            # Parse requirements
            parsed_reqs = {}
            for req in requirements:
                if req.strip() and not req.startswith('#'):
                    # Simple parsing - in production this would be more robust
                    if '==' in req:
                        package, version = req.split('==', 1)
                        parsed_reqs[package.strip()] = version.strip()
                    elif '>=' in req:
                        package = req.split('>=')[0].strip()
                        parsed_reqs[package] = req.split('>=')[1].strip()
            
            # Check for known problematic combinations
            if 'torch' in parsed_reqs and 'tensorflow' in parsed_reqs:
                conflicts.append({
                    'severity': 'high',
                    'title': 'PyTorch and TensorFlow conflict',
                    'description': 'Both PyTorch and TensorFlow are installed, which can cause conflicts',
                    'resolution': 'Consider using only one deep learning framework'
                })
            
            # Check Python version compatibility
            python_version = await self.get_python_version()
            if python_version:
                if python_version.startswith('3.13') and 'torch' in parsed_reqs:
                    self.add_recommendation(
                        'Python 3.13 compatibility check',
                        'Verify that PyTorch version is compatible with Python 3.13',
                        'medium'
                    )
        
        except Exception as e:
            conflicts.append({
                'severity': 'high',
                'title': 'Error reading requirements.txt',
                'description': f'Failed to parse requirements.txt: {str(e)}',
                'resolution': 'Fix requirements.txt syntax'
            })
        
        return conflicts
    
    async def analyze_cross_environment_compatibility(self) -> List[Dict[str, Any]]:
        """Analyze compatibility between different environments."""
        issues = []
        
        # Check if both environments exist
        frontend_exists = self.package_json_path.exists()
        backend_exists = self.requirements_path.exists()
        
        if not frontend_exists or not backend_exists:
            issues.append({
                'severity': 'critical',
                'title': 'Missing environment configuration',
                'description': f'Frontend exists: {frontend_exists}, Backend exists: {backend_exists}',
                'resolution': 'Ensure both frontend and backend environments are properly configured'
            })
        
        # Check Node.js and Python version compatibility
        node_version = await self.get_node_version()
        python_version = await self.get_python_version()
        
        if node_version and python_version:
            self.set_metric('node_version', node_version)
            self.set_metric('python_version', python_version)
            
            # Check for known compatibility issues
            if node_version.startswith('18.') and python_version.startswith('3.13.'):
                self.add_recommendation(
                    'Environment versions validated',
                    f'Node.js {node_version} and Python {python_version} are compatible',
                    'low'
                )
        
        return issues
    
    async def fix_frontend_conflicts(self, conflicts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Attempt to fix frontend dependency conflicts."""
        resolutions = []
        
        for conflict in conflicts:
            if 'Version mismatch' in conflict['title']:
                # This would implement actual fixes - for now just simulate
                resolutions.append({
                    'conflict': conflict['title'],
                    'description': f"Simulated fix for {conflict['title']}",
                    'action_taken': 'version_alignment'
                })
        
        return resolutions
    
    async def fix_backend_conflicts(self, conflicts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Attempt to fix backend dependency conflicts."""
        resolutions = []
        
        for conflict in conflicts:
            if 'PyTorch and TensorFlow' in conflict['title']:
                # This would implement actual fixes - for now just simulate
                resolutions.append({
                    'conflict': conflict['title'],
                    'description': "Recommended removal of conflicting framework",
                    'action_taken': 'conflict_warning'
                })
        
        return resolutions
    
    # Environment audit methods
    
    async def audit_frontend_environment(self) -> Dict[str, Any]:
        """Audit frontend environment health."""
        audit = {'status': 'healthy', 'issues': [], 'metrics': {}}
        
        # Check Node.js version
        node_version = await self.get_node_version()
        if node_version:
            audit['metrics']['node_version'] = node_version
        else:
            audit['issues'].append('Node.js not found or not in PATH')
            audit['status'] = 'unhealthy'
        
        # Check npm version
        npm_version = await self.get_npm_version()
        if npm_version:
            audit['metrics']['npm_version'] = npm_version
        
        # Check if node_modules exists
        node_modules_path = self.frontend_path / "node_modules"
        if node_modules_path.exists():
            audit['metrics']['dependencies_installed'] = True
        else:
            audit['issues'].append('node_modules directory not found - dependencies may not be installed')
        
        return audit
    
    async def audit_backend_environment(self) -> Dict[str, Any]:
        """Audit backend environment health."""
        audit = {'status': 'healthy', 'issues': [], 'metrics': {}}
        
        # Check Python version
        python_version = await self.get_python_version()
        if python_version:
            audit['metrics']['python_version'] = python_version
        else:
            audit['issues'].append('Python not found or not in PATH')
            audit['status'] = 'unhealthy'
        
        # Check virtual environment
        if self.backend_venv_path.exists():
            audit['metrics']['virtual_env_exists'] = True
        else:
            audit['issues'].append('Backend virtual environment not found')
        
        return audit
    
    async def security_audit(self) -> Dict[str, Any]:
        """Perform security audit of dependencies."""
        security = {'vulnerabilities': [], 'recommendations': []}
        
        # This would implement actual security scanning
        # For now, provide general recommendations
        security['recommendations'].extend([
            'Run npm audit for frontend vulnerabilities',
            'Use pip-audit for Python package vulnerabilities',
            'Keep dependencies updated regularly',
            'Use dependency scanning in CI/CD pipeline'
        ])
        
        return security
    
    async def performance_analysis(self) -> Dict[str, Any]:
        """Analyze performance characteristics of dependencies."""
        performance = {'bundle_size_analysis': {}, 'recommendations': []}
        
        # This would implement actual performance analysis
        performance['recommendations'].extend([
            'Consider tree-shaking for unused code elimination',
            'Analyze bundle size impact of new dependencies',
            'Use dynamic imports for code splitting'
        ])
        
        return performance
    
    # Setup validation methods
    
    async def create_environment_backup(self) -> Dict[str, Any]:
        """Create backup of current environment configuration."""
        try:
            backup_dir = self.project_root / "backups" / f"env_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # This would implement actual backup creation
            return {
                'success': True,
                'backup_location': str(backup_dir),
                'description': 'Environment backup created successfully'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'description': 'Failed to create environment backup'
            }
    
    async def validate_nodejs_environment(self) -> Dict[str, Any]:
        """Validate Node.js environment setup."""
        try:
            node_version = await self.get_node_version()
            npm_version = await self.get_npm_version()
            
            if not node_version:
                return {
                    'success': False,
                    'description': 'Node.js not found',
                    'recommendation': 'Install Node.js 18 or later'
                }
            
            # Check version compatibility
            major_version = int(node_version.split('.')[0])
            if major_version < 16:
                return {
                    'success': False,
                    'description': f'Node.js version {node_version} is too old',
                    'recommendation': 'Upgrade to Node.js 18 or later'
                }
            
            return {
                'success': True,
                'description': f'Node.js {node_version}, npm {npm_version}',
                'versions': {'node': node_version, 'npm': npm_version}
            }
            
        except Exception as e:
            return {
                'success': False,
                'description': f'Error validating Node.js environment: {str(e)}',
                'recommendation': 'Check Node.js installation'
            }
    
    async def validate_python_environment(self) -> Dict[str, Any]:
        """Validate Python environment setup."""
        try:
            python_version = await self.get_python_version()
            
            if not python_version:
                return {
                    'success': False,
                    'description': 'Python not found',
                    'recommendation': 'Install Python 3.8 or later'
                }
            
            # Check version compatibility
            version_parts = python_version.split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            
            if major < 3 or (major == 3 and minor < 8):
                return {
                    'success': False,
                    'description': f'Python version {python_version} is too old',
                    'recommendation': 'Upgrade to Python 3.8 or later'
                }
            
            # Check virtual environment
            venv_exists = self.backend_venv_path.exists()
            
            return {
                'success': True,
                'description': f'Python {python_version}, venv exists: {venv_exists}',
                'versions': {'python': python_version},
                'virtual_env': venv_exists
            }
            
        except Exception as e:
            return {
                'success': False,
                'description': f'Error validating Python environment: {str(e)}',
                'recommendation': 'Check Python installation'
            }
    
    async def validate_project_structure(self) -> Dict[str, Any]:
        """Validate project directory structure."""
        required_paths = [
            self.frontend_path,
            self.backend_path,
            self.package_json_path,
            self.requirements_path
        ]
        
        missing_paths = []
        for path in required_paths:
            if not path.exists():
                missing_paths.append(str(path))
        
        if missing_paths:
            return {
                'success': False,
                'description': f'Missing required paths: {", ".join(missing_paths)}',
                'recommendation': 'Ensure project structure is complete'
            }
        
        return {
            'success': True,
            'description': 'Project structure validated',
            'required_paths': [str(p) for p in required_paths]
        }
    
    async def test_service_connections(self) -> List[Dict[str, Any]]:
        """Test connections to required services."""
        connection_tests = []
        
        # Test local development server ports
        ports_to_test = [3003, 8000, 8188]  # Frontend, Backend, ComfyUI
        
        for port in ports_to_test:
            test_result = await self.test_port_connection('localhost', port)
            connection_tests.append(test_result)
        
        return connection_tests
    
    async def test_port_connection(self, host: str, port: int) -> Dict[str, Any]:
        """Test connection to a specific port."""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                return {
                    'success': True,
                    'description': f'Port {port} is accessible',
                    'service': self.get_service_name(port),
                    'host': host,
                    'port': port
                }
            else:
                return {
                    'success': False,
                    'description': f'Port {port} is not accessible',
                    'service': self.get_service_name(port),
                    'host': host,
                    'port': port,
                    'recommendation': f'Start the {self.get_service_name(port)} service'
                }
                
        except Exception as e:
            return {
                'success': False,
                'description': f'Error testing port {port}: {str(e)}',
                'service': self.get_service_name(port),
                'host': host,
                'port': port,
                'recommendation': 'Check network configuration'
            }
    
    def get_service_name(self, port: int) -> str:
        """Get service name for a port."""
        service_map = {
            3003: 'Frontend (Next.js)',
            8000: 'Backend (FastAPI)',
            8188: 'ComfyUI'
        }
        return service_map.get(port, f'Unknown service on port {port}')
    
    # Utility methods
    
    async def get_node_version(self) -> Optional[str]:
        """Get Node.js version."""
        try:
            result = await self.run_command(['node', '--version'])
            if result['success']:
                return result['output'].strip().replace('v', '')
        except:
            pass
        return None
    
    async def get_npm_version(self) -> Optional[str]:
        """Get npm version."""
        try:
            result = await self.run_command(['npm', '--version'])
            if result['success']:
                return result['output'].strip()
        except:
            pass
        return None
    
    async def get_python_version(self) -> Optional[str]:
        """Get Python version."""
        try:
            result = await self.run_command(['python', '--version'])
            if result['success']:
                return result['output'].strip().replace('Python ', '')
        except:
            pass
        return None
    
    async def run_command(self, command: List[str]) -> Dict[str, Any]:
        """Run a shell command and return the result."""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                'success': process.returncode == 0,
                'output': stdout.decode().strip(),
                'error': stderr.decode().strip(),
                'return_code': process.returncode
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'output': '',
                'return_code': -1
            }
    
    def calculate_environment_health(self, audit_results: Dict[str, Any]) -> float:
        """Calculate overall environment health score (0-100)."""
        scores = []
        
        for env_name, env_audit in audit_results.items():
            if env_name in ['frontend', 'backend']:
                if env_audit.get('status') == 'healthy':
                    scores.append(100)
                elif len(env_audit.get('issues', [])) == 0:
                    scores.append(90)
                else:
                    # Deduct points for each issue
                    deduction = min(len(env_audit['issues']) * 20, 80)
                    scores.append(max(100 - deduction, 20))
        
        return sum(scores) / len(scores) if scores else 0
    
    def generate_audit_recommendations(self, audit_results: Dict[str, Any]) -> None:
        """Generate recommendations based on audit results."""
        for env_name, env_audit in audit_results.items():
            issues = env_audit.get('issues', [])
            for issue in issues:
                self.add_recommendation(
                    f'Fix {env_name} environment issue',
                    issue,
                    'high' if 'not found' in issue else 'medium'
                )
    
    async def generate_setup_guide(self, validation_results: List[Dict[str, Any]]) -> List[str]:
        """Generate setup guide based on validation results."""
        guide_steps = []
        
        for result in validation_results:
            if not result.get('success', True) and result.get('recommendation'):
                guide_steps.append(result['recommendation'])
        
        if not guide_steps:
            guide_steps.append("Environment setup is complete!")
        
        return guide_steps


# Entry point for the orchestration system
async def execute(context: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the dependency orchestrator agent."""
    agent = DependencyOrchestratorAgent(context)
    return await agent.run()


if __name__ == "__main__":
    # For testing purposes
    import json
    
    if len(sys.argv) > 1:
        context_file = sys.argv[1]
        with open(context_file, 'r') as f:
            context = json.load(f)
        
        result = asyncio.run(execute(context))
        print(json.dumps(result, indent=2, default=str))
    else:
        print("Usage: python dependency_orchestrator.py <context_file.json>")