from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ComfyUI Custom Frontend"
    
    # Service URLs (configurable via environment)
    COMFYUI_HOST: str = "localhost"  # Use localhost for consistency
    COMFYUI_PORT: int = 8188  # Match the actual ComfyUI port
    COMFYUI_API_URL: str = "http://localhost:8188"  # Will be set from env or computed
    COMFYUI_WS_URL: str = "ws://localhost:8188/ws"  # Will be set from env or computed
    
    # Ollama Settings
    OLLAMA_HOST: str = "127.0.0.1"
    OLLAMA_PORT: int = 11434
    OLLAMA_API_URL: str = "http://localhost:11434"  # Will be set from env or computed
    OLLAMA_DEFAULT_MODEL: str = "gpt-oss:latest"
    
    # Middleware Settings
    ENABLE_MIDDLEWARE_MODE: bool = True
    ENABLE_SERVICE_DISCOVERY: bool = True
    ENABLE_AUTO_RECONNECT: bool = True
    API_TIMEOUT: int = 30000  # milliseconds
    WS_RECONNECT_INTERVAL: int = 5000  # milliseconds
    WS_MAX_RETRIES: int = 10
    
    # Hardware Optimization Settings
    MAX_CONCURRENT_GENERATIONS: int = 2  # For RTX 4070 Ti SUPER
    VRAM_BUFFER_GB: float = 2.0  # Reserve 2GB for UI operations
    MAX_IMAGE_CACHE_SIZE_GB: float = 8.0  # Use 8GB for image caching
    
    # File Paths - Configurable model and output directories
    # These align with the frontend configuration structure
    MODEL_BASE_PATH: str = "G:/ZComfyUI/ComfyPort/ComfyUI/models"
    OUTPUT_BASE_PATH: str = "G:/ZComfyUI/ComfyPort/ComfyUI/output"
    TEMP_BASE_PATH: str = "G:/ZComfyUI/ComfyPort/ComfyUI/temp"
    CUSTOM_NODES_PATH: str = "G:/ZComfyUI/ComfyPort/ComfyUI/custom_nodes"
    
    # Legacy path support for backward compatibility
    LEGACY_MODEL_PATH: str = "L:/ComfyUI/models"
    
    # Dynamic model paths (computed from base paths)
    @property
    def CHECKPOINTS_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/checkpoints"
    
    @property
    def LORAS_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/loras"
    
    @property
    def CONTROLNET_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/controlnet"
    
    @property
    def UPSCALE_MODELS_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/upscale_models"
    
    @property
    def VAE_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/vae"
    
    @property
    def CLIP_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/clip"
    
    @property
    def UNET_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/unet"
    
    @property
    def DIFFUSION_MODELS_PATH(self) -> str:
        return f"{self.MODEL_BASE_PATH}/diffusion_models"
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3003",
        "http://127.0.0.1:3003",
    ]
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3003",
        "http://127.0.0.1:3003",
    ]
    
    # System Monitoring
    SYSTEM_STATS_UPDATE_INTERVAL: float = 2.0  # seconds
    GPU_TEMPERATURE_WARNING: int = 80  # Celsius
    VRAM_WARNING_THRESHOLD: float = 0.9  # 90% usage
    SYSTEM_MONITOR_INTERVAL: int = 5
    SYSTEM_MONITOR_ENABLED: bool = True
    
    class Config:
        case_sensitive = True
        env_file = ".env"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Compute service URLs from host/port if not explicitly set
        if self.COMFYUI_API_URL == "http://localhost:8188":
            self.COMFYUI_API_URL = f"http://{self.COMFYUI_HOST}:{self.COMFYUI_PORT}"

        if self.COMFYUI_WS_URL == "ws://localhost:8188/ws":
            self.COMFYUI_WS_URL = f"ws://{self.COMFYUI_HOST}:{self.COMFYUI_PORT}/ws"
            
        if self.OLLAMA_API_URL == "http://localhost:11434":
            self.OLLAMA_API_URL = f"http://{self.OLLAMA_HOST}:{self.OLLAMA_PORT}"
    
    def normalize_path(self, path: str) -> str:
        """Normalize Windows path separators"""
        return str(Path(path)).replace('\\', '/')
    
    def migrate_legacy_path(self, path: str) -> str:
        """Migrate legacy L: drive paths to G: drive structure"""
        if path.startswith('L:/ComfyUI/models'):
            relative_path = path.replace('L:/ComfyUI/models/', '')
            return f"{self.MODEL_BASE_PATH}/{relative_path}"
        elif path.startswith('L:/ComfyUI/'):
            relative_path = path.replace('L:/ComfyUI/', '')
            base_path = self.MODEL_BASE_PATH.replace('/models', '')
            return f"{base_path}/{relative_path}"
        return path
    
    def is_legacy_path(self, path: str) -> bool:
        """Check if path is a legacy L: drive path"""
        return path.startswith('L:/ComfyUI/') or path.startswith('L:\\ComfyUI\\')
    
    def get_model_directories(self) -> dict:
        """Get all model directory paths with fallback resolution"""
        # Import here to avoid circular imports
        try:
            from app.utils.path_resolver import model_path_resolver
            resolved_dirs = model_path_resolver.get_all_model_directories()

            # Use resolved paths if available, fallback to configured paths
            return {
                'checkpoints': resolved_dirs.get('checkpoints') or self.CHECKPOINTS_PATH,
                'unet': resolved_dirs.get('unet') or self.UNET_PATH,
                'diffusion_models': resolved_dirs.get('diffusion_models') or self.DIFFUSION_MODELS_PATH,
                'clip': resolved_dirs.get('clip') or self.CLIP_PATH,
                'loras': resolved_dirs.get('loras') or self.LORAS_PATH,
                'vae': resolved_dirs.get('vae') or self.VAE_PATH,
                'upscale_models': resolved_dirs.get('upscale_models') or self.UPSCALE_MODELS_PATH,
                'controlnet': resolved_dirs.get('controlnet') or self.CONTROLNET_PATH,
                'embeddings': resolved_dirs.get('embeddings') or f"{self.MODEL_BASE_PATH}/embeddings",
                'hypernetworks': resolved_dirs.get('hypernetworks') or f"{self.MODEL_BASE_PATH}/hypernetworks",
                'style_models': resolved_dirs.get('style_models') or f"{self.MODEL_BASE_PATH}/style_models"
            }
        except ImportError:
            logger.warning("Path resolver not available, using configured paths")
            return {
                'checkpoints': self.CHECKPOINTS_PATH,
                'unet': self.UNET_PATH,
                'diffusion_models': self.DIFFUSION_MODELS_PATH,
                'clip': self.CLIP_PATH,
                'loras': self.LORAS_PATH,
                'vae': self.VAE_PATH,
                'upscale_models': self.UPSCALE_MODELS_PATH,
                'controlnet': self.CONTROLNET_PATH,
                'embeddings': f"{self.MODEL_BASE_PATH}/embeddings",
                'hypernetworks': f"{self.MODEL_BASE_PATH}/hypernetworks",
                'style_models': f"{self.MODEL_BASE_PATH}/style_models"
            }

    def get_working_model_base_path(self) -> Optional[str]:
        """Get the working model base path using path resolver"""
        try:
            from app.utils.path_resolver import model_path_resolver
            return model_path_resolver.get_working_model_base_path()
        except ImportError:
            logger.warning("Path resolver not available, using configured path")
            return self.MODEL_BASE_PATH if os.path.exists(self.MODEL_BASE_PATH) else None

# Enhanced settings instance with path migration support
settings = Settings()

# Utility functions for the settings
def get_service_url(service: str) -> str:
    """Get service URL with fallback support"""
    if service == 'comfyui':
        return settings.COMFYUI_API_URL
    elif service == 'ollama':
        return settings.OLLAMA_API_URL
    else:
        raise ValueError(f"Unknown service: {service}")

def get_websocket_url(service: str = 'comfyui') -> str:
    """Get WebSocket URL for service"""
    if service == 'comfyui':
        return settings.COMFYUI_WS_URL
    else:
        raise ValueError(f"Unknown service: {service}")

def is_path_allowed(path: str) -> bool:
    """Check if path is within allowed directories"""
    normalized = settings.normalize_path(path)
    allowed_prefixes = [
        settings.normalize_path(settings.MODEL_BASE_PATH),
        settings.normalize_path(settings.OUTPUT_BASE_PATH),
        settings.normalize_path(settings.TEMP_BASE_PATH),
        settings.normalize_path(settings.CUSTOM_NODES_PATH),
        # Allow legacy paths during migration
        'L:/ComfyUI/models',
        'L:/ComfyUI/output',
        'L:/ComfyUI/temp',
        'L:/ComfyUI/custom_nodes'
    ]
    
    return any(normalized.startswith(prefix) for prefix in allowed_prefixes)
