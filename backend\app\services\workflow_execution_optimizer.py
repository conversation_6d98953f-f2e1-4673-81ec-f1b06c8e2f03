"""
Workflow Execution Optimizer
Advanced optimization system for ComfyUI workflow execution paths and node processing
"""

import asyncio
import json
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3

from app.utils.centralized_logger import get_logger, log_activity, log_error

logger = get_logger()

class NodeType(Enum):
    MODEL_LOADER = "model_loader"
    VAE_LOADER = "vae_loader" 
    CLIP_LOADER = "clip_loader"
    TEXT_ENCODE = "text_encode"
    SAMPLER = "sampler"
    VAE_DECODE = "vae_decode"
    IMAGE_SAVE = "image_save"
    CONDITIONING = "conditioning"
    LATENT_IMAGE = "latent_image"
    NOISE = "noise"
    SCHEDULER = "scheduler"
    GUIDANCE = "guidance"
    UNKNOWN = "unknown"

class OptimizationPriority(Enum):
    SPEED = "speed"           # Minimize execution time
    MEMORY = "memory"         # Minimize VRAM usage
    QUALITY = "quality"       # Maximize output quality
    BALANCED = "balanced"     # Balance all factors

@dataclass
class NodeExecutionProfile:
    node_id: str
    node_type: NodeType
    execution_time_ms: float = 0.0
    vram_usage_mb: float = 0.0
    dependencies: List[str] = None
    can_cache: bool = True
    cache_size_mb: float = 0.0
    parallelizable: bool = False
    cpu_intensive: bool = False
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

@dataclass
class WorkflowOptimizationPlan:
    workflow_hash: str
    execution_order: List[str]  # Optimized node execution order
    parallel_groups: List[List[str]]  # Groups of nodes that can run in parallel
    caching_strategy: Dict[str, bool]  # Which nodes to cache
    memory_checkpoints: List[str]  # Points to free memory
    estimated_total_time_ms: float
    estimated_peak_vram_mb: float
    optimization_notes: List[str]

class WorkflowExecutionOptimizer:
    """Advanced workflow execution optimizer with dependency analysis and performance tuning"""
    
    def __init__(self, database_path: str = "data/workflow_optimization.db"):
        self.database_path = database_path
        self.node_profiles: Dict[str, NodeExecutionProfile] = {}
        self.workflow_cache: Dict[str, WorkflowOptimizationPlan] = {}
        self._initialize_database()
        self._initialize_node_type_mappings()
        
        log_activity("WORKFLOW_OPTIMIZER", "Workflow execution optimizer initialized")

    def _initialize_database(self):
        """Initialize the workflow optimization database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Node execution profiles table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS node_execution_profiles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        node_id TEXT NOT NULL,
                        node_type TEXT NOT NULL,
                        workflow_hash TEXT NOT NULL,
                        execution_time_ms REAL,
                        vram_usage_mb REAL,
                        cache_size_mb REAL,
                        execution_count INTEGER DEFAULT 1,
                        avg_execution_time_ms REAL,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(node_id, workflow_hash)
                    )
                """)
                
                # Workflow optimization plans table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS workflow_optimization_plans (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        workflow_hash TEXT UNIQUE NOT NULL,
                        model_type TEXT,
                        complexity_level TEXT,
                        optimization_priority TEXT,
                        execution_order TEXT,
                        parallel_groups TEXT,
                        caching_strategy TEXT,
                        memory_checkpoints TEXT,
                        estimated_total_time_ms REAL,
                        estimated_peak_vram_mb REAL,
                        success_rate REAL DEFAULT 1.0,
                        usage_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Workflow execution history
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS workflow_execution_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT NOT NULL,
                        workflow_hash TEXT NOT NULL,
                        optimization_plan_id INTEGER,
                        actual_execution_time_ms REAL,
                        actual_peak_vram_mb REAL,
                        success BOOLEAN,
                        error_message TEXT,
                        bottleneck_nodes TEXT,
                        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (optimization_plan_id) REFERENCES workflow_optimization_plans(id)
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "database_init_failed", f"Failed to initialize database: {str(e)}", {}, e)
            raise

    def _initialize_node_type_mappings(self):
        """Initialize node type mappings for ComfyUI nodes"""
        self.node_type_map = {
            # Model loaders
            "CheckpointLoaderSimple": NodeType.MODEL_LOADER,
            "UNETLoader": NodeType.MODEL_LOADER,
            "DiffusionModel_load": NodeType.MODEL_LOADER,
            
            # VAE loaders
            "VAELoader": NodeType.VAE_LOADER,
            "VAE_load": NodeType.VAE_LOADER,
            
            # CLIP loaders
            "CLIPLoader": NodeType.CLIP_LOADER,
            "DualCLIPLoader": NodeType.CLIP_LOADER,
            "CLIP_load": NodeType.CLIP_LOADER,
            
            # Text encoding
            "CLIPTextEncode": NodeType.TEXT_ENCODE,
            "CLIPTextEncodeSDXL": NodeType.TEXT_ENCODE,
            
            # Samplers
            "KSampler": NodeType.SAMPLER,
            "KSamplerAdvanced": NodeType.SAMPLER,
            "SamplerCustom": NodeType.SAMPLER,
            "SamplerCustomAdvanced": NodeType.SAMPLER,
            
            # VAE operations
            "VAEDecode": NodeType.VAE_DECODE,
            "VAEEncode": NodeType.VAE_DECODE,
            
            # Image operations
            "SaveImage": NodeType.IMAGE_SAVE,
            "PreviewImage": NodeType.IMAGE_SAVE,
            
            # Latent operations
            "EmptyLatentImage": NodeType.LATENT_IMAGE,
            "EmptySD3LatentImage": NodeType.LATENT_IMAGE,
            "LatentUpscale": NodeType.LATENT_IMAGE,
            
            # Noise and scheduling
            "RandomNoise": NodeType.NOISE,
            "BasicScheduler": NodeType.SCHEDULER,
            "KSamplerSelect": NodeType.SCHEDULER,
            
            # Conditioning and guidance
            "ConditioningConcat": NodeType.CONDITIONING,
            "ConditioningSetArea": NodeType.CONDITIONING,
            "FluxGuidance": NodeType.GUIDANCE,
            "BasicGuider": NodeType.GUIDANCE
        }

    def _detect_node_type(self, class_type: str) -> NodeType:
        """Detect node type from ComfyUI class type"""
        return self.node_type_map.get(class_type, NodeType.UNKNOWN)

    def _calculate_workflow_hash(self, workflow: Dict[str, Any]) -> str:
        """Calculate unique hash for workflow structure"""
        # Create a normalized representation of the workflow
        normalized = {
            "nodes": sorted([
                {"id": node.get("id"), "type": node.get("class_type", node.get("type", ""))}
                for node in workflow.get("nodes", [])
            ], key=lambda x: x["id"]),
            "links": sorted(workflow.get("links", []), key=lambda x: (x.get("from"), x.get("to")))
        }
        
        workflow_str = json.dumps(normalized, sort_keys=True)
        return hashlib.sha256(workflow_str.encode()).hexdigest()[:16]

    async def optimize_workflow_execution(self, workflow: Dict[str, Any], 
                                         generation_params: Dict[str, Any],
                                         priority: OptimizationPriority = OptimizationPriority.BALANCED) -> WorkflowOptimizationPlan:
        """Generate optimized execution plan for workflow"""
        
        workflow_hash = self._calculate_workflow_hash(workflow)
        
        log_activity("WORKFLOW_OPTIMIZER", f"Optimizing workflow execution", {
            "workflow_hash": workflow_hash,
            "priority": priority.value,
            "node_count": len(workflow.get("nodes", []))
        })
        
        # Check cache first
        cached_plan = await self._get_cached_optimization_plan(workflow_hash, priority)
        if cached_plan:
            log_activity("WORKFLOW_OPTIMIZER", f"Using cached optimization plan for {workflow_hash}")
            return cached_plan
        
        # Analyze workflow structure
        node_profiles = await self._analyze_workflow_nodes(workflow, generation_params)
        dependency_graph = self._build_dependency_graph(workflow)
        
        # Generate optimization plan
        plan = await self._generate_optimization_plan(
            workflow_hash, node_profiles, dependency_graph, priority, generation_params
        )
        
        # Cache the plan
        await self._cache_optimization_plan(plan, priority, generation_params)
        
        log_activity("WORKFLOW_OPTIMIZER", f"Generated optimization plan for {workflow_hash}", {
            "estimated_time_ms": plan.estimated_total_time_ms,
            "estimated_vram_mb": plan.estimated_peak_vram_mb,
            "parallel_groups": len(plan.parallel_groups)
        })
        
        return plan

    async def _analyze_workflow_nodes(self, workflow: Dict[str, Any], 
                                    generation_params: Dict[str, Any]) -> Dict[str, NodeExecutionProfile]:
        """Analyze workflow nodes and create execution profiles"""
        
        node_profiles = {}
        nodes = workflow.get("nodes", [])
        
        for node in nodes:
            node_id = str(node.get("id", "unknown"))
            class_type = node.get("class_type", node.get("type", ""))
            node_type = self._detect_node_type(class_type)
            
            # Get historical performance data
            historical_data = await self._get_node_historical_performance(node_id, class_type, generation_params)
            
            profile = NodeExecutionProfile(
                node_id=node_id,
                node_type=node_type,
                execution_time_ms=historical_data.get("avg_execution_time_ms", self._estimate_node_execution_time(node_type, generation_params)),
                vram_usage_mb=historical_data.get("avg_vram_usage_mb", self._estimate_node_vram_usage(node_type, generation_params)),
                can_cache=self._can_node_be_cached(node_type),
                cache_size_mb=self._estimate_cache_size(node_type, generation_params),
                parallelizable=self._is_node_parallelizable(node_type),
                cpu_intensive=self._is_node_cpu_intensive(node_type)
            )
            
            node_profiles[node_id] = profile
        
        return node_profiles

    def _estimate_node_execution_time(self, node_type: NodeType, params: Dict[str, Any]) -> float:
        """Estimate node execution time based on type and parameters"""
        
        base_times = {
            NodeType.MODEL_LOADER: 2000.0,    # 2 seconds
            NodeType.VAE_LOADER: 500.0,       # 0.5 seconds
            NodeType.CLIP_LOADER: 800.0,      # 0.8 seconds
            NodeType.TEXT_ENCODE: 100.0,      # 0.1 seconds
            NodeType.SAMPLER: 15000.0,        # 15 seconds (varies greatly)
            NodeType.VAE_DECODE: 1000.0,      # 1 second
            NodeType.IMAGE_SAVE: 200.0,       # 0.2 seconds
            NodeType.CONDITIONING: 50.0,      # 0.05 seconds
            NodeType.LATENT_IMAGE: 10.0,      # 0.01 seconds
            NodeType.NOISE: 5.0,              # 0.005 seconds
            NodeType.SCHEDULER: 1.0,          # 0.001 seconds
            NodeType.GUIDANCE: 1.0,           # 0.001 seconds
            NodeType.UNKNOWN: 100.0           # Default
        }
        
        base_time = base_times.get(node_type, 100.0)
        
        # Adjust based on parameters
        if node_type == NodeType.SAMPLER:
            steps = params.get("steps", 25)
            width = params.get("width", 512)
            height = params.get("height", 512)
            batch_size = params.get("batch_size", 1)
            
            # Scale based on resolution and steps
            resolution_factor = (width * height) / (512 * 512)
            step_factor = steps / 25
            batch_factor = batch_size
            
            base_time *= resolution_factor * step_factor * batch_factor
        
        elif node_type == NodeType.VAE_DECODE:
            width = params.get("width", 512)
            height = params.get("height", 512)
            batch_size = params.get("batch_size", 1)
            
            resolution_factor = (width * height) / (512 * 512)
            base_time *= resolution_factor * batch_size
        
        return base_time

    def _estimate_node_vram_usage(self, node_type: NodeType, params: Dict[str, Any]) -> float:
        """Estimate node VRAM usage based on type and parameters"""
        
        base_vram = {
            NodeType.MODEL_LOADER: 4096.0,    # 4GB for model
            NodeType.VAE_LOADER: 512.0,       # 512MB for VAE
            NodeType.CLIP_LOADER: 1024.0,     # 1GB for CLIP
            NodeType.TEXT_ENCODE: 256.0,      # 256MB for text encoding
            NodeType.SAMPLER: 2048.0,         # 2GB during sampling
            NodeType.VAE_DECODE: 1024.0,      # 1GB for VAE decode
            NodeType.IMAGE_SAVE: 100.0,       # 100MB for image processing
            NodeType.CONDITIONING: 128.0,     # 128MB for conditioning
            NodeType.LATENT_IMAGE: 256.0,     # 256MB for latent
            NodeType.NOISE: 64.0,             # 64MB for noise
            NodeType.SCHEDULER: 10.0,         # 10MB for scheduler
            NodeType.GUIDANCE: 10.0,          # 10MB for guidance
            NodeType.UNKNOWN: 256.0           # Default
        }
        
        base_usage = base_vram.get(node_type, 256.0)
        
        # Adjust based on parameters
        if node_type in [NodeType.SAMPLER, NodeType.VAE_DECODE, NodeType.LATENT_IMAGE]:
            width = params.get("width", 512)
            height = params.get("height", 512)
            batch_size = params.get("batch_size", 1)
            
            resolution_factor = (width * height) / (512 * 512)
            base_usage *= resolution_factor * batch_size
        
        return base_usage

    def _can_node_be_cached(self, node_type: NodeType) -> bool:
        """Determine if node output can be cached"""
        cacheable_types = {
            NodeType.MODEL_LOADER,
            NodeType.VAE_LOADER,
            NodeType.CLIP_LOADER,
            NodeType.TEXT_ENCODE,
            NodeType.CONDITIONING,
            NodeType.SCHEDULER
        }
        return node_type in cacheable_types

    def _estimate_cache_size(self, node_type: NodeType, params: Dict[str, Any]) -> float:
        """Estimate cache size for node"""
        if not self._can_node_be_cached(node_type):
            return 0.0
        
        cache_sizes = {
            NodeType.MODEL_LOADER: 4096.0,    # Model weights
            NodeType.VAE_LOADER: 512.0,       # VAE weights  
            NodeType.CLIP_LOADER: 1024.0,     # CLIP weights
            NodeType.TEXT_ENCODE: 64.0,       # Encoded text
            NodeType.CONDITIONING: 32.0,      # Conditioning data
            NodeType.SCHEDULER: 1.0           # Schedule data
        }
        
        return cache_sizes.get(node_type, 32.0)

    def _is_node_parallelizable(self, node_type: NodeType) -> bool:
        """Determine if node can run in parallel with others"""
        # Most nodes are not parallelizable due to dependencies
        parallelizable_types = {
            NodeType.TEXT_ENCODE,  # Multiple text encoding can be parallel
            NodeType.IMAGE_SAVE    # Image saving can be parallel
        }
        return node_type in parallelizable_types

    def _is_node_cpu_intensive(self, node_type: NodeType) -> bool:
        """Determine if node is CPU intensive"""
        cpu_intensive_types = {
            NodeType.TEXT_ENCODE,
            NodeType.IMAGE_SAVE,
            NodeType.CONDITIONING
        }
        return node_type in cpu_intensive_types

    def _build_dependency_graph(self, workflow: Dict[str, Any]) -> Dict[str, List[str]]:
        """Build dependency graph from workflow links"""
        
        dependencies = {}
        nodes = {str(node.get("id")): node for node in workflow.get("nodes", [])}
        
        # Initialize all nodes with empty dependencies
        for node_id in nodes.keys():
            dependencies[node_id] = []
        
        # Process links to build dependencies
        for link in workflow.get("links", []):
            from_node = str(link.get("from"))
            to_node = str(link.get("to"))
            
            if to_node in dependencies and from_node in nodes:
                dependencies[to_node].append(from_node)
        
        return dependencies

    async def _generate_optimization_plan(self, workflow_hash: str,
                                         node_profiles: Dict[str, NodeExecutionProfile],
                                         dependency_graph: Dict[str, List[str]],
                                         priority: OptimizationPriority,
                                         params: Dict[str, Any]) -> WorkflowOptimizationPlan:
        """Generate optimized execution plan"""
        
        # 1. Topological sort for execution order
        execution_order = self._topological_sort(dependency_graph)
        
        # 2. Identify parallel execution opportunities
        parallel_groups = self._identify_parallel_groups(execution_order, dependency_graph, node_profiles)
        
        # 3. Determine caching strategy
        caching_strategy = self._determine_caching_strategy(node_profiles, priority)
        
        # 4. Identify memory checkpoint locations
        memory_checkpoints = self._identify_memory_checkpoints(execution_order, node_profiles, priority)
        
        # 5. Estimate performance metrics
        estimated_time = self._estimate_total_execution_time(execution_order, node_profiles, parallel_groups)
        estimated_vram = self._estimate_peak_vram_usage(execution_order, node_profiles, caching_strategy)
        
        # 6. Generate optimization notes
        optimization_notes = self._generate_optimization_notes(
            node_profiles, parallel_groups, caching_strategy, priority
        )
        
        plan = WorkflowOptimizationPlan(
            workflow_hash=workflow_hash,
            execution_order=execution_order,
            parallel_groups=parallel_groups,
            caching_strategy=caching_strategy,
            memory_checkpoints=memory_checkpoints,
            estimated_total_time_ms=estimated_time,
            estimated_peak_vram_mb=estimated_vram,
            optimization_notes=optimization_notes
        )
        
        return plan

    def _topological_sort(self, dependency_graph: Dict[str, List[str]]) -> List[str]:
        """Perform topological sort to determine execution order"""
        
        # Calculate in-degrees
        in_degree = {node: 0 for node in dependency_graph}
        for node, deps in dependency_graph.items():
            for dep in deps:
                if dep in in_degree:
                    in_degree[dep] = in_degree.get(dep, 0)
            for dep in deps:
                if dep in in_degree:
                    in_degree[node] += 1
        
        # Find nodes with no dependencies
        queue = [node for node, degree in in_degree.items() if degree == 0]
        execution_order = []
        
        while queue:
            current = queue.pop(0)
            execution_order.append(current)
            
            # Update in-degrees of dependent nodes
            for node, deps in dependency_graph.items():
                if current in deps:
                    in_degree[node] -= 1
                    if in_degree[node] == 0:
                        queue.append(node)
        
        return execution_order

    def _identify_parallel_groups(self, execution_order: List[str],
                                 dependency_graph: Dict[str, List[str]],
                                 node_profiles: Dict[str, NodeExecutionProfile]) -> List[List[str]]:
        """Identify groups of nodes that can execute in parallel"""
        
        parallel_groups = []
        processed = set()
        
        for node_id in execution_order:
            if node_id in processed:
                continue
            
            # Find all nodes that can run in parallel with this one
            parallel_candidates = [node_id]
            
            # Check remaining nodes
            for other_node in execution_order:
                if other_node in processed or other_node == node_id:
                    continue
                
                # Check if nodes can run in parallel
                if self._can_run_in_parallel(node_id, other_node, dependency_graph, node_profiles):
                    parallel_candidates.append(other_node)
            
            if len(parallel_candidates) > 1:
                parallel_groups.append(parallel_candidates)
                processed.update(parallel_candidates)
            else:
                processed.add(node_id)
        
        return parallel_groups

    def _can_run_in_parallel(self, node1: str, node2: str,
                            dependency_graph: Dict[str, List[str]],
                            node_profiles: Dict[str, NodeExecutionProfile]) -> bool:
        """Check if two nodes can run in parallel"""
        
        # Check if nodes have interdependencies
        if node1 in dependency_graph.get(node2, []) or node2 in dependency_graph.get(node1, []):
            return False
        
        # Check if both nodes are parallelizable
        profile1 = node_profiles.get(node1)
        profile2 = node_profiles.get(node2)
        
        if not (profile1 and profile2):
            return False
        
        if not (profile1.parallelizable and profile2.parallelizable):
            return False
        
        # Check resource constraints (don't parallel if both are GPU intensive)
        gpu_intensive_types = {NodeType.SAMPLER, NodeType.VAE_DECODE, NodeType.MODEL_LOADER}
        
        if (profile1.node_type in gpu_intensive_types and 
            profile2.node_type in gpu_intensive_types):
            return False
        
        return True

    def _determine_caching_strategy(self, node_profiles: Dict[str, NodeExecutionProfile],
                                   priority: OptimizationPriority) -> Dict[str, bool]:
        """Determine which nodes should have their outputs cached"""
        
        caching_strategy = {}
        
        for node_id, profile in node_profiles.items():
            should_cache = False
            
            if profile.can_cache:
                if priority == OptimizationPriority.SPEED:
                    # Cache if execution time is significant
                    should_cache = profile.execution_time_ms > 1000  # > 1 second
                elif priority == OptimizationPriority.MEMORY:
                    # Cache only if cache size is small
                    should_cache = profile.cache_size_mb < 512  # < 512MB
                elif priority == OptimizationPriority.QUALITY:
                    # Cache expensive operations
                    should_cache = profile.execution_time_ms > 500  # > 0.5 seconds
                else:  # BALANCED
                    # Cache if benefit/cost ratio is good
                    if profile.cache_size_mb > 0:
                        benefit_cost_ratio = profile.execution_time_ms / profile.cache_size_mb
                        should_cache = benefit_cost_ratio > 2.0  # 2ms saved per MB cached
            
            caching_strategy[node_id] = should_cache
        
        return caching_strategy

    def _identify_memory_checkpoints(self, execution_order: List[str],
                                   node_profiles: Dict[str, NodeExecutionProfile],
                                   priority: OptimizationPriority) -> List[str]:
        """Identify points where memory should be freed"""
        
        checkpoints = []
        cumulative_vram = 0.0
        vram_threshold = 12288.0  # 12GB threshold for RTX 4070 Ti SUPER
        
        if priority == OptimizationPriority.MEMORY:
            vram_threshold = 10240.0  # 10GB for memory-focused optimization
        
        for node_id in execution_order:
            profile = node_profiles.get(node_id)
            if not profile:
                continue
            
            cumulative_vram += profile.vram_usage_mb
            
            # Add checkpoint if approaching VRAM limit
            if cumulative_vram > vram_threshold:
                checkpoints.append(node_id)
                cumulative_vram = profile.vram_usage_mb  # Reset after checkpoint
            
            # Add checkpoint after heavy operations
            if profile.node_type == NodeType.SAMPLER:
                checkpoints.append(node_id)
                cumulative_vram = 0.0  # Reset after sampling
        
        return checkpoints

    def _estimate_total_execution_time(self, execution_order: List[str],
                                     node_profiles: Dict[str, NodeExecutionProfile],
                                     parallel_groups: List[List[str]]) -> float:
        """Estimate total workflow execution time"""
        
        total_time = 0.0
        parallel_processed = set()
        
        # Process parallel groups
        for group in parallel_groups:
            if len(group) > 1:
                # For parallel execution, take the maximum time in the group
                group_times = []
                for node_id in group:
                    profile = node_profiles.get(node_id)
                    if profile:
                        group_times.append(profile.execution_time_ms)
                        parallel_processed.add(node_id)
                
                if group_times:
                    total_time += max(group_times)
        
        # Process sequential nodes
        for node_id in execution_order:
            if node_id not in parallel_processed:
                profile = node_profiles.get(node_id)
                if profile:
                    total_time += profile.execution_time_ms
        
        return total_time

    def _estimate_peak_vram_usage(self, execution_order: List[str],
                                 node_profiles: Dict[str, NodeExecutionProfile],
                                 caching_strategy: Dict[str, bool]) -> float:
        """Estimate peak VRAM usage during execution"""
        
        current_vram = 0.0
        peak_vram = 0.0
        cached_memory = 0.0
        
        for node_id in execution_order:
            profile = node_profiles.get(node_id)
            if not profile:
                continue
            
            # Add node execution memory
            current_vram += profile.vram_usage_mb
            
            # Add cache memory if caching
            if caching_strategy.get(node_id, False):
                cached_memory += profile.cache_size_mb
            
            # Track peak
            total_vram = current_vram + cached_memory
            peak_vram = max(peak_vram, total_vram)
            
            # Remove execution memory after node completes (if not cached)
            if not caching_strategy.get(node_id, False):
                current_vram -= profile.vram_usage_mb
        
        return peak_vram

    def _generate_optimization_notes(self, node_profiles: Dict[str, NodeExecutionProfile],
                                   parallel_groups: List[List[str]],
                                   caching_strategy: Dict[str, bool],
                                   priority: OptimizationPriority) -> List[str]:
        """Generate human-readable optimization notes"""
        
        notes = []
        
        # Parallel execution notes
        total_parallel_nodes = sum(len(group) for group in parallel_groups if len(group) > 1)
        if total_parallel_nodes > 0:
            notes.append(f"Identified {len(parallel_groups)} parallel execution opportunities affecting {total_parallel_nodes} nodes")
        
        # Caching notes
        cached_nodes = sum(1 for cached in caching_strategy.values() if cached)
        if cached_nodes > 0:
            notes.append(f"Enabled caching for {cached_nodes} nodes to improve performance")
        
        # Performance bottleneck identification
        bottlenecks = []
        for node_id, profile in node_profiles.items():
            if profile.execution_time_ms > 5000:  # > 5 seconds
                bottlenecks.append(f"{node_id} ({profile.node_type.value})")
        
        if bottlenecks:
            notes.append(f"Potential bottlenecks identified: {', '.join(bottlenecks)}")
        
        # Priority-specific notes
        if priority == OptimizationPriority.SPEED:
            notes.append("Optimization focused on minimizing execution time")
        elif priority == OptimizationPriority.MEMORY:
            notes.append("Optimization focused on minimizing VRAM usage")
        elif priority == OptimizationPriority.QUALITY:
            notes.append("Optimization focused on maximizing output quality")
        else:
            notes.append("Balanced optimization considering speed, memory, and quality")
        
        return notes

    async def _get_cached_optimization_plan(self, workflow_hash: str, 
                                           priority: OptimizationPriority) -> Optional[WorkflowOptimizationPlan]:
        """Get cached optimization plan if available"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT execution_order, parallel_groups, caching_strategy, memory_checkpoints,
                           estimated_total_time_ms, estimated_peak_vram_mb
                    FROM workflow_optimization_plans
                    WHERE workflow_hash = ? AND optimization_priority = ?
                    ORDER BY last_used DESC
                    LIMIT 1
                """, (workflow_hash, priority.value))
                
                result = cursor.fetchone()
                
                if result:
                    execution_order, parallel_groups, caching_strategy, memory_checkpoints, est_time, est_vram = result
                    
                    # Update usage statistics
                    cursor.execute("""
                        UPDATE workflow_optimization_plans
                        SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP
                        WHERE workflow_hash = ? AND optimization_priority = ?
                    """, (workflow_hash, priority.value))
                    
                    conn.commit()
                    
                    plan = WorkflowOptimizationPlan(
                        workflow_hash=workflow_hash,
                        execution_order=json.loads(execution_order),
                        parallel_groups=json.loads(parallel_groups),
                        caching_strategy=json.loads(caching_strategy),
                        memory_checkpoints=json.loads(memory_checkpoints),
                        estimated_total_time_ms=est_time,
                        estimated_peak_vram_mb=est_vram,
                        optimization_notes=["Loaded from cache"]
                    )
                    
                    return plan
                    
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "cache_retrieval_failed", f"Failed to retrieve cached plan: {str(e)}", {
                "workflow_hash": workflow_hash
            }, e)
        
        return None

    async def _cache_optimization_plan(self, plan: WorkflowOptimizationPlan, 
                                      priority: OptimizationPriority,
                                      params: Dict[str, Any]):
        """Cache optimization plan for future use"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO workflow_optimization_plans (
                        workflow_hash, model_type, complexity_level, optimization_priority,
                        execution_order, parallel_groups, caching_strategy, memory_checkpoints,
                        estimated_total_time_ms, estimated_peak_vram_mb
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    plan.workflow_hash,
                    params.get("model", "unknown"),
                    params.get("complexity", "medium"),
                    priority.value,
                    json.dumps(plan.execution_order),
                    json.dumps(plan.parallel_groups),
                    json.dumps(plan.caching_strategy),
                    json.dumps(plan.memory_checkpoints),
                    plan.estimated_total_time_ms,
                    plan.estimated_peak_vram_mb
                ))
                
                conn.commit()
                
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "cache_storage_failed", f"Failed to cache optimization plan: {str(e)}", {
                "workflow_hash": plan.workflow_hash
            }, e)

    async def _get_node_historical_performance(self, node_id: str, class_type: str, 
                                              params: Dict[str, Any]) -> Dict[str, Any]:
        """Get historical performance data for a node"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT AVG(execution_time_ms) as avg_time, AVG(vram_usage_mb) as avg_vram,
                           COUNT(*) as sample_count
                    FROM node_execution_profiles
                    WHERE node_type = ?
                    GROUP BY node_type
                """, (self._detect_node_type(class_type).value,))
                
                result = cursor.fetchone()
                
                if result and result[2] > 0:  # sample_count > 0
                    return {
                        "avg_execution_time_ms": result[0],
                        "avg_vram_usage_mb": result[1],
                        "sample_count": result[2]
                    }
                    
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "historical_data_retrieval_failed", f"Failed to get historical data: {str(e)}", {
                "node_id": node_id,
                "class_type": class_type
            }, e)
        
        return {}

    async def record_execution_performance(self, generation_id: str, workflow_hash: str,
                                          actual_execution_time_ms: float,
                                          actual_peak_vram_mb: float,
                                          success: bool,
                                          error_message: Optional[str] = None,
                                          bottleneck_nodes: Optional[List[str]] = None):
        """Record actual execution performance for learning"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO workflow_execution_history (
                        generation_id, workflow_hash, actual_execution_time_ms,
                        actual_peak_vram_mb, success, error_message, bottleneck_nodes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    generation_id, workflow_hash, actual_execution_time_ms,
                    actual_peak_vram_mb, success, error_message,
                    json.dumps(bottleneck_nodes) if bottleneck_nodes else None
                ))
                
                # Update optimization plan success rate
                cursor.execute("""
                    UPDATE workflow_optimization_plans
                    SET success_rate = (
                        SELECT AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END)
                        FROM workflow_execution_history
                        WHERE workflow_hash = ?
                    )
                    WHERE workflow_hash = ?
                """, (workflow_hash, workflow_hash))
                
                conn.commit()
                
                log_activity("WORKFLOW_OPTIMIZER", f"Recorded execution performance for {generation_id}", {
                    "workflow_hash": workflow_hash,
                    "execution_time_ms": actual_execution_time_ms,
                    "success": success
                })
                
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "performance_recording_failed", f"Failed to record performance: {str(e)}", {
                "generation_id": generation_id,
                "workflow_hash": workflow_hash
            }, e)

    async def get_optimization_statistics(self, days: int = 7) -> Dict[str, Any]:
        """Get workflow optimization statistics"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Get basic statistics
                cursor.execute("""
                    SELECT COUNT(*) as total_workflows,
                           AVG(estimated_total_time_ms) as avg_estimated_time,
                           AVG(estimated_peak_vram_mb) as avg_estimated_vram
                    FROM workflow_optimization_plans
                    WHERE created_at >= datetime('now', '-{} days')
                """.format(days))
                
                basic_stats = cursor.fetchone()
                
                # Get execution statistics
                cursor.execute("""
                    SELECT COUNT(*) as total_executions,
                           AVG(actual_execution_time_ms) as avg_actual_time,
                           AVG(actual_peak_vram_mb) as avg_actual_vram,
                           AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) as success_rate
                    FROM workflow_execution_history
                    WHERE executed_at >= datetime('now', '-{} days')
                """.format(days))
                
                execution_stats = cursor.fetchone()
                
                # Get optimization effectiveness
                cursor.execute("""
                    SELECT p.optimization_priority,
                           COUNT(*) as plan_count,
                           AVG(h.success) as success_rate,
                           AVG(h.actual_execution_time_ms) as avg_execution_time
                    FROM workflow_optimization_plans p
                    LEFT JOIN workflow_execution_history h ON p.workflow_hash = h.workflow_hash
                    WHERE p.created_at >= datetime('now', '-{} days')
                    GROUP BY p.optimization_priority
                """.format(days))
                
                priority_stats = cursor.fetchall()
                
                stats = {
                    "period_days": days,
                    "workflow_optimization": {
                        "total_workflows": basic_stats[0] if basic_stats else 0,
                        "avg_estimated_time_ms": basic_stats[1] if basic_stats else 0,
                        "avg_estimated_vram_mb": basic_stats[2] if basic_stats else 0
                    },
                    "execution_performance": {
                        "total_executions": execution_stats[0] if execution_stats else 0,
                        "avg_actual_time_ms": execution_stats[1] if execution_stats else 0,
                        "avg_actual_vram_mb": execution_stats[2] if execution_stats else 0,
                        "success_rate": execution_stats[3] if execution_stats else 0
                    },
                    "optimization_effectiveness": {}
                }
                
                for row in priority_stats:
                    priority, plan_count, success_rate, avg_time = row
                    stats["optimization_effectiveness"][priority] = {
                        "plan_count": plan_count,
                        "success_rate": success_rate or 0,
                        "avg_execution_time_ms": avg_time or 0
                    }
                
                return stats
                
        except Exception as e:
            log_error("WORKFLOW_OPTIMIZER", "statistics_generation_failed", f"Failed to generate statistics: {str(e)}", {}, e)
            return {"error": str(e)}