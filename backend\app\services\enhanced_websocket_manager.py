"""
Enhanced WebSocket Manager for Backend Middleware
Provides advanced WebSocket handling with real-time progress updates, 
connection resilience, and intelligent event streaming
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Set, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from fastapi import WebSocket
from app.services.service_discovery import service_discovery, ServiceStatus
from app.services.websocket_relay import websocket_relay

logger = logging.getLogger(__name__)

class EventType(Enum):
    # System events
    CONNECTION_STATUS = "connection_status"
    SERVICE_STATUS = "service_status"
    SYSTEM_STATS = "system_stats"
    
    # Generation events
    GENERATION_START = "generation_start"
    GENERATION_PROGRESS = "generation_progress"
    GENERATION_COMPLETE = "generation_complete"
    GENERATION_ERROR = "generation_error"
    GENERATION_CANCELLED = "generation_cancelled"
    
    # Real-time events
    COMFYUI_PROGRESS = "comfyui_progress"
    COMFYUI_EXECUTION_START = "comfyui_execution_start"
    COMFYUI_EXECUTION_COMPLETE = "comfyui_execution_complete"
    COMFYUI_EXECUTION_ERROR = "comfyui_execution_error"
    
    # Heartbeat events
    PING = "ping"
    PONG = "pong"

@dataclass
class WSMessage:
    """WebSocket message structure"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    priority: int = 5  # Higher number = higher priority
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.message_id,
            'type': self.event_type.value,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'priority': self.priority
        }

@dataclass 
class ClientConnection:
    """WebSocket client connection information"""
    client_id: str
    websocket: WebSocket
    connected_at: datetime = field(default_factory=datetime.now)
    last_heartbeat: datetime = field(default_factory=datetime.now)
    subscriptions: Set[EventType] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    message_count: int = 0
    last_error: Optional[str] = None
    
class ProgressThrottler:
    """Throttles progress messages to prevent spam"""
    
    def __init__(self, interval_seconds: float = 1.0):
        self.interval = interval_seconds
        self.last_sent: Dict[str, datetime] = {}
        self.pending_progress: Dict[str, Dict[str, Any]] = {}
    
    def should_send_progress(self, generation_id: str, progress_data: Dict[str, Any]) -> bool:
        """Check if progress update should be sent"""
        now = datetime.now()
        last_sent = self.last_sent.get(generation_id)
        
        # Always send first progress or if interval has passed
        if last_sent is None or (now - last_sent).total_seconds() >= self.interval:
            self.last_sent[generation_id] = now
            # Clear any pending progress
            if generation_id in self.pending_progress:
                del self.pending_progress[generation_id]
            return True
        
        # Store pending progress (will send latest when interval passes)
        self.pending_progress[generation_id] = progress_data
        return False
    
    def get_pending_progress(self) -> List[tuple[str, Dict[str, Any]]]:
        """Get and clear pending progress updates that should be sent"""
        now = datetime.now()
        to_send = []
        to_remove = []
        
        for generation_id, progress_data in self.pending_progress.items():
            last_sent = self.last_sent.get(generation_id)
            if last_sent is None or (now - last_sent).total_seconds() >= self.interval:
                to_send.append((generation_id, progress_data))
                to_remove.append(generation_id)
                self.last_sent[generation_id] = now
        
        for generation_id in to_remove:
            del self.pending_progress[generation_id]
        
        return to_send

class EnhancedWebSocketManager:
    """Enhanced WebSocket manager with advanced features"""
    
    def __init__(self):
        self.clients: Dict[str, ClientConnection] = {}
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.progress_throttler = ProgressThrottler()
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.progress_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # Configuration
        self.heartbeat_interval = 30  # seconds
        self.client_timeout = 60  # seconds
        self.progress_throttle_interval = 1.0  # seconds
        self.auto_reconnect = True  # Enable automatic reconnection for clients
        self.performance_monitor = None  # Will be set by performance monitoring service
        
        # Statistics
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'messages_received': 0,
            'errors': 0,
            'disconnections': 0
        }
        
        logger.info("Enhanced WebSocket Manager initialized")
    
    async def start(self):
        """Start the WebSocket manager background tasks"""
        if self.is_running:
            return
        
        self.is_running = True
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.progress_task = asyncio.create_task(self._progress_loop())
        logger.info("Enhanced WebSocket Manager started")
    
    async def stop(self):
        """Stop the WebSocket manager and clean up"""
        self.is_running = False
        
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        
        if self.progress_task:
            self.progress_task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.clients.keys()):
            await self._disconnect_client(client_id, "Server shutting down")
        
        logger.info("Enhanced WebSocket Manager stopped")
    
    async def add_client(self, websocket: WebSocket) -> str:
        """Add a new WebSocket client"""
    # Connection is accepted in the main endpoint; do not accept here
        
        client_id = str(uuid.uuid4())
        client = ClientConnection(
            client_id=client_id,
            websocket=websocket
        )
        
        self.clients[client_id] = client
        self.stats['total_connections'] += 1
        self.stats['active_connections'] = len(self.clients)
        
        # Send connection confirmation
        await self._send_to_client(client_id, WSMessage(
            event_type=EventType.CONNECTION_STATUS,
            data={
                'status': 'connected',
                'client_id': client_id,
                'server_time': datetime.now().isoformat()
            }
        ))
        
        logger.info(f"Client {client_id} connected. Total clients: {len(self.clients)}")
        return client_id
    
    async def connect_client(self, websocket: WebSocket) -> str:
        """Connect a new WebSocket client (alias for add_client)"""
        return await self.add_client(websocket)
    
    async def disconnect_client(self, client_id: str, reason: str = "Client disconnected"):
        """Disconnect a WebSocket client"""
        await self._disconnect_client(client_id, reason)
    
    async def remove_client(self, client_id: str, reason: str = "Client disconnected"):
        """Remove a WebSocket client (alias for disconnect_client)"""
        await self._disconnect_client(client_id, reason)
    
    async def _disconnect_client(self, client_id: str, reason: str = "Disconnected"):
        """Internal method to disconnect a client"""
        if client_id in self.clients:
            client = self.clients[client_id]
            
            try:
                # Send disconnection message
                await self._send_to_client(client_id, WSMessage(
                    event_type=EventType.CONNECTION_STATUS,
                    data={
                        'status': 'disconnecting',
                        'reason': reason
                    }
                ))
                
                # Close WebSocket connection
                await client.websocket.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket for client {client_id}: {e}")
            finally:
                del self.clients[client_id]
                self.stats['active_connections'] = len(self.clients)
                self.stats['disconnections'] += 1
                
                logger.info(f"Client {client_id} disconnected: {reason}. Active clients: {len(self.clients)}")
    
    async def broadcast_message(self, message: WSMessage, exclude_clients: Optional[Set[str]] = None):
        """Broadcast message to all connected clients"""
        if exclude_clients is None:
            exclude_clients = set()
        
        tasks = []
        for client_id in self.clients:
            if client_id not in exclude_clients:
                tasks.append(self._send_to_client(client_id, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_to_client(self, client_id: str, message: WSMessage) -> bool:
        """Send message to a specific client"""
        return await self._send_to_client(client_id, message)
    
    async def _send_to_client(self, client_id: str, message: WSMessage) -> bool:
        """Internal method to send message to client"""
        if client_id not in self.clients:
            logger.warning(f"Attempted to send message to non-existent client: {client_id}")
            return False

        client = self.clients[client_id]

        # Check WebSocket connection state before sending
        try:
            # Check if WebSocket is in a valid state for sending
            if hasattr(client.websocket, 'client_state'):
                from starlette.websockets import WebSocketState
                if client.websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket for client {client_id} is not connected (state: {client.websocket.client_state})")
                    await self._disconnect_client(client_id, "WebSocket not connected")
                    return False
            elif hasattr(client.websocket, 'application_state'):
                from starlette.websockets import WebSocketState
                if client.websocket.application_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket for client {client_id} is not connected (app_state: {client.websocket.application_state})")
                    await self._disconnect_client(client_id, "WebSocket not connected")
                    return False

            await client.websocket.send_text(json.dumps(message.to_dict()))
            client.message_count += 1
            self.stats['messages_sent'] += 1
            return True
        except Exception as e:
            logger.error(f"Failed to send message to client {client_id}: {e}")
            client.last_error = str(e)
            self.stats['errors'] += 1
            # Disconnect client on send error
            await self._disconnect_client(client_id, f"Send error: {e}")
            return False
    
    async def handle_client_message(self, client_id: str, message_data: Dict[str, Any]):
        """Handle incoming message from client"""
        if client_id not in self.clients:
            return
        
        client = self.clients[client_id]
        client.last_heartbeat = datetime.now()
        self.stats['messages_received'] += 1
        
        try:
            message_type = message_data.get('type')
            data = message_data.get('data', {})
            
            if message_type == 'ping':
                # Respond to ping with pong
                await self._send_to_client(client_id, WSMessage(
                    event_type=EventType.PONG,
                    data={'server_time': datetime.now().isoformat()}
                ))
            
            elif message_type == 'subscribe':
                # Handle event subscription
                event_types = data.get('events', [])
                for event_type_str in event_types:
                    try:
                        event_type = EventType(event_type_str)
                        client.subscriptions.add(event_type)
                        logger.info(f"Client {client_id} subscribed to {event_type.value}")
                    except ValueError:
                        logger.warning(f"Invalid event type: {event_type_str}")
            
            elif message_type == 'unsubscribe':
                # Handle event unsubscription
                event_types = data.get('events', [])
                for event_type_str in event_types:
                    try:
                        event_type = EventType(event_type_str)
                        client.subscriptions.discard(event_type)
                        logger.info(f"Client {client_id} unsubscribed from {event_type.value}")
                    except ValueError:
                        logger.warning(f"Invalid event type: {event_type_str}")
            
            else:
                logger.info(f"Received message from client {client_id}: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message from client {client_id}: {e}")
            client.last_error = str(e)
            self.stats['errors'] += 1
    
    async def send_progress_update(self, generation_id: str, progress_data: Dict[str, Any]):
        """Send throttled progress update"""
        if self.progress_throttler.should_send_progress(generation_id, progress_data):
            message = WSMessage(
                event_type=EventType.GENERATION_PROGRESS,
                data={
                    'generation_id': generation_id,
                    **progress_data
                },
                priority=5
            )
            await self.broadcast_message(message)
    
    async def send_generation_start(self, generation_id: str, generation_data: Dict[str, Any]):
        """Send generation start event"""
        message = WSMessage(
            event_type=EventType.GENERATION_START,
            data={
                'generation_id': generation_id,
                **generation_data
            },
            priority=8
        )
        await self.broadcast_message(message)
    
    async def send_generation_complete(self, generation_id: str, result_data: Dict[str, Any]):
        """Send generation complete event"""
        message = WSMessage(
            event_type=EventType.GENERATION_COMPLETE,
            data={
                'generation_id': generation_id,
                **result_data
            },
            priority=9
        )
        await self.broadcast_message(message)
    
    async def send_generation_error(self, generation_id: str, error_data: Dict[str, Any]):
        """Send generation error event"""
        message = WSMessage(
            event_type=EventType.GENERATION_ERROR,
            data={
                'generation_id': generation_id,
                **error_data
            },
            priority=10
        )
        await self.broadcast_message(message)
    
    async def send_system_stats(self, stats_data: Dict[str, Any]):
        """Send system statistics"""
        message = WSMessage(
            event_type=EventType.SYSTEM_STATS,
            data=stats_data,
            priority=3
        )
        await self.broadcast_message(message)
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop"""
        while self.is_running:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.is_running:
                    break
                
                # Send heartbeat to all clients
                heartbeat_message = WSMessage(
                    event_type=EventType.PING,
                    data={'server_time': datetime.now().isoformat()},
                    priority=1
                )
                
                # Check for timed out clients
                now = datetime.now()
                timeout_clients = []
                
                for client_id, client in self.clients.items():
                    # Send heartbeat
                    await self._send_to_client(client_id, heartbeat_message)
                    
                    # Check for timeout
                    if (now - client.last_heartbeat).total_seconds() > self.client_timeout:
                        timeout_clients.append(client_id)
                
                # Disconnect timed out clients
                for client_id in timeout_clients:
                    await self._disconnect_client(client_id, "Client timeout")
                
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                self.stats['errors'] += 1
    
    async def _progress_loop(self):
        """Background loop to send pending progress updates"""
        while self.is_running:
            try:
                await asyncio.sleep(0.5)  # Check every 500ms
                
                if not self.is_running:
                    break
                
                # Send pending progress updates
                pending_updates = self.progress_throttler.get_pending_progress()
                for generation_id, progress_data in pending_updates:
                    message = WSMessage(
                        event_type=EventType.GENERATION_PROGRESS,
                        data={
                            'generation_id': generation_id,
                            **progress_data
                        },
                        priority=5
                    )
                    await self.broadcast_message(message)
                
            except Exception as e:
                logger.error(f"Error in progress loop: {e}")
                self.stats['errors'] += 1
    
    def get_client_count(self) -> int:
        """Get number of connected clients"""
        return len(self.clients)
    
    def get_client_info(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific client"""
        if client_id not in self.clients:
            return None
        
        client = self.clients[client_id]
        return {
            'client_id': client.client_id,
            'connected_at': client.connected_at.isoformat(),
            'last_heartbeat': client.last_heartbeat.isoformat(),
            'message_count': client.message_count,
            'subscriptions': [event.value for event in client.subscriptions],
            'metadata': client.metadata,
            'last_error': client.last_error
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket manager statistics"""
        return {
            **self.stats,
            'uptime_seconds': (datetime.now() - datetime.now()).total_seconds() if self.is_running else 0,
            'client_details': [self.get_client_info(client_id) for client_id in self.clients]
        }
    
    def is_client_connected(self, client_id: str) -> bool:
        """Check if a client is connected"""
        return client_id in self.clients
    
    def get_connected_clients(self) -> List[str]:
        """Get list of connected client IDs"""
        return list(self.clients.keys())

# Global instance
enhanced_websocket_manager = EnhancedWebSocketManager()