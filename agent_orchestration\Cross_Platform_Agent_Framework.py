#!/usr/bin/env python3
"""
Universal Cross-Platform Agent Framework
Complete system for making agents accessible across all Claude platforms

This single file contains:
1. Universal Agent Communication Layer
2. Cross-Platform Registry System
3. Enhanced Documentation Overseer with Universal Access
4. Platform-Specific Integrations (Claude Code, Augment, Windsurf, Copilot)
5. Deployment and Management Tools

Usage:
    python universal_agent_framework.py
"""

import asyncio
import json
import os
import sqlite3
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any, Union
from datetime import datetime
import yaml
import hashlib
import logging
from dataclasses import dataclass, asdict
from collections import defaultdict
import traceback
import tempfile
import shutil
import socket
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
import socketserver
import urllib.parse
import urllib.request
import subprocess
import sys
import time
import signal
import atexit

# Optional imports for enhanced functionality
try:
    import nltk
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.cluster import KMeans
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False
    print("Optional: Install sentence-transformers, scikit-learn, numpy for enhanced semantic features")

try:
    import networkx as nx
    GRAPH_AVAILABLE = True
except ImportError:
    GRAPH_AVAILABLE = False

# ==================== CORE DATA STRUCTURES ====================

@dataclass
class AgentInterface:
    """Standard interface for cross-platform agent communication."""
    agent_id: str
    agent_name: str
    capabilities: List[str]
    endpoints: List[str]
    authentication: Dict[str, str]
    platform: str = "multi-platform"
    status: str = "active"
    last_heartbeat: datetime = None
    version: str = "1.0.0"

@dataclass  
class AgentMessage:
    """Standard message format for agent communication."""
    message_id: str
    source_agent: str
    target_agent: str
    message_type: str  # 'request', 'response', 'notification', 'heartbeat'
    payload: Dict[str, Any]
    timestamp: datetime
    priority: str = "normal"  # 'low', 'normal', 'high', 'urgent'
    correlation_id: Optional[str] = None

@dataclass
class KnowledgeEntry:
    """Structured knowledge entry for the knowledge base."""
    id: str
    title: str
    content: str
    topic: str
    tags: List[str]
    source_file: str
    embedding: Optional[Any] = None
    last_updated: datetime = None
    related_entries: List[str] = None
    context_level: str = "detailed"  # 'overview', 'detailed', 'technical', 'reference'
    importance_score: float = 0.5
    access_count: int = 0
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()
        if self.related_entries is None:
            self.related_entries = []

@dataclass
class ContextTemplate:
    """Template for generating context files."""
    topic: str
    level: str
    sections: List[str]
    format_type: str = "markdown"  # 'markdown', 'json', 'yaml'
    max_tokens: int = 4000
    include_examples: bool = True
    include_references: bool = True

class AgentResult:
    """Result object for agent execution."""
    def __init__(self, status: str, data: Dict[str, Any], error: str = None):
        self.status = status
        self.data = data
        self.error = error
        self.timestamp = datetime.now().isoformat()

# ==================== UNIVERSAL AGENT REGISTRY ====================

class UniversalAgentRegistry:
    """Registry for all agents across platforms."""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, registry_port: int = 8765):
        if hasattr(self, 'initialized'):
            return
            
        self.registry_port = registry_port
        self.agents: Dict[str, AgentInterface] = {}
        self.message_queue: Dict[str, List[AgentMessage]] = defaultdict(list)
        self.response_handlers: Dict[str, Any] = {}
        
        # Setup directories and database
        self.agent_dir = Path.home() / '.claude_agents'
        self.agent_dir.mkdir(exist_ok=True)
        self.db_path = self.agent_dir / 'registry.db'
        
        # Initialize logging
        self.logger = logging.getLogger('UniversalAgentRegistry')
        self._setup_logging()
        
        # Initialize database
        self._init_database()
        
        # Load existing agents
        self._load_agents_from_db()
        
        # Start background services
        self._start_background_services()
        
        self.initialized = True
        
        # Cleanup on exit
        atexit.register(self.cleanup)
        
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
            
            # Also log to file
            file_handler = logging.FileHandler(self.agent_dir / 'registry.log')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
    def _init_database(self):
        """Initialize agent registry database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agents (
                agent_id TEXT PRIMARY KEY,
                agent_name TEXT NOT NULL,
                platform TEXT NOT NULL,
                capabilities TEXT NOT NULL,
                endpoints TEXT NOT NULL,
                authentication TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                version TEXT DEFAULT '1.0.0',
                last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_messages (
                message_id TEXT PRIMARY KEY,
                source_agent TEXT NOT NULL,
                target_agent TEXT NOT NULL,
                message_type TEXT NOT NULL,
                payload TEXT NOT NULL,
                correlation_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed BOOLEAN DEFAULT FALSE,
                response TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_id TEXT NOT NULL,
                action TEXT NOT NULL,
                execution_time REAL,
                success BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agent_id) REFERENCES agents (agent_id)
            )
        ''')
        
        conn.commit()
        conn.close()

    def _load_agents_from_db(self):
        """Load existing agents from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM agents WHERE status = "active"')
            
            for row in cursor.fetchall():
                agent_id, agent_name, platform, capabilities, endpoints, auth, status, version, last_heartbeat, created_at = row
                
                agent = AgentInterface(
                    agent_id=agent_id,
                    agent_name=agent_name,
                    platform=platform,
                    capabilities=json.loads(capabilities),
                    endpoints=json.loads(endpoints),
                    authentication=json.loads(auth),
                    status=status,
                    version=version,
                    last_heartbeat=datetime.fromisoformat(last_heartbeat) if last_heartbeat else None
                )
                
                self.agents[agent_id] = agent
                
            conn.close()
            self.logger.info(f"Loaded {len(self.agents)} agents from database")
            
        except Exception as e:
            self.logger.error(f"Failed to load agents from database: {str(e)}")

    def _start_background_services(self):
        """Start background services for the registry."""
        # Start HTTP server for agent communication
        threading.Thread(target=self._start_http_server, daemon=True).start()
        
        # Start heartbeat monitor
        threading.Thread(target=self._heartbeat_monitor, daemon=True).start()
        
        # Start message processor
        threading.Thread(target=self._message_processor, daemon=True).start()

    def _start_http_server(self):
        """Start HTTP server for agent communication."""
        try:
            class AgentHTTPHandler(BaseHTTPRequestHandler):
                def __init__(self, registry, *args, **kwargs):
                    self.registry = registry
                    super().__init__(*args, **kwargs)
                
                def do_POST(self):
                    try:
                        content_length = int(self.headers['Content-Length'])
                        post_data = self.rfile.read(content_length)
                        data = json.loads(post_data.decode('utf-8'))
                        
                        # Route the request
                        response = self.registry._handle_http_request(data, self.path)
                        
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.end_headers()
                        self.wfile.write(json.dumps(response).encode('utf-8'))
                        
                    except Exception as e:
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({"error": str(e)}).encode('utf-8'))
                
                def do_GET(self):
                    if self.path == '/agents':
                        # Return list of available agents
                        agents_list = [
                            {
                                "agent_id": agent.agent_id,
                                "agent_name": agent.agent_name,
                                "capabilities": agent.capabilities,
                                "status": agent.status,
                                "platform": agent.platform
                            }
                            for agent in self.registry.agents.values()
                        ]
                        
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.end_headers()
                        self.wfile.write(json.dumps(agents_list).encode('utf-8'))
                    else:
                        self.send_response(404)
                        self.end_headers()
                
                def log_message(self, format, *args):
                    pass  # Suppress default logging
            
            # Create handler with registry reference
            handler = lambda *args, **kwargs: AgentHTTPHandler(self, *args, **kwargs)
            
            # Find available port
            port = self.registry_port
            while port < self.registry_port + 100:
                try:
                    httpd = HTTPServer(('localhost', port), handler)
                    self.registry_port = port
                    self.logger.info(f"Agent registry HTTP server started on port {port}")
                    httpd.serve_forever()
                    break
                except OSError:
                    port += 1
                    
        except Exception as e:
            self.logger.error(f"Failed to start HTTP server: {str(e)}")

    def _handle_http_request(self, data: Dict[str, Any], path: str) -> Dict[str, Any]:
        """Handle HTTP requests to the registry."""
        try:
            request_type = data.get('type', 'unknown')
            
            if request_type == 'register':
                return self._handle_register_request(data)
            elif request_type == 'discover':
                return self._handle_discover_request(data)
            elif request_type == 'message':
                return self._handle_message_request(data)
            elif request_type == 'agent_call':
                return self._handle_agent_call_request(data)
            else:
                return {"error": f"Unknown request type: {request_type}"}
                
        except Exception as e:
            self.logger.error(f"Error handling HTTP request: {str(e)}")
            return {"error": str(e)}

    def _handle_register_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle agent registration request."""
        try:
            agent_data = data.get('agent', {})
            agent = AgentInterface(**agent_data)
            
            success = asyncio.run(self.register_agent(agent))
            return {"success": success, "agent_id": agent.agent_id}
            
        except Exception as e:
            return {"error": f"Registration failed: {str(e)}"}

    def _handle_discover_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle agent discovery request."""
        try:
            capability = data.get('capability')
            agents = asyncio.run(self.discover_agents(capability))
            
            return {
                "agents": [
                    {
                        "agent_id": agent.agent_id,
                        "agent_name": agent.agent_name,
                        "capabilities": agent.capabilities,
                        "endpoints": agent.endpoints,
                        "platform": agent.platform,
                        "status": agent.status
                    }
                    for agent in agents
                ]
            }
            
        except Exception as e:
            return {"error": f"Discovery failed: {str(e)}"}

    def _handle_message_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle message routing request."""
        try:
            message_data = data.get('message', {})
            message_data['timestamp'] = datetime.now()
            message = AgentMessage(**message_data)
            
            success = asyncio.run(self.route_message(message))
            return {"success": success, "message_id": message.message_id}
            
        except Exception as e:
            return {"error": f"Message routing failed: {str(e)}"}

    def _handle_agent_call_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle direct agent call request."""
        try:
            target_agent = data.get('target_agent')
            action = data.get('action')
            parameters = data.get('parameters', {})
            
            # Find target agent
            agent = None
            for a in self.agents.values():
                if a.agent_name == target_agent or a.agent_id == target_agent:
                    agent = a
                    break
            
            if not agent:
                return {"error": f"Agent '{target_agent}' not found"}
            
            # Create and route message
            message = AgentMessage(
                message_id=f"call_{datetime.now().timestamp()}_{hash(target_agent)}",
                source_agent="http_client",
                target_agent=agent.agent_id,
                message_type="request",
                payload={"action": action, "parameters": parameters},
                timestamp=datetime.now()
            )
            
            success = asyncio.run(self.route_message(message))
            if not success:
                return {"error": "Failed to route message"}
            
            # Wait for response
            response = self._wait_for_response_sync(message.message_id, timeout=30)
            return response or {"error": "Timeout waiting for response"}
            
        except Exception as e:
            return {"error": f"Agent call failed: {str(e)}"}

    def _wait_for_response_sync(self, message_id: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """Wait for response synchronously."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check database for response
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT response FROM agent_messages WHERE message_id = ? AND response IS NOT NULL',
                    (message_id,)
                )
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    return json.loads(result[0])
                    
            except Exception as e:
                self.logger.error(f"Error checking for response: {str(e)}")
            
            time.sleep(0.1)
        
        return None

    def _heartbeat_monitor(self):
        """Monitor agent heartbeats."""
        while True:
            try:
                current_time = datetime.now()
                inactive_agents = []
                
                for agent_id, agent in self.agents.items():
                    if agent.last_heartbeat:
                        time_diff = current_time - agent.last_heartbeat
                        if time_diff.seconds > 300:  # 5 minutes
                            inactive_agents.append(agent_id)
                
                # Mark inactive agents
                for agent_id in inactive_agents:
                    self.agents[agent_id].status = "inactive"
                    self.logger.warning(f"Agent {agent_id} marked as inactive")
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Heartbeat monitor error: {str(e)}")
                time.sleep(10)

    def _message_processor(self):
        """Process queued messages."""
        while True:
            try:
                # Process messages from database
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT * FROM agent_messages WHERE processed = FALSE ORDER BY timestamp ASC LIMIT 10'
                )
                
                messages = cursor.fetchall()
                for message_row in messages:
                    message_id, source_agent, target_agent, message_type, payload, correlation_id, timestamp, processed, response = message_row
                    
                    # Mark as processed
                    cursor.execute(
                        'UPDATE agent_messages SET processed = TRUE WHERE message_id = ?',
                        (message_id,)
                    )
                
                conn.commit()
                conn.close()
                
                time.sleep(1)  # Process every second
                
            except Exception as e:
                self.logger.error(f"Message processor error: {str(e)}")
                time.sleep(5)

    async def register_agent(self, agent: AgentInterface) -> bool:
        """Register an agent in the universal registry."""
        try:
            agent.last_heartbeat = datetime.now()
            self.agents[agent.agent_id] = agent
            
            # Persist to database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO agents 
                (agent_id, agent_name, platform, capabilities, endpoints, authentication, status, version, last_heartbeat)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                agent.agent_id, agent.agent_name, agent.platform,
                json.dumps(agent.capabilities), json.dumps(agent.endpoints),
                json.dumps(agent.authentication), agent.status, agent.version,
                agent.last_heartbeat
            ))
            conn.commit()
            conn.close()
            
            self.logger.info(f"Agent {agent.agent_name} ({agent.agent_id}) registered successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent.agent_name}: {str(e)}")
            return False

    async def discover_agents(self, capability: str = None) -> List[AgentInterface]:
        """Discover available agents, optionally filtered by capability."""
        available_agents = []
        
        for agent in self.agents.values():
            if agent.status == "active":
                if capability is None or capability in agent.capabilities:
                    available_agents.append(agent)
                    
        return available_agents

    async def route_message(self, message: AgentMessage) -> bool:
        """Route message between agents."""
        try:
            target_agent = self.agents.get(message.target_agent)
            if not target_agent:
                # Try to find by name
                for agent in self.agents.values():
                    if agent.agent_name == message.target_agent:
                        target_agent = agent
                        message.target_agent = agent.agent_id
                        break
                
                if not target_agent:
                    self.logger.warning(f"Target agent {message.target_agent} not found")
                    return False
            
            # Store message in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO agent_messages 
                (message_id, source_agent, target_agent, message_type, payload, correlation_id, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                message.message_id, message.source_agent, message.target_agent,
                message.message_type, json.dumps(message.payload), message.correlation_id,
                message.timestamp
            ))
            conn.commit()
            conn.close()
            
            # Route to appropriate platform
            await self._deliver_message(target_agent, message)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to route message: {str(e)}")
            return False

    async def _deliver_message(self, target_agent: AgentInterface, message: AgentMessage):
        """Deliver message to target agent based on platform."""
        for endpoint in target_agent.endpoints:
            try:
                if endpoint.startswith('http'):
                    await self._deliver_http(endpoint, message)
                elif endpoint.startswith('internal'):
                    await self._deliver_internal(endpoint, message)
                else:
                    self.logger.warning(f"Unknown endpoint type: {endpoint}")
                    continue
                    
                break  # Successfully delivered
                
            except Exception as e:
                self.logger.warning(f"Failed to deliver to {endpoint}: {str(e)}")
                continue

    async def _deliver_http(self, endpoint: str, message: AgentMessage):
        """Deliver message via HTTP."""
        try:
            import urllib.request
            import urllib.parse
            
            data = {
                "type": "message",
                "message": asdict(message)
            }
            
            req_data = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(
                endpoint,
                data=req_data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(req, timeout=10) as response:
                result = json.loads(response.read().decode('utf-8'))
                
            self.logger.info(f"Message delivered to {endpoint}")
            
        except Exception as e:
            self.logger.error(f"HTTP delivery failed: {str(e)}")
            raise

    async def _deliver_internal(self, endpoint: str, message: AgentMessage):
        """Deliver message to internal agent."""
        # Extract agent name from endpoint
        agent_name = endpoint.replace('internal://', '')
        
        # Find the agent handler
        if hasattr(self, '_internal_agents'):
            handler = self._internal_agents.get(agent_name)
            if handler:
                response = await handler.handle_message(message)
                
                # Store response in database
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE agent_messages SET response = ?, processed = TRUE WHERE message_id = ?',
                    (json.dumps(response), message.message_id)
                )
                conn.commit()
                conn.close()

    def register_internal_agent(self, agent_name: str, handler):
        """Register an internal agent handler."""
        if not hasattr(self, '_internal_agents'):
            self._internal_agents = {}
        self._internal_agents[agent_name] = handler

    def cleanup(self):
        """Cleanup resources."""
        try:
            # Mark all agents as inactive
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('UPDATE agents SET status = "inactive"')
            conn.commit()
            conn.close()
            
            self.logger.info("Agent registry cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {str(e)}")

# ==================== CROSS-PLATFORM AGENT ADAPTER ====================

class CrossPlatformAgentAdapter:
    """Adapter to make agents work across different Claude platforms."""
    
    def __init__(self, agent_name: str, capabilities: List[str]):
        self.agent_name = agent_name
        self.capabilities = capabilities
        self.registry = UniversalAgentRegistry()
        self.agent_id = f"{agent_name}_{int(datetime.now().timestamp())}"
        self.http_port = None
        self.logger = logging.getLogger(f'Agent-{agent_name}')
        
    async def initialize(self):
        """Initialize agent for cross-platform communication."""
        # Detect current platform
        platform = await self._detect_platform()
        
        # Create appropriate endpoints based on platform
        endpoints = await self._create_endpoints(platform)
        
        # Register with universal registry
        interface = AgentInterface(
            agent_id=self.agent_id,
            agent_name=self.agent_name,
            capabilities=self.capabilities,
            endpoints=endpoints,
            authentication={"type": "bearer", "token": os.environ.get("CLAUDE_API_KEY", "")},
            platform=platform
        )
        
        await self.registry.register_agent(interface)
        
        # Start platform-specific services
        await self._start_platform_services(platform)
        
        # Register as internal agent
        self.registry.register_internal_agent(self.agent_name, self)

    async def handle_message(self, message: AgentMessage) -> Dict[str, Any]:
        """Handle incoming messages from other agents."""
        try:
            action = message.payload.get('action')
            parameters = message.payload.get('parameters', {})
            
            # Override this method in subclasses
            result = await self.handle_request(action, parameters)
            
            return {
                "status": "success",
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error handling message: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def handle_request(self, action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle specific requests - override in subclasses."""
        return {"message": f"Action '{action}' not implemented"}

    async def _detect_platform(self) -> str:
        """Detect which AI coding platform is currently running."""
        # Check for Claude Code
        if os.environ.get('CLAUDE_CODE_INSTANCE') or os.path.exists('.claude'):
            return 'claude-code'
        
        # Check for VS Code with Augment
        if 'VSCODE_PID' in os.environ or 'VSCODE_CWD' in os.environ or os.path.exists('.vscode'):
            return 'augment'
            
        # Check for Windsurf
        if os.path.exists('.windsurf') or 'WINDSURF' in os.environ:
            return 'windsurf'
            
        # Check for GitHub Copilot
        if 'GITHUB_COPILOT' in os.environ:
            return 'copilot'
            
        # Check for Cursor
        if os.environ.get('CURSOR_USER_DATA') or os.path.exists('.cursor') or 'CURSOR' in os.environ:
            return 'cursor'
            
        # Check for Tabnine (Trae)
        if os.environ.get('TABNINE_HOME') or os.path.exists('.tabnine') or 'TABNINE' in os.environ:
            return 'tabnine'
            
        # Check for Qodo (formerly Codota)
        if os.environ.get('QODO_HOME') or os.path.exists('.qodo') or 'QODO' in os.environ:
            return 'qodo'
            
        # Check for Gemini CLI
        if os.environ.get('GEMINI_API_KEY') or os.environ.get('GOOGLE_AI_STUDIO_API_KEY'):
            return 'gemini-cli'
            
        # Check for Amazon Q
        if os.environ.get('AWS_Q_INSTANCE') or os.environ.get('AMAZON_Q_USER_ID') or 'AMAZON_Q' in os.environ:
            return 'amazon-q'
            
        return 'generic'

    async def _create_endpoints(self, platform: str) -> List[str]:
        """Create communication endpoints based on platform."""
        endpoints = []
        
        # Always create internal endpoint for direct communication
        endpoints.append(f"internal://{self.agent_name}")
        
        # Platform-specific endpoints would be added here
        # For now, using internal communication for reliability
        
        return endpoints

    async def _start_platform_services(self, platform: str):
        """Start platform-specific services."""
        # Create platform integration files
        await self._create_platform_integrations(platform)

    async def _create_platform_integrations(self, platform: str):
        """Create platform-specific integration files."""
        try:
            if platform == 'claude-code':
                await ClaudeCodeIntegration.create_subagent_file(self.agent_name, self.capabilities)
            elif platform == 'augment':
                await AugmentIntegration.register_extension_command(self.agent_name)
            elif platform == 'windsurf':
                await WindsurfIntegration.create_windsurf_config(self.agent_name)
            elif platform == 'cursor':
                await CursorIntegration.create_cursor_config(self.agent_name, self.capabilities)
            elif platform == 'tabnine':
                await TabnineIntegration.create_tabnine_config(self.agent_name, self.capabilities)
            elif platform == 'qodo':
                await QodoIntegration.create_qodo_config(self.agent_name, self.capabilities)
            elif platform == 'gemini-cli':
                await GeminiCLIIntegration.create_gemini_config(self.agent_name, self.capabilities)
            elif platform == 'amazon-q':
                await AmazonQIntegration.create_amazon_q_config(self.agent_name, self.capabilities)
                
        except Exception as e:
            self.logger.warning(f"Failed to create platform integrations: {str(e)}")

    async def call_agent(self, target_agent: str, action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Call another agent regardless of platform."""
        message = AgentMessage(
            message_id=f"msg_{datetime.now().timestamp()}_{hash(target_agent)}",
            source_agent=self.agent_id,
            target_agent=target_agent,
            message_type="request",
            payload={
                "action": action,
                "parameters": parameters
            },
            timestamp=datetime.now()
        )
        
        success = await self.registry.route_message(message)
        if not success:
            return {"error": "Failed to route message"}
        
        # Wait for response
        response = await self._wait_for_response(message.message_id)
        return response

    async def _wait_for_response(self, message_id: str, timeout: int = 30) -> Dict[str, Any]:
        """Wait for response to a message."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                conn = sqlite3.connect(self.registry.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT response FROM agent_messages WHERE message_id = ? AND response IS NOT NULL',
                    (message_id,)
                )
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    return json.loads(result[0])
                    
            except Exception as e:
                self.logger.error(f"Error waiting for response: {str(e)}")
            
            await asyncio.sleep(0.1)
        
        return {"error": "Timeout waiting for response"}

# ==================== ENHANCED DOCUMENTATION OVERSEER ====================

class BaseAgent:
    """Base agent class with logging and utility functions."""
    def __init__(self, context: Dict[str, Any]):
        self.context = context
        self.logger = logging.getLogger(self.__class__.__name__)
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message.""" 
        self.logger.warning(message)
        
    def log_error(self, message: str):
        """Log error message."""
        self.logger.error(message)

class EnhancedDocumentationOverseer(BaseAgent):
    """
    Enhanced Documentation Overseer with Knowledge Management
    
    Capabilities:
    - Semantic content organization and indexing
    - Automated context file generation for any topic
    - Intelligent summarization and synthesis
    - Knowledge graph construction and maintenance
    - LLM-ready context delivery
    - Physical file organization optimization
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.agent_name = "enhanced-documentation-overseer"
        self.version = "3.0.0"
        
        # Enhanced components
        self.knowledge_base: Dict[str, KnowledgeEntry] = {}
        self.semantic_model = None
        self.knowledge_graph = {}
        self.topic_clusters: Dict[str, List[str]] = {}
        self.context_templates: Dict[str, ContextTemplate] = {}
        self.file_relationships: Dict[str, List[str]] = {}
        self.dependency_graph: Dict[str, Any] = {}
        self.project_structure_analysis: Dict[str, Any] = {}
        
        # Database connections
        self.db_connections: Dict[str, sqlite3.Connection] = {}
        
        # Initialize components
        self._initialize_semantic_processing()
        self._initialize_databases()
        self._load_context_templates()
        
    def _initialize_semantic_processing(self):
        """Initialize semantic processing capabilities."""
        if SEMANTIC_AVAILABLE:
            try:
                self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.log_info("Semantic processing initialized successfully")
            except Exception as e:
                self.log_warning(f"Could not initialize semantic model: {str(e)}")
                self.semantic_model = None
        else:
            self.log_warning("Semantic processing dependencies not available.")

    def _initialize_databases(self):
        """Initialize SQLite databases for knowledge management."""
        project_root = Path(self.context.get('project_root', '.'))
        data_dir = project_root / 'data' / 'knowledge_management'
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Knowledge base database
        kb_db_path = data_dir / 'knowledge_base.db'
        self.db_connections['knowledge_base'] = sqlite3.connect(str(kb_db_path), check_same_thread=False)
        self._create_knowledge_base_schema()
        
        # Context database
        context_db_path = data_dir / 'context_management.db'
        self.db_connections['context'] = sqlite3.connect(str(context_db_path), check_same_thread=False)
        self._create_context_schema()

    def _create_knowledge_base_schema(self):
        """Create knowledge base database schema."""
        cursor = self.db_connections['knowledge_base'].cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_entries (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                topic TEXT NOT NULL,
                tags TEXT NOT NULL,
                source_file TEXT NOT NULL,
                context_level TEXT NOT NULL,
                importance_score REAL DEFAULT 0.5,
                access_count INTEGER DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entry_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_id TEXT NOT NULL,
                related_entry_id TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                strength REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entry_id) REFERENCES knowledge_entries (id),
                FOREIGN KEY (related_entry_id) REFERENCES knowledge_entries (id)
            )
        ''')
        
        self.db_connections['knowledge_base'].commit()

    def _create_context_schema(self):
        """Create context management database schema."""
        cursor = self.db_connections['context'].cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS context_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                level TEXT NOT NULL,
                file_path TEXT NOT NULL,
                size INTEGER,
                token_count INTEGER,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.db_connections['context'].commit()

    def _load_context_templates(self):
        """Load predefined context templates."""
        self.context_templates = {
            'overview': ContextTemplate(
                topic="",
                level="overview",
                sections=["Summary", "Key Concepts", "Quick References"],
                max_tokens=1500,
                include_examples=False,
                include_references=True
            ),
            'detailed': ContextTemplate(
                topic="",
                level="detailed",
                sections=["Introduction", "Core Concepts", "Implementation Details", "Examples", "Best Practices"],
                max_tokens=3000,
                include_examples=True,
                include_references=True
            ),
            'technical': ContextTemplate(
                topic="",
                level="technical",
                sections=["Architecture", "API Reference", "Code Examples", "Configuration", "Troubleshooting"],
                max_tokens=4000,
                include_examples=True,
                include_references=True
            ),
            'reference': ContextTemplate(
                topic="",
                level="reference",
                sections=["API Documentation", "Parameters", "Return Values", "Examples", "Related Functions"],
                max_tokens=2000,
                include_examples=True,
                include_references=False
            )
        }

    async def execute(self, context: Dict[str, Any]) -> AgentResult:
        """Execute enhanced documentation overseer tasks."""
        task_info = context.get('task', {})
        if isinstance(task_info, dict):
            task_name = task_info.get('name', 'unknown')
            parameters = task_info.get('parameters', {})
        else:
            task_name = context.get('task_name', context.get('task', 'unknown'))
            parameters = context.get('parameters', context.get('params', {}))
        
        self.log_info(f"Executing task: {task_name}")
        
        try:
            # Main task routing
            if task_name == "build_knowledge_base":
                return await self._build_knowledge_base(parameters)
            elif task_name == "generate_context_files":
                return await self._generate_context_files(parameters)
            elif task_name == "llm_context_delivery":
                return await self._llm_context_delivery(parameters)
            elif task_name == "intelligent_file_organization":
                return await self._intelligent_file_organization(parameters)
            elif task_name == "audit_documentation":
                return await self._audit_documentation(parameters)
            elif task_name == "semantic_organization":
                return await self._semantic_organization(parameters)
            elif task_name == "knowledge_synthesis":
                return await self._knowledge_synthesis(parameters)
            elif task_name == "maintain_knowledge_graph":
                return await self._maintain_knowledge_graph(parameters)
            elif task_name == "content_summarization":
                return await self._content_summarization(parameters)
            elif task_name == "topic_clustering":
                return await self._topic_clustering(parameters)
            elif task_name == "context_extraction":
                return await self._context_extraction(parameters)
            else:
                available_tasks = [
                    "build_knowledge_base", "generate_context_files", "llm_context_delivery",
                    "intelligent_file_organization", "audit_documentation", "semantic_organization",
                    "knowledge_synthesis", "maintain_knowledge_graph", "content_summarization",
                    "topic_clustering", "context_extraction"
                ]
                return AgentResult(
                    status="error",
                    data={"error": f"Unknown task: {task_name}", "available_tasks": available_tasks},
                    error=f"Unknown task: {task_name}"
                )
                
        except Exception as e:
            self.log_error(f"Task execution failed: {str(e)}")
            return AgentResult(status="error", data={"error": str(e)}, error=str(e))

    async def _build_knowledge_base(self, params: Dict[str, Any]) -> AgentResult:
        """Build comprehensive knowledge base with semantic indexing."""
        self.log_info("Building comprehensive knowledge base")
        
        result = AgentResult(
            status="success",
            data={
                "knowledge_base_stats": {},
                "semantic_index": {},
                "topic_distribution": {},
                "indexing_metadata": {}
            }
        )
        
        try:
            # Scan all content files
            content_files = await self._scan_all_content_files()
            self.log_info(f"Found {len(content_files)} content files")
            
            # Process each file into knowledge entries
            knowledge_entries = []
            for file_path in content_files:
                entries = await self._extract_knowledge_entries(file_path)
                knowledge_entries.extend(entries)
            
            # Generate semantic embeddings
            if self.semantic_model and params.get("generate_embeddings", True):
                embeddings = await self._generate_embeddings(knowledge_entries)
                result.data["semantic_index"]["embeddings_generated"] = len(embeddings)
            
            # Cluster by topics
            if params.get("cluster_topics", True):
                clusters = await self._cluster_knowledge_entries(knowledge_entries)
                result.data["topic_distribution"] = clusters
            
            # Store knowledge base
            await self._store_knowledge_entries(knowledge_entries)
            self.knowledge_base = {entry.id: entry for entry in knowledge_entries}
            
            result.data["knowledge_base_stats"] = {
                "total_entries": len(knowledge_entries),
                "unique_topics": len(set(entry.topic for entry in knowledge_entries)),
                "total_tags": len(set(tag for entry in knowledge_entries for tag in entry.tags)),
                "files_processed": len(content_files)
            }
            
            self.log_info(f"Knowledge base built with {len(knowledge_entries)} entries")
            
        except Exception as e:
            self.log_error(f"Failed to build knowledge base: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _generate_context_files(self, params: Dict[str, Any]) -> AgentResult:
        """Generate standardized context files for topics."""
        self.log_info("Generating context files for topics")
        
        topics = params.get("topics", [])
        if not topics:
            topics = await self._auto_detect_topics()
        
        result = AgentResult(
            status="success",
            data={
                "context_files_generated": [],
                "topics_processed": topics,
                "master_index": {}
            }
        )
        
        try:
            for topic in topics:
                context_data = await self._generate_topic_context(topic, params)
                
                context_levels = params.get("levels", ["overview", "detailed", "technical", "reference"])
                for level in context_levels:
                    context_file = await self._create_context_file(topic, level, context_data)
                    if context_file:
                        result.data["context_files_generated"].append({
                            "topic": topic,
                            "level": level,
                            "file_path": context_file["path"],
                            "size": context_file["size"],
                            "token_estimate": context_file.get("token_estimate", 0),
                            "last_updated": datetime.now().isoformat()
                        })
            
            # Generate master context index
            master_index = await self._generate_master_context_index_internal(result.data["context_files_generated"])
            result.data["master_index"] = master_index
            
            self.log_info(f"Generated context files for {len(topics)} topics")
            
        except Exception as e:
            self.log_error(f"Failed to generate context files: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _llm_context_delivery(self, params: Dict[str, Any]) -> AgentResult:
        """Deliver optimized context for LLM consumption."""
        self.log_info("Preparing LLM-optimized context delivery")
        
        query = params.get("query", "")
        context_type = params.get("context_type", "comprehensive")
        max_tokens = params.get("max_tokens", 4000)
        
        result = AgentResult(
            status="success",
            data={
                "context_delivery": {},
                "llm_metadata": {},
                "ready_for_llm": True
            }
        )
        
        try:
            # Find relevant knowledge entries
            relevant_entries = await self._find_relevant_entries(query, max_results=10)
            
            # Generate structured context
            structured_context = await self._generate_structured_context(
                relevant_entries, context_type, max_tokens
            )
            
            result.data["context_delivery"] = {
                "primary_context": structured_context["primary"],
                "supporting_context": structured_context["supporting"],
                "related_topics": structured_context["related"],
                "key_concepts": structured_context["concepts"],
                "file_references": structured_context["files"]
            }
            
            result.data["llm_metadata"] = {
                "token_count_estimate": structured_context["token_estimate"],
                "context_completeness": structured_context["completeness_score"],
                "last_updated": datetime.now().isoformat(),
                "source_confidence": structured_context["confidence_score"]
            }
            
            self.log_info(f"LLM context prepared with {len(relevant_entries)} relevant entries")
            
        except Exception as e:
            self.log_error(f"Failed to prepare LLM context: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    # Helper methods implementation
    
    async def _scan_all_content_files(self) -> List[Path]:
        """Scan for all content files that can contribute to knowledge base."""
        content_extensions = {'.md', '.rst', '.txt', '.py', '.js', '.ts', '.json', '.yaml', '.yml', '.adoc'}
        exclude_patterns = {'.git', 'node_modules', '__pycache__', '.venv', 'venv', 'dist', 'build'}
        content_files = []
        
        project_root = Path(self.context.get('project_root', '.'))
        
        for file_path in project_root.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix.lower() in content_extensions and
                not any(exclude in str(file_path) for exclude in exclude_patterns)):
                content_files.append(file_path)
        
        return content_files

    async def _extract_knowledge_entries(self, file_path: Path) -> List[KnowledgeEntry]:
        """Extract knowledge entries from a file."""
        entries = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if file_path.suffix == '.md':
                entries.extend(await self._extract_markdown_entries(file_path, content))
            elif file_path.suffix in {'.py', '.js', '.ts'}:
                entries.extend(await self._extract_code_entries(file_path, content))
            elif file_path.suffix in {'.json', '.yaml', '.yml'}:
                entries.extend(await self._extract_config_entries(file_path, content))
            else:
                entry = await self._create_generic_entry(file_path, content)
                if entry:
                    entries.append(entry)
                    
        except Exception as e:
            self.log_warning(f"Could not extract knowledge from {file_path}: {str(e)}")
        
        return entries

    async def _extract_markdown_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract structured knowledge from markdown files."""
        entries = []
        
        # Split by headers
        sections = re.split(r'^(#+)\s+(.+)$', content, flags=re.MULTILINE)
        
        for i in range(1, len(sections), 3):
            if i + 2 < len(sections):
                header_level = sections[i]
                title = sections[i + 1].strip()
                section_content = sections[i + 2].strip()
                
                if len(section_content) > 100:  # Only substantial content
                    entry = KnowledgeEntry(
                        id=hashlib.md5(f"{file_path}:{title}".encode()).hexdigest(),
                        title=title,
                        content=section_content,
                        topic=await self._infer_topic(title, section_content),
                        tags=await self._extract_tags(section_content),
                        source_file=str(file_path),
                        last_updated=datetime.fromtimestamp(file_path.stat().st_mtime),
                        context_level="detailed"
                    )
                    entries.append(entry)
        
        return entries

    async def _extract_code_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from code files."""
        entries = []
        
        try:
            if file_path.suffix == '.py':
                entries.extend(await self._extract_python_entries(file_path, content))
            elif file_path.suffix in {'.js', '.ts'}:
                entries.extend(await self._extract_javascript_entries(file_path, content))
                
        except Exception as e:
            self.log_warning(f"Could not extract code entries from {file_path}: {str(e)}")
        
        return entries

    async def _extract_python_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from Python files."""
        entries = []
        
        # Extract module docstring
        module_docstring_match = re.search(r'^"""(.*?)"""', content, re.DOTALL | re.MULTILINE)
        if module_docstring_match:
            docstring = module_docstring_match.group(1).strip()
            if len(docstring) > 50:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:module".encode()).hexdigest(),
                    title=f"{file_path.stem} Module",
                    content=docstring,
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["python", "module", "documentation"],
                    source_file=str(file_path),
                    context_level="overview"
                )
                entries.append(entry)
        
        return entries

    async def _extract_javascript_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from JavaScript/TypeScript files."""
        entries = []
        
        # Extract JSDoc comments
        jsdoc_pattern = r'/\*\*(.*?)\*/'
        for match in re.finditer(jsdoc_pattern, content, re.DOTALL):
            comment = match.group(1).strip()
            comment = re.sub(r'^\s*\*\s?', '', comment, flags=re.MULTILINE)
            
            if len(comment) > 50:
                entry = KnowledgeEntry(
                    id=hashlib.md5(f"{file_path}:jsdoc:{match.start()}".encode()).hexdigest(),
                    title=f"Documentation from {file_path.name}",
                    content=comment,
                    topic=await self._infer_topic_from_filename(file_path),
                    tags=["javascript", "typescript", "documentation"],
                    source_file=str(file_path),
                    context_level="technical"
                )
                entries.append(entry)
        
        return entries

    async def _extract_config_entries(self, file_path: Path, content: str) -> List[KnowledgeEntry]:
        """Extract knowledge from configuration files."""
        entries = []
        
        try:
            if file_path.suffix == '.json':
                data = json.loads(content)
            elif file_path.suffix in {'.yaml', '.yml'}:
                data = yaml.safe_load(content)
            else:
                return entries
            
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (dict, list)) and len(str(value)) > 100:
                        entry = KnowledgeEntry(
                            id=hashlib.md5(f"{file_path}:config:{key}".encode()).hexdigest(),
                            title=f"Configuration: {key}",
                            content=json.dumps(value, indent=2) if isinstance(value, (dict, list)) else str(value),
                            topic=f"configuration-{file_path.stem}",
                            tags=["configuration", key.lower(), file_path.suffix[1:]],
                            source_file=str(file_path),
                            context_level="reference"
                        )
                        entries.append(entry)
                        
        except Exception as e:
            self.log_warning(f"Could not parse config file {file_path}: {str(e)}")
        
        return entries

    async def _create_generic_entry(self, file_path: Path, content: str) -> Optional[KnowledgeEntry]:
        """Create a generic knowledge entry for text files."""
        if len(content.strip()) < 100:
            return None
        
        preview = content[:500] + "..." if len(content) > 500 else content
        
        return KnowledgeEntry(
            id=hashlib.md5(f"{file_path}:generic".encode()).hexdigest(),
            title=f"Content from {file_path.name}",
            content=preview,
            topic=await self._infer_topic_from_filename(file_path),
            tags=["text", "generic", file_path.suffix[1:] if file_path.suffix else "unknown"],
            source_file=str(file_path),
            context_level="overview"
        )

    async def _infer_topic(self, title: str, content: str) -> str:
        """Infer topic from title and content."""
        tech_keywords = {
            'react': 'frontend-react',
            'vue': 'frontend-vue',
            'angular': 'frontend-angular',
            'python': 'backend-python',
            'javascript': 'frontend-javascript',
            'typescript': 'frontend-typescript',
            'node': 'backend-nodejs',
            'api': 'backend-api',
            'database': 'database',
            'docker': 'devops',
            'test': 'testing',
            'ui': 'frontend-ui',
            'documentation': 'documentation'
        }
        
        text = (title + " " + content).lower()
        
        for keyword, topic in tech_keywords.items():
            if keyword in text:
                return topic
        
        return "general"

    async def _infer_topic_from_filename(self, file_path: Path) -> str:
        """Infer topic from filename and path."""
        path_str = str(file_path).lower()
        
        if 'frontend' in path_str or 'ui' in path_str:
            return 'frontend'
        elif 'backend' in path_str or 'server' in path_str:
            return 'backend'
        elif 'test' in path_str:
            return 'testing'
        elif 'doc' in path_str:
            return 'documentation'
        elif 'config' in path_str:
            return 'configuration'
        else:
            return 'general'

    async def _extract_tags(self, content: str) -> List[str]:
        """Extract relevant tags from content."""
        tags = []
        
        tech_terms = [
            'api', 'rest', 'websocket', 'react', 'vue', 'python', 'javascript',
            'docker', 'database', 'testing', 'security', 'performance'
        ]
        
        content_lower = content.lower()
        for term in tech_terms:
            if term in content_lower:
                tags.append(term.replace(' ', '-'))
        
        return tags[:10]

    async def _generate_embeddings(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Generate semantic embeddings for knowledge entries."""
        if not self.semantic_model:
            return {}
        
        embeddings = {}
        texts = [f"{entry.title} {entry.content}" for entry in knowledge_entries]
        
        try:
            vectors = self.semantic_model.encode(texts)
            for i, entry in enumerate(knowledge_entries):
                entry.embedding = vectors[i]
                embeddings[entry.id] = vectors[i].tolist()
                
        except Exception as e:
            self.log_error(f"Error generating embeddings: {str(e)}")
        
        return embeddings

    async def _cluster_knowledge_entries(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Cluster knowledge entries by topic similarity."""
        if not self.semantic_model or not SEMANTIC_AVAILABLE:
            return await self._simple_topic_clustering(knowledge_entries)
        
        try:
            embeddings = [entry.embedding for entry in knowledge_entries if entry.embedding is not None]
            if not embeddings:
                return await self._simple_topic_clustering(knowledge_entries)
            
            n_clusters = min(10, len(embeddings) // 5 + 1)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings)
            
            clusters = defaultdict(list)
            for i, entry in enumerate([e for e in knowledge_entries if e.embedding is not None]):
                cluster_id = f"cluster_{cluster_labels[i]}"
                clusters[cluster_id].append({
                    "id": entry.id,
                    "title": entry.title,
                    "topic": entry.topic
                })
            
            return dict(clusters)
            
        except Exception as e:
            self.log_error(f"Error in semantic clustering: {str(e)}")
            return await self._simple_topic_clustering(knowledge_entries)

    async def _simple_topic_clustering(self, knowledge_entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Simple topic-based clustering fallback."""
        clusters = defaultdict(list)
        
        for entry in knowledge_entries:
            clusters[entry.topic].append({
                "id": entry.id,
                "title": entry.title,
                "topic": entry.topic
            })
        
        return dict(clusters)

    async def _store_knowledge_entries(self, knowledge_entries: List[KnowledgeEntry]):
        """Store knowledge entries in database."""
        cursor = self.db_connections['knowledge_base'].cursor()
        
        for entry in knowledge_entries:
            cursor.execute('''
                INSERT OR REPLACE INTO knowledge_entries 
                (id, title, content, topic, tags, source_file, context_level, importance_score, access_count, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                entry.id, entry.title, entry.content, entry.topic,
                json.dumps(entry.tags), entry.source_file, entry.context_level,
                entry.importance_score, entry.access_count, entry.last_updated
            ))
        
        self.db_connections['knowledge_base'].commit()

    async def _auto_detect_topics(self) -> List[str]:
        """Auto-detect topics from knowledge base."""
        if not self.knowledge_base:
            return ["general", "documentation", "code", "configuration"]
        
        topics = set()
        for entry in self.knowledge_base.values():
            topics.add(entry.topic)
        
        return list(topics)

    async def _generate_topic_context(self, topic: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive context data for a topic."""
        context_data = {
            "topic": topic,
            "overview": "",
            "key_concepts": [],
            "related_files": [],
            "examples": [],
            "last_updated": datetime.now().isoformat()
        }
        
        related_entries = [entry for entry in self.knowledge_base.values() if entry.topic == topic]
        
        if related_entries:
            context_data["overview"] = await self._synthesize_topic_overview(related_entries)
            context_data["key_concepts"] = await self._extract_key_concepts(related_entries)
            context_data["related_files"] = list(set(entry.source_file for entry in related_entries))
            context_data["examples"] = await self._extract_examples(related_entries)
        
        return context_data

    async def _synthesize_topic_overview(self, entries: List[KnowledgeEntry]) -> str:
        """Synthesize an overview from multiple knowledge entries."""
        if not entries:
            return "No information available for this topic."
        
        sorted_entries = sorted(entries, key=lambda x: x.importance_score, reverse=True)
        top_entries = sorted_entries[:3]
        
        overview_parts = []
        for entry in top_entries:
            first_sentence = entry.content.split('.')[0] + '.'
            if len(first_sentence) > 200:
                first_sentence = entry.content[:200] + "..."
            overview_parts.append(f"**{entry.title}**: {first_sentence}")
        
        return "\n\n".join(overview_parts)

    async def _extract_key_concepts(self, entries: List[KnowledgeEntry]) -> List[str]:
        """Extract key concepts from entries."""
        concepts = set()
        
        for entry in entries:
            concepts.update(entry.tags)
            title_words = re.findall(r'\b[A-Za-z]{3,}\b', entry.title)
            concepts.update(word.lower() for word in title_words)
        
        filtered_concepts = [c for c in concepts if len(c) > 2 and c not in {'the', 'and', 'for', 'with'}]
        return sorted(filtered_concepts)[:10]

    async def _extract_examples(self, entries: List[KnowledgeEntry]) -> List[Dict[str, str]]:
        """Extract code examples and usage examples from entries."""
        examples = []
        
        for entry in entries:
            code_blocks = re.findall(r'``````', entry.content, re.DOTALL)
            for code in code_blocks:
                if len(code.strip()) > 20:
                    examples.append({
                        "type": "code",
                        "content": code.strip(),
                        "source": entry.title
                    })
        
        return examples[:5]

    async def _create_context_file(self, topic: str, level: str, context_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a context file for a specific topic and detail level."""
        try:
            project_root = Path(self.context.get('project_root', '.'))
            context_dir = project_root / 'contexts'
            context_dir.mkdir(exist_ok=True)
            
            filename = f"{topic.lower().replace(' ', '_').replace('-', '_')}_{level}.md"
            file_path = context_dir / filename
            
            content = await self._generate_context_content(topic, level, context_data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            token_estimate = len(content.split()) * 1.3
            
            return {
                "path": str(file_path),
                "size": len(content),
                "topic": topic,
                "level": level,
                "token_estimate": int(token_estimate)
            }
            
        except Exception as e:
            self.log_error(f"Failed to create context file for {topic}/{level}: {str(e)}")
            return None

    async def _generate_context_content(self, topic: str, level: str, context_data: Dict[str, Any]) -> str:
        """Generate context content based on the specified level."""
        if level == "overview":
            return await self._generate_overview_context(topic, context_data)
        elif level == "detailed":
            return await self._generate_detailed_context(topic, context_data)
        elif level == "technical":
            return await self._generate_technical_context(topic, context_data)
        elif level == "reference":
            return await self._generate_reference_context(topic, context_data)
        else:
            return await self._generate_overview_context(topic, context_data)

    async def _generate_overview_context(self, topic: str, context_data: Dict[str, Any]) -> str:
        """Generate overview-level context content."""
        content = f"""# {topic.title()} - Overview Context

## Summary
{context_data.get('overview', 'Overview not available')}

## Key Concepts
{chr(10).join(f"- **{concept}**" for concept in context_data.get('key_concepts', [])[:5])}

## Quick References
{chr(10).join(f"- `{Path(ref).name}`" for ref in context_data.get('related_files', [])[:5])}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_detailed_context(self, topic: str, context_data: Dict[str, Any]) -> str:
        """Generate detailed-level context content."""
        content = f"""# {topic.title()} - Detailed Context

## Introduction
{context_data.get('overview', 'No overview available')}

## Core Concepts
{chr(10).join(f"### {concept.title()}" + chr(10) + f"Key concept in {topic}" for concept in context_data.get('key_concepts', [])[:8])}

## Implementation Details
Based on analysis of related files:
{chr(10).join(f"- **{Path(file).name}**: {Path(file).suffix[1:].upper()} file" for file in context_data.get('related_files', [])[:10])}

## Examples
{await self._format_examples(context_data.get('examples', []))}

## Related Files
{chr(10).join(f"- `{file}`" for file in context_data.get('related_files', []))}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated detailed context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_technical_context(self, topic: str, context_data: Dict[str, Any]) -> str:
        """Generate technical-level context content."""
        content = f"""# {topic.title()} - Technical Context

## Architecture Overview
Technical implementation details for {topic}.

{context_data.get('overview', '')}

## Code Examples
{await self._format_examples(context_data.get('examples', []), detailed=True)}

## File Structure
```
{chr(10).join(f"- {file}" for file in context_data.get('related_files', []))}
```

## Configuration
Configuration details and parameters for {topic}.

## Troubleshooting
Common issues and solutions related to {topic}.

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated technical context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _generate_reference_context(self, topic: str, context_data: Dict[str, Any]) -> str:
        """Generate reference-level context content."""
        content = f"""# {topic.title()} - Reference Context

## API Documentation
Reference documentation for {topic}.

## Parameters
{chr(10).join(f"- **{concept}**: Description for {concept}" for concept in context_data.get('key_concepts', []))}

## Return Values
Return value documentation for {topic} operations.

## Examples
{await self._format_examples(context_data.get('examples', []), detailed=True)}

## Related Functions
{chr(10).join(f"- `{Path(file).stem}`" for file in context_data.get('related_files', [])[:10])}

## Last Updated
{context_data.get('last_updated', 'Unknown')}

---
*This is an auto-generated reference context file for LLM consumption*
*Generated by Enhanced Documentation Overseer v{self.version}*
"""
        return content

    async def _format_examples(self, examples: List[Dict[str, str]], detailed: bool = False) -> str:
        """Format examples for context files."""
        if not examples:
            return "No examples available."
        
        formatted = []
        for i, example in enumerate(examples[:3]):
            if example['type'] == 'code':
                formatted.append(f"""### Example {i+1}: {example.get('source', 'Unknown')}
```
{example['content']}
```""")
            else:
                formatted.append(f"**{example.get('source', 'Example')}**: {example['content']}")
        
        return chr(10).join(formatted)

    async def _generate_master_context_index_internal(self, context_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate master index of all context files."""
        index = {
            "total_files": len(context_files),
            "by_topic": {},
            "by_level": {},
            "total_size": 0,
            "last_updated": datetime.now().isoformat()
        }
        
        for file_info in context_files:
            topic = file_info['topic']
            level = file_info['level']
            
            if topic not in index['by_topic']:
                index['by_topic'][topic] = []
            index['by_topic'][topic].append(file_info)
            
            if level not in index['by_level']:
                index['by_level'][level] = []
            index['by_level'][level].append(file_info)
            
            index['total_size'] += file_info.get('size', 0)
        
        return index

    async def _find_relevant_entries(self, query: str, max_results: int = 10) -> List[KnowledgeEntry]:
        """Find knowledge entries relevant to a query."""
        if not self.knowledge_base:
            return []
        
        relevant_entries = []
        query_lower = query.lower()
        
        for entry in self.knowledge_base.values():
            relevance_score = 0
            
            if query_lower in entry.title.lower():
                relevance_score += 2.0
            if query_lower in entry.content.lower():
                relevance_score += 1.0
            if any(tag in query_lower for tag in entry.tags):
                relevance_score += 1.5
            if query_lower in entry.topic.lower():
                relevance_score += 1.0
            
            if relevance_score > 0:
                entry.importance_score = relevance_score
                relevant_entries.append(entry)
        
        relevant_entries.sort(key=lambda x: x.importance_score, reverse=True)
        return relevant_entries[:max_results]

    async def _generate_structured_context(self, entries: List[KnowledgeEntry], context_type: str, max_tokens: int) -> Dict[str, Any]:
        """Generate structured context for LLM consumption."""
        structured = {
            "primary": "",
            "supporting": "",
            "related": [],
            "concepts": [],
            "files": [],
            "token_estimate": 0,
            "completeness_score": 0.0,
            "confidence_score": 0.0
        }
        
        if not entries:
            return structured
        
        primary_entries = entries[:3]
        supporting_entries = entries[3:6]
        
        structured['primary'] = chr(10).join(f"**{entry.title}**: {entry.content[:300]}..." for entry in primary_entries)
        structured['supporting'] = chr(10).join(f"- {entry.title}" for entry in supporting_entries)
        structured['related'] = list(set(entry.topic for entry in entries))
        structured['concepts'] = list(set(tag for entry in entries for tag in entry.tags))
        structured['files'] = list(set(entry.source_file for entry in entries))
        
        structured['token_estimate'] = len(structured['primary'].split()) + len(structured['supporting'].split())
        structured['completeness_score'] = min(1.0, len(entries) / 10.0)
        structured['confidence_score'] = sum(entry.importance_score for entry in entries) / len(entries) if entries else 0.0
        
        return structured

    # Additional specialized methods for other tasks
    
    async def _intelligent_file_organization(self, params: Dict[str, Any]) -> AgentResult:
        """Optimize file organization based on content analysis."""
        self.log_info("Starting intelligent file organization")
        
        result = AgentResult(
            status="success",
            data={
                "reorganization_suggestions": [],
                "duplicate_files": [],
                "optimization_metrics": {}
            }
        )
        
        try:
            # Analyze current file structure
            file_analysis = await self._analyze_file_structure()
            
            # Detect duplicates and near-duplicates
            duplicates = await self._detect_duplicate_content()
            result.data["duplicate_files"] = duplicates
            
            # Generate reorganization suggestions
            suggestions = await self._generate_reorganization_suggestions(file_analysis)
            result.data["reorganization_suggestions"] = suggestions
            
            # Calculate optimization metrics
            metrics = await self._calculate_organization_metrics(file_analysis)
            result.data["optimization_metrics"] = metrics
            
            self.log_info("File organization analysis completed")
            
        except Exception as e:
            self.log_error(f"File organization failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _audit_documentation(self, params: Dict[str, Any]) -> AgentResult:
        """Audit documentation quality and completeness."""
        self.log_info("Starting documentation audit")
        
        result = AgentResult(
            status="success",
            data={
                "audit_results": {},
                "quality_score": 0.0,
                "improvement_suggestions": []
            }
        )
        
        try:
            audit_results = {
                "total_files": 0,
                "documented_files": 0,
                "outdated_files": 0,
                "missing_docs": [],
                "quality_issues": []
            }
            
            # Scan all files for documentation
            content_files = await self._scan_all_content_files()
            audit_results["total_files"] = len(content_files)
            
            for file_path in content_files:
                doc_quality = await self._assess_file_documentation(file_path)
                if doc_quality["has_documentation"]:
                    audit_results["documented_files"] += 1
                else:
                    audit_results["missing_docs"].append(str(file_path))
                
                if doc_quality["is_outdated"]:
                    audit_results["outdated_files"] += 1
                
                audit_results["quality_issues"].extend(doc_quality["issues"])
            
            # Calculate quality score
            quality_score = audit_results["documented_files"] / audit_results["total_files"] if audit_results["total_files"] > 0 else 0.0
            result.data["quality_score"] = quality_score
            result.data["audit_results"] = audit_results
            
            # Generate improvement suggestions
            suggestions = await self._generate_doc_improvement_suggestions(audit_results)
            result.data["improvement_suggestions"] = suggestions
            
            self.log_info(f"Documentation audit completed. Quality score: {quality_score:.2f}")
            
        except Exception as e:
            self.log_error(f"Documentation audit failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _semantic_organization(self, params: Dict[str, Any]) -> AgentResult:
        """Organize content using semantic similarity."""
        self.log_info("Starting semantic organization")
        
        result = AgentResult(
            status="success",
            data={
                "semantic_clusters": {},
                "similarity_matrix": {},
                "organization_recommendations": []
            }
        )
        
        if not SEMANTIC_AVAILABLE:
            result.status = "error"
            result.error = "Semantic processing dependencies not available"
            return result
        
        try:
            # Generate semantic clusters
            if self.knowledge_base:
                clusters = await self._cluster_knowledge_entries(list(self.knowledge_base.values()))
                result.data["semantic_clusters"] = clusters
            
            # Generate organization recommendations
            recommendations = await self._generate_semantic_organization_recommendations(clusters)
            result.data["organization_recommendations"] = recommendations
            
            self.log_info("Semantic organization completed")
            
        except Exception as e:
            self.log_error(f"Semantic organization failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _knowledge_synthesis(self, params: Dict[str, Any]) -> AgentResult:
        """Synthesize knowledge from multiple sources."""
        self.log_info("Starting knowledge synthesis")
        
        result = AgentResult(
            status="success",
            data={
                "synthesis_results": {},
                "combined_insights": [],
                "knowledge_gaps": []
            }
        )
        
        try:
            topic = params.get("topic", "general")
            related_entries = [entry for entry in self.knowledge_base.values() if entry.topic == topic]
            
            if not related_entries:
                result.data["synthesis_results"]["message"] = f"No knowledge entries found for topic: {topic}"
                return result
            
            # Synthesize information
            synthesis = await self._synthesize_knowledge_entries(related_entries)
            result.data["synthesis_results"] = synthesis
            
            # Identify knowledge gaps
            gaps = await self._identify_knowledge_gaps(related_entries, topic)
            result.data["knowledge_gaps"] = gaps
            
            self.log_info(f"Knowledge synthesis completed for topic: {topic}")
            
        except Exception as e:
            self.log_error(f"Knowledge synthesis failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _maintain_knowledge_graph(self, params: Dict[str, Any]) -> AgentResult:
        """Maintain and update knowledge graph relationships."""
        self.log_info("Maintaining knowledge graph")
        
        result = AgentResult(
            status="success",
            data={
                "graph_stats": {},
                "relationships_updated": 0,
                "graph_health": {}
            }
        )
        
        try:
            # Update relationships between knowledge entries
            relationships_count = await self._update_knowledge_relationships()
            result.data["relationships_updated"] = relationships_count
            
            # Calculate graph statistics
            stats = await self._calculate_graph_statistics()
            result.data["graph_stats"] = stats
            
            # Assess graph health
            health = await self._assess_graph_health()
            result.data["graph_health"] = health
            
            self.log_info(f"Knowledge graph maintenance completed. Updated {relationships_count} relationships")
            
        except Exception as e:
            self.log_error(f"Knowledge graph maintenance failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _content_summarization(self, params: Dict[str, Any]) -> AgentResult:
        """Generate content summaries at different abstraction levels."""
        self.log_info("Starting content summarization")
        
        result = AgentResult(
            status="success",
            data={
                "summaries": {},
                "abstraction_levels": ["brief", "detailed", "comprehensive"]
            }
        )
        
        try:
            target_content = params.get("content", "")
            target_files = params.get("files", [])
            
            if target_files:
                for file_path in target_files:
                    summaries = await self._generate_file_summaries(Path(file_path))
                    result.data["summaries"][file_path] = summaries
            elif target_content:
                summaries = await self._generate_content_summaries(target_content)
                result.data["summaries"]["provided_content"] = summaries
            else:
                # Summarize all knowledge base content
                summaries = await self._generate_knowledge_base_summaries()
                result.data["summaries"] = summaries
            
            self.log_info("Content summarization completed")
            
        except Exception as e:
            self.log_error(f"Content summarization failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _topic_clustering(self, params: Dict[str, Any]) -> AgentResult:
        """Perform advanced topic clustering analysis."""
        self.log_info("Starting topic clustering")
        
        result = AgentResult(
            status="success",
            data={
                "clusters": {},
                "cluster_quality": {},
                "recommendations": []
            }
        )
        
        try:
            if not self.knowledge_base:
                result.data["message"] = "No knowledge base available for clustering"
                return result
            
            # Perform clustering
            clusters = await self._cluster_knowledge_entries(list(self.knowledge_base.values()))
            result.data["clusters"] = clusters
            
            # Assess cluster quality
            quality = await self._assess_cluster_quality(clusters)
            result.data["cluster_quality"] = quality
            
            # Generate recommendations
            recommendations = await self._generate_clustering_recommendations(clusters, quality)
            result.data["recommendations"] = recommendations
            
            self.log_info(f"Topic clustering completed with {len(clusters)} clusters")
            
        except Exception as e:
            self.log_error(f"Topic clustering failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    async def _context_extraction(self, params: Dict[str, Any]) -> AgentResult:
        """Extract context for specific queries or topics."""
        self.log_info("Starting context extraction")
        
        result = AgentResult(
            status="success",
            data={
                "extracted_context": {},
                "relevance_scores": {},
                "context_metadata": {}
            }
        )
        
        try:
            query = params.get("query", "")
            max_context_size = params.get("max_size", 4000)
            
            if not query:
                result.status = "error"
                result.error = "No query provided for context extraction"
                return result
            
            # Find relevant entries
            relevant_entries = await self._find_relevant_entries(query, max_results=15)
            
            # Extract and structure context
            extracted_context = await self._extract_structured_context(relevant_entries, max_context_size)
            result.data["extracted_context"] = extracted_context
            
            # Calculate relevance scores
            relevance_scores = {entry.id: entry.importance_score for entry in relevant_entries}
            result.data["relevance_scores"] = relevance_scores
            
            # Generate metadata
            metadata = {
                "query": query,
                "entries_found": len(relevant_entries),
                "total_size": len(str(extracted_context)),
                "extraction_timestamp": datetime.now().isoformat()
            }
            result.data["context_metadata"] = metadata
            
            self.log_info(f"Context extraction completed for query: '{query[:50]}...'")
            
        except Exception as e:
            self.log_error(f"Context extraction failed: {str(e)}")
            result.status = "error"
            result.error = str(e)
        
        return result

    # Helper methods for the new functionality
    
    async def _analyze_file_structure(self) -> Dict[str, Any]:
        """Analyze current file structure for optimization opportunities."""
        return {
            "total_files": 0,
            "file_types": {},
            "directory_depth": {},
            "naming_patterns": {},
            "size_distribution": {}
        }

    async def _detect_duplicate_content(self) -> List[Dict[str, Any]]:
        """Detect duplicate and near-duplicate content."""
        return []

    async def _generate_reorganization_suggestions(self, file_analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate file reorganization suggestions."""
        return []

    async def _calculate_organization_metrics(self, file_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate organization quality metrics."""
        return {
            "organization_score": 0.0,
            "naming_consistency": 0.0,
            "directory_balance": 0.0
        }

    async def _assess_file_documentation(self, file_path: Path) -> Dict[str, Any]:
        """Assess documentation quality for a single file."""
        return {
            "has_documentation": False,
            "is_outdated": False,
            "issues": []
        }

    async def _generate_doc_improvement_suggestions(self, audit_results: Dict[str, Any]) -> List[str]:
        """Generate documentation improvement suggestions."""
        suggestions = []
        
        if audit_results["missing_docs"]:
            suggestions.append(f"Add documentation to {len(audit_results['missing_docs'])} files")
        
        if audit_results["outdated_files"] > 0:
            suggestions.append(f"Update {audit_results['outdated_files']} outdated documentation files")
        
        return suggestions

    async def _generate_semantic_organization_recommendations(self, clusters: Dict[str, Any]) -> List[str]:
        """Generate semantic organization recommendations."""
        return [
            "Consider grouping semantically similar files together",
            "Create topic-based directory structures",
            "Implement consistent naming conventions"
        ]

    async def _synthesize_knowledge_entries(self, entries: List[KnowledgeEntry]) -> Dict[str, Any]:
        """Synthesize knowledge from multiple entries."""
        return {
            "combined_overview": await self._synthesize_topic_overview(entries),
            "unified_concepts": await self._extract_key_concepts(entries),
            "synthesis_confidence": 0.8
        }

    async def _identify_knowledge_gaps(self, entries: List[KnowledgeEntry], topic: str) -> List[str]:
        """Identify gaps in knowledge coverage."""
        return [
            f"Missing implementation examples for {topic}",
            f"No troubleshooting guide for {topic}",
            f"Configuration details incomplete for {topic}"
        ]

    async def _update_knowledge_relationships(self) -> int:
        """Update relationships between knowledge entries."""
        return 0

    async def _calculate_graph_statistics(self) -> Dict[str, Any]:
        """Calculate knowledge graph statistics."""
        return {
            "total_nodes": len(self.knowledge_base),
            "total_edges": 0,
            "average_connectivity": 0.0,
            "graph_density": 0.0
        }

    async def _assess_graph_health(self) -> Dict[str, Any]:
        """Assess knowledge graph health."""
        return {
            "connectivity_score": 0.0,
            "completeness_score": 0.0,
            "consistency_score": 0.0,
            "overall_health": "good"
        }

    async def _generate_file_summaries(self, file_path: Path) -> Dict[str, str]:
        """Generate summaries for a specific file."""
        return {
            "brief": f"Brief summary of {file_path.name}",
            "detailed": f"Detailed summary of {file_path.name}",
            "comprehensive": f"Comprehensive summary of {file_path.name}"
        }

    async def _generate_content_summaries(self, content: str) -> Dict[str, str]:
        """Generate summaries for provided content."""
        return {
            "brief": content[:100] + "...",
            "detailed": content[:300] + "...",
            "comprehensive": content[:500] + "..."
        }

    async def _generate_knowledge_base_summaries(self) -> Dict[str, Dict[str, str]]:
        """Generate summaries for the entire knowledge base."""
        summaries = {}
        for topic in set(entry.topic for entry in self.knowledge_base.values()):
            topic_entries = [entry for entry in self.knowledge_base.values() if entry.topic == topic]
            summaries[topic] = {
                "brief": f"Topic {topic} with {len(topic_entries)} entries",
                "detailed": await self._synthesize_topic_overview(topic_entries[:3]),
                "comprehensive": await self._synthesize_topic_overview(topic_entries)
            }
        return summaries

    async def _assess_cluster_quality(self, clusters: Dict[str, Any]) -> Dict[str, float]:
        """Assess the quality of clustering results."""
        return {
            "silhouette_score": 0.0,
            "inertia": 0.0,
            "cluster_balance": 0.0,
            "overall_quality": 0.0
        }

    async def _generate_clustering_recommendations(self, clusters: Dict[str, Any], quality: Dict[str, float]) -> List[str]:
        """Generate recommendations based on clustering analysis."""
        recommendations = []
        
        if quality["overall_quality"] < 0.5:
            recommendations.append("Consider adjusting clustering parameters for better results")
        
        if quality["cluster_balance"] < 0.3:
            recommendations.append("Clusters are unbalanced - consider re-running with different parameters")
        
        return recommendations

    async def _extract_structured_context(self, entries: List[KnowledgeEntry], max_size: int) -> Dict[str, Any]:
        """Extract structured context from knowledge entries."""
        context = {
            "summary": "",
            "key_points": [],
            "details": "",
            "references": []
        }
        
        if entries:
            context["summary"] = await self._synthesize_topic_overview(entries[:2])
            context["key_points"] = await self._extract_key_concepts(entries)
            context["details"] = chr(10).join(entry.content[:200] + "..." for entry in entries[:3])
            context["references"] = [entry.source_file for entry in entries]
        
        return context

    def cleanup_connections(self):
        """Clean up database connections."""
        for conn in self.db_connections.values():
            try:
                conn.close()
            except Exception as e:
                self.log_warning(f"Error closing database connection: {str(e)}")

# ==================== PLATFORM INTEGRATIONS ====================

class ClaudeCodeIntegration:
    """Integration utilities for Claude Code platform."""
    
    @staticmethod
    async def create_subagent_file(agent_name: str, capabilities: List[str]):
        """Create a subagent definition file for Claude Code."""
        agent_dir = Path.home() / '.claude_agents' / 'claude_code'
        agent_dir.mkdir(parents=True, exist_ok=True)
        
        subagent_config = {
            "name": agent_name,
            "description": f"Cross-platform agent: {agent_name}",
            "capabilities": capabilities,
            "version": "1.0.0",
            "integration_type": "claude_code",
            "endpoints": [f"internal://{agent_name}"]
        }
        
        config_file = agent_dir / f"{agent_name}.json"
        with open(config_file, 'w') as f:
            json.dump(subagent_config, f, indent=2)

class AugmentIntegration:
    """Integration utilities for VS Code Augment."""
    
    @staticmethod
    async def register_extension_command(agent_name: str):
        """Register agent as VS Code extension command."""
        vscode_dir = Path.home() / '.vscode' / 'claude_agents'
        vscode_dir.mkdir(parents=True, exist_ok=True)
        
        command_config = {
            "commands": [
                {
                    "command": f"claude.agent.{agent_name}",
                    "title": f"Call {agent_name} Agent",
                    "category": "Claude Agents"
                }
            ],
            "contributes": {
                "commands": [
                    {
                        "command": f"claude.agent.{agent_name}",
                        "title": f"Call {agent_name} Agent"
                    }
                ]
            }
        }
        
        config_file = vscode_dir / f"{agent_name}_commands.json"
        with open(config_file, 'w') as f:
            json.dump(command_config, f, indent=2)

class WindsurfIntegration:
    """Integration utilities for Windsurf IDE."""
    
    @staticmethod
    async def create_windsurf_config(agent_name: str):
        """Create Windsurf-specific agent configuration."""
        windsurf_dir = Path.home() / '.windsurf' / 'claude_agents'
        windsurf_dir.mkdir(parents=True, exist_ok=True)
        
        windsurf_config = {
            "agent_name": agent_name,
            "integration_type": "windsurf",
            "activation_method": "command_palette",
            "shortcuts": [f"Ctrl+Shift+A, {agent_name[0].upper()}"]
        }
        
        config_file = windsurf_dir / f"{agent_name}.windsurf.json"
        with open(config_file, 'w') as f:
            json.dump(windsurf_config, f, indent=2)

class CursorIntegration:
    """Integration utilities for Cursor AI IDE."""
    
    @staticmethod
    async def create_cursor_config(agent_name: str, capabilities: List[str]):
        """Create Cursor-specific agent configuration."""
        cursor_dir = Path.home() / '.cursor' / 'agents'
        cursor_dir.mkdir(parents=True, exist_ok=True)
        
        cursor_config = {
            "name": agent_name,
            "description": f"Cross-platform agent: {agent_name}",
            "capabilities": capabilities,
            "integration_type": "cursor",
            "activation": {
                "command": f"@{agent_name}",
                "shortcuts": [f"Ctrl+Shift+{agent_name[0].upper()}"],
                "context_menu": True
            },
            "api_endpoint": f"http://localhost:8765/api/agents/{agent_name}",
            "version": "1.0.0"
        }
        
        config_file = cursor_dir / f"{agent_name}.cursor.json"
        with open(config_file, 'w') as f:
            json.dump(cursor_config, f, indent=2)
        
        # Create Cursor extension manifest
        extension_manifest = {
            "name": f"{agent_name}-agent",
            "displayName": f"{agent_name.title()} Agent",
            "description": f"Cross-platform agent integration for {agent_name}",
            "version": "1.0.0",
            "engines": {"cursor": "^1.0.0"},
            "categories": ["AI", "Productivity"],
            "activationEvents": [f"onCommand:cursor.agent.{agent_name}"],
            "main": "./extension.js",
            "contributes": {
                "commands": [
                    {
                        "command": f"cursor.agent.{agent_name}",
                        "title": f"Call {agent_name.title()} Agent",
                        "category": "AI Agents"
                    }
                ],
                "keybindings": [
                    {
                        "command": f"cursor.agent.{agent_name}",
                        "key": f"ctrl+shift+{agent_name[0]}",
                        "when": "editorTextFocus"
                    }
                ]
            }
        }
        
        manifest_file = cursor_dir / f"{agent_name}_manifest.json"
        with open(manifest_file, 'w') as f:
            json.dump(extension_manifest, f, indent=2)

class TabnineIntegration:
    """Integration utilities for Tabnine AI coding assistant."""
    
    @staticmethod
    async def create_tabnine_config(agent_name: str, capabilities: List[str]):
        """Create Tabnine-specific agent configuration."""
        tabnine_dir = Path.home() / '.tabnine' / 'agents'
        tabnine_dir.mkdir(parents=True, exist_ok=True)
        
        tabnine_config = {
            "agent_id": agent_name,
            "agent_name": agent_name,
            "capabilities": capabilities,
            "integration_type": "tabnine",
            "provider": "custom_agent",
            "endpoint": f"http://localhost:8765/api/agents/{agent_name}",
            "activation": {
                "trigger_phrase": f"@{agent_name}",
                "context_aware": True,
                "auto_complete": True
            },
            "settings": {
                "enabled": True,
                "priority": "high",
                "response_format": "completion"
            }
        }
        
        config_file = tabnine_dir / f"{agent_name}.tabnine.json"
        with open(config_file, 'w') as f:
            json.dump(tabnine_config, f, indent=2)
        
        # Create Tabnine plugin configuration
        plugin_config = {
            "name": f"{agent_name}_agent_plugin",
            "version": "1.0.0",
            "type": "custom_agent",
            "main": f"{agent_name}.py",
            "metadata": {
                "description": f"Cross-platform agent integration for {agent_name}",
                "author": "Universal Agent Framework",
                "tags": capabilities
            }
        }
        
        plugin_file = tabnine_dir / f"{agent_name}_plugin.json"
        with open(plugin_file, 'w') as f:
            json.dump(plugin_config, f, indent=2)

class QodoIntegration:
    """Integration utilities for Qodo (formerly Codota) AI assistant."""
    
    @staticmethod
    async def create_qodo_config(agent_name: str, capabilities: List[str]):
        """Create Qodo-specific agent configuration."""
        qodo_dir = Path.home() / '.qodo' / 'agents'
        qodo_dir.mkdir(parents=True, exist_ok=True)
        
        qodo_config = {
            "agent": {
                "name": agent_name,
                "id": f"agent_{agent_name}",
                "version": "1.0.0",
                "capabilities": capabilities
            },
            "integration": {
                "type": "qodo",
                "api_endpoint": f"http://localhost:8765/api/agents/{agent_name}",
                "authentication": "none"
            },
            "behavior": {
                "trigger_patterns": [f"@{agent_name}", f"#{agent_name}"],
                "context_window": 2048,
                "response_format": "structured"
            },
            "ui": {
                "show_in_suggestions": True,
                "icon": "agent",
                "category": "AI Agents"
            }
        }
        
        config_file = qodo_dir / f"{agent_name}.qodo.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(qodo_config, f, default_flow_style=False, indent=2)
        
        # Create Qodo extension descriptor
        extension_descriptor = {
            "extension_id": f"{agent_name}_agent",
            "display_name": f"{agent_name.title()} Agent",
            "description": f"Cross-platform integration for {agent_name} agent",
            "provider": "universal_agent_framework",
            "entry_point": f"{agent_name}_handler.py",
            "permissions": ["code_context", "file_access", "network"],
            "supported_languages": ["python", "javascript", "typescript", "java", "cpp", "go"]
        }
        
        descriptor_file = qodo_dir / f"{agent_name}_descriptor.json"
        with open(descriptor_file, 'w') as f:
            json.dump(extension_descriptor, f, indent=2)

class GeminiCLIIntegration:
    """Integration utilities for Google Gemini CLI."""
    
    @staticmethod
    async def create_gemini_config(agent_name: str, capabilities: List[str]):
        """Create Gemini CLI-specific agent configuration."""
        gemini_dir = Path.home() / '.config' / 'gemini-cli' / 'agents'
        gemini_dir.mkdir(parents=True, exist_ok=True)
        
        gemini_config = {
            "agent_config": {
                "name": agent_name,
                "description": f"Cross-platform agent: {agent_name}",
                "capabilities": capabilities,
                "version": "1.0.0"
            },
            "gemini_integration": {
                "model": "gemini-pro",
                "endpoint": f"http://localhost:8765/api/agents/{agent_name}",
                "proxy_requests": True
            },
            "cli_commands": {
                f"{agent_name}": {
                    "description": f"Invoke {agent_name} agent",
                    "usage": f"gemini {agent_name} [action] [parameters]",
                    "examples": [
                        f"gemini {agent_name} execute --task=analyze",
                        f"gemini {agent_name} help"
                    ]
                }
            },
            "environment": {
                "required_vars": ["GEMINI_API_KEY"],
                "optional_vars": ["GOOGLE_AI_STUDIO_API_KEY"]
            }
        }
        
        config_file = gemini_dir / f"{agent_name}.gemini.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(gemini_config, f, default_flow_style=False, indent=2)
        
        # Create CLI command script
        cli_script = f'''#!/usr/bin/env python3
"""
Gemini CLI integration script for {agent_name} agent
Auto-generated by Universal Agent Framework
"""

import json
import requests
import sys
import os
from typing import Dict, Any

def call_agent(action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Call the {agent_name} agent via the universal framework."""
    try:
        url = "http://localhost:8765/api"
        data = {{
            "type": "agent_call",
            "target_agent": "{agent_name}",
            "action": action,
            "parameters": parameters
        }}
        
        response = requests.post(url, json=data, timeout=30)
        return response.json()
    except Exception as e:
        return {{"error": str(e)}}

def main():
    """Main CLI entry point."""
    if len(sys.argv) < 2:
        print("Usage: {agent_name} [action] [parameters]")
        return
    
    action = sys.argv[1]
    parameters = {{}}
    
    # Parse parameters from command line
    for arg in sys.argv[2:]:
        if "=" in arg:
            key, value = arg.split("=", 1)
            key = key.lstrip("-")
            parameters[key] = value
    
    result = call_agent(action, parameters)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
'''
        
        script_file = gemini_dir / f"{agent_name}_cli.py"
        with open(script_file, 'w') as f:
            f.write(cli_script)
        
        # Make script executable
        import stat
        script_file.chmod(script_file.stat().st_mode | stat.S_IEXEC)

class AmazonQIntegration:
    """Integration utilities for Amazon Q Developer."""
    
    @staticmethod
    async def create_amazon_q_config(agent_name: str, capabilities: List[str]):
        """Create Amazon Q-specific agent configuration."""
        amazonq_dir = Path.home() / '.aws' / 'amazonq' / 'agents'
        amazonq_dir.mkdir(parents=True, exist_ok=True)
        
        amazonq_config = {
            "agent_definition": {
                "name": agent_name,
                "display_name": agent_name.title().replace('-', ' '),
                "description": f"Cross-platform agent integration for {agent_name}",
                "version": "1.0.0",
                "capabilities": capabilities
            },
            "amazon_q_integration": {
                "service_type": "custom_agent",
                "endpoint": f"http://localhost:8765/api/agents/{agent_name}",
                "authentication": {
                    "type": "none",
                    "aws_region": "us-east-1"
                }
            },
            "interaction": {
                "trigger_phrases": [f"@{agent_name}", f"call {agent_name}"],
                "context_types": ["code", "documentation", "chat"],
                "response_format": "markdown"
            },
            "permissions": {
                "code_analysis": True,
                "file_operations": True,
                "network_access": True,
                "workspace_access": True
            },
            "ui_integration": {
                "show_in_chat": True,
                "show_in_inline": True,
                "show_in_sidebar": True,
                "icon": "custom-agent"
            }
        }
        
        config_file = amazonq_dir / f"{agent_name}.amazonq.json"
        with open(config_file, 'w') as f:
            json.dump(amazonq_config, f, indent=2)
        
        # Create Amazon Q skill configuration
        skill_config = {
            "skill": {
                "name": f"{agent_name}_skill",
                "type": "custom",
                "handler": f"{agent_name}_handler",
                "metadata": {
                    "title": f"{agent_name.title()} Agent",
                    "description": f"Custom agent skill for {agent_name}",
                    "category": "development",
                    "tags": capabilities
                }
            },
            "lambda_function": {
                "function_name": f"amazonq-{agent_name}-agent",
                "runtime": "python3.9",
                "handler": "lambda_function.lambda_handler",
                "environment": {
                    "AGENT_ENDPOINT": f"http://localhost:8765/api/agents/{agent_name}",
                    "AGENT_NAME": agent_name
                }
            }
        }
        
        skill_file = amazonq_dir / f"{agent_name}_skill.json"
        with open(skill_file, 'w') as f:
            json.dump(skill_config, f, indent=2)
        
        # Create Lambda handler template
        lambda_handler = f'''import json
import urllib.request
import urllib.parse

def lambda_handler(event, context):
    """
    AWS Lambda handler for Amazon Q integration with {agent_name} agent
    """
    try:
        # Extract request details from Amazon Q event
        action = event.get('action', 'execute')
        parameters = event.get('parameters', {{}})
        
        # Call the universal agent framework
        agent_url = "http://localhost:8765/api"
        data = {{
            "type": "agent_call",
            "target_agent": "{agent_name}",
            "action": action,
            "parameters": parameters
        }}
        
        req_data = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(
            agent_url,
            data=req_data,
            headers={{'Content-Type': 'application/json'}}
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        # Format response for Amazon Q
        return {{
            'statusCode': 200,
            'body': json.dumps({{
                'response': result,
                'agent': '{agent_name}',
                'timestamp': context.aws_request_id
            }})
        }}
        
    except Exception as e:
        return {{
            'statusCode': 500,
            'body': json.dumps({{'error': str(e)}})
        }}
'''
        
        lambda_file = amazonq_dir / f"{agent_name}_lambda.py"
        with open(lambda_file, 'w') as f:
            f.write(lambda_handler)

# ==================== MAIN ORCHESTRATION AND DEPLOYMENT ====================

class UniversalAgentOrchestrator:
    """Main orchestrator for the universal agent system."""
    
    def __init__(self):
        self.registry = UniversalAgentRegistry()
        self.active_agents: Dict[str, CrossPlatformAgentAdapter] = {}
        self.logger = logging.getLogger('UniversalAgentOrchestrator')
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    async def deploy_agent(self, agent_class, agent_name: str, capabilities: List[str], context: Dict[str, Any] = None):
        """Deploy an agent across all platforms."""
        try:
            self.logger.info(f"Deploying agent: {agent_name}")
            
            # Create agent adapter
            adapter = CrossPlatformAgentAdapter(agent_name, capabilities)
            
            # Initialize the actual agent
            if context is None:
                context = {"project_root": str(Path.cwd())}
            
            agent_instance = agent_class(context)
            
            # Wrap agent in adapter
            adapter.agent_instance = agent_instance
            
            # Override handle_request to delegate to actual agent
            async def handle_request_wrapper(action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
                if action == "execute":
                    result = await agent_instance.execute(parameters)
                    # Convert AgentResult to dict
                    if hasattr(result, 'status') and hasattr(result, 'data'):
                        return {
                            "status": result.status,
                            "data": result.data,
                            "error": getattr(result, 'error', None),
                            "timestamp": getattr(result, 'timestamp', None)
                        }
                    elif isinstance(result, dict):
                        return result
                    else:
                        return {"data": result}
                else:
                    return {"error": f"Unknown action: {action}"}
            
            adapter.handle_request = handle_request_wrapper
            
            # Initialize cross-platform communication
            await adapter.initialize()
            
            # Store reference
            self.active_agents[agent_name] = adapter
            
            self.logger.info(f"Agent {agent_name} deployed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deploy agent {agent_name}: {str(e)}")
            return False

    async def call_agent(self, agent_name: str, action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Call an agent by name."""
        if agent_name in self.active_agents:
            adapter = self.active_agents[agent_name]
            return await adapter.handle_request(action, parameters)
        else:
            # Try to call via registry
            try:
                url = f"http://localhost:{self.registry.registry_port}/api"
                data = {
                    "type": "agent_call",
                    "target_agent": agent_name,
                    "action": action,
                    "parameters": parameters
                }
                
                req_data = json.dumps(data).encode('utf-8')
                req = urllib.request.Request(
                    url,
                    data=req_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                with urllib.request.urlopen(req, timeout=30) as response:
                    result = json.loads(response.read().decode('utf-8'))
                    return result
                    
            except Exception as e:
                self.logger.error(f"Failed to call agent {agent_name}: {str(e)}")
                return {"error": str(e)}

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all available agents."""
        agents = []
        
        # Add local agents
        for name, adapter in self.active_agents.items():
            agents.append({
                "name": name,
                "status": "active",
                "type": "local",
                "capabilities": adapter.capabilities
            })
        
        # Add registry agents
        registry_agents = await self.registry.discover_agents()
        for agent in registry_agents:
            agents.append({
                "name": agent.agent_name,
                "status": agent.status,
                "type": "registry",
                "capabilities": agent.capabilities,
                "platform": agent.platform
            })
        
        return agents

    def cleanup(self):
        """Cleanup all agents and resources."""
        try:
            for agent in self.active_agents.values():
                if hasattr(agent, 'agent_instance') and hasattr(agent.agent_instance, 'cleanup_connections'):
                    agent.agent_instance.cleanup_connections()
            
            self.registry.cleanup()
            self.logger.info("Universal agent orchestrator cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {str(e)}")

# ==================== CLI AND DEPLOYMENT UTILITIES ====================

def create_deployment_script(agent_configs: List[Dict[str, Any]]) -> str:
    """Create a deployment script for multiple agents."""
    script_content = f'''#!/usr/bin/env python3
"""
Auto-generated agent deployment script
Created: {datetime.now().isoformat()}
"""

import asyncio
import sys
from pathlib import Path

# Add the framework to path
sys.path.insert(0, str(Path(__file__).parent))

from Cross_Platform_Agent_Framework import (
    UniversalAgentOrchestrator,
    EnhancedDocumentationOverseer
)

async def main():
    """Deploy all configured agents."""
    orchestrator = UniversalAgentOrchestrator()
    
    print("Starting agent deployment...")
    
    # Deploy agents
    agents_deployed = 0
'''
    
    for config in agent_configs:
        script_content += f'''
    # Deploy {config['name']}
    success = await orchestrator.deploy_agent(
        {config['class_name']},
        "{config['name']}",
        {config['capabilities']},
        {config.get('context', {})}
    )
    if success:
        agents_deployed += 1
        print(f"✓ {config['name']} deployed successfully")
    else:
        print(f"✗ Failed to deploy {config['name']}")
'''
    
    script_content += f'''
    print(f"\\nDeployment complete: {{agents_deployed}}/{len(agent_configs)} agents deployed")
    
    # Keep system running
    print("\\nAgent system is running. Press Ctrl+C to stop...")
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\\nShutting down...")
        orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    return script_content

def main():
    """Main entry point for the universal agent framework."""
    print("Universal Cross-Platform Agent Framework")
    print("=========================================")
    
    import argparse
    parser = argparse.ArgumentParser(description="Universal Cross-Platform Agent Framework")
    parser.add_argument('--deploy', action='store_true', help='Deploy default agents')
    parser.add_argument('--list', action='store_true', help='List available agents')
    parser.add_argument('--call', nargs=3, metavar=('AGENT', 'ACTION', 'PARAMS'), help='Call an agent')
    parser.add_argument('--registry-port', type=int, default=8765, help='Registry port')
    
    args = parser.parse_args()
    
    async def run_main():
        orchestrator = UniversalAgentOrchestrator()
        
        try:
            if args.deploy:
                print("Deploying default agents...")
                
                # Deploy Enhanced Documentation Overseer
                context = {"project_root": str(Path.cwd())}
                success = await orchestrator.deploy_agent(
                    EnhancedDocumentationOverseer,
                    "enhanced-documentation-overseer",
                    [
                        "build_knowledge_base",
                        "generate_context_files", 
                        "llm_context_delivery",
                        "intelligent_file_organization",
                        "audit_documentation",
                        "semantic_organization",
                        "knowledge_synthesis",
                        "maintain_knowledge_graph",
                        "content_summarization",
                        "topic_clustering",
                        "context_extraction"
                    ],
                    context
                )
                
                if success:
                    print("[OK] Enhanced Documentation Overseer deployed")
                else:
                    print("[FAILED] Failed to deploy Enhanced Documentation Overseer")
                
            elif args.list:
                print("Available agents:")
                agents = await orchestrator.list_agents()
                for agent in agents:
                    print(f"  - {agent['name']} ({agent['status']}) - {', '.join(agent['capabilities'])}")
                    
            elif args.call:
                agent_name, action, params_str = args.call
                try:
                    import json
                    parameters = json.loads(params_str)
                except:
                    parameters = {"raw_params": params_str}
                
                print(f"Calling {agent_name}.{action}...")
                result = await orchestrator.call_agent(agent_name, action, parameters)
                print(f"Result: {json.dumps(result, indent=2)}")
                
            else:
                print("No action specified. Use --help for options.")
                
        except KeyboardInterrupt:
            print("\nShutting down...")
        finally:
            orchestrator.cleanup()
    
    # Run the async main function
    asyncio.run(run_main())

if __name__ == "__main__":
    main()
