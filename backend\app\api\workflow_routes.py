"""
API routes for workflow tracking and analytics
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.workflow_data_service import workflow_data_service

router = APIRouter(prefix="/api/v1/workflows", tags=["workflows"])


# Pydantic models for request/response
class WorkflowTrackingRequest(BaseModel):
    execution_id: str
    original_request: Dict[str, Any]
    generation_settings: Dict[str, Any]
    original_workflow: Dict[str, Any]
    validation_result: Dict[str, Any]
    user_id: Optional[str] = None


class ExecutionStatusUpdate(BaseModel):
    status: str = Field(..., pattern="^(submitted|processing|completed|failed|cancelled)$")
    additional_data: Optional[Dict[str, Any]] = None


class ExecutionCompletion(BaseModel):
    success: bool
    error: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    image_generated: Optional[bool] = False
    image_size: Optional[int] = None
    processing_time: Optional[float] = None
    memory_usage: Optional[float] = None
    gpu_utilization: Optional[float] = None


class ExecutionFilters(BaseModel):
    status: Optional[str] = None
    model_type: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = Field(default=100, le=1000)
    offset: int = Field(default=0, ge=0)


class TroubleshootingFilters(BaseModel):
    status: Optional[str] = None
    model_type: Optional[str] = None
    error_type: Optional[str] = None
    limit: int = Field(default=50, le=200)


# Routes
@router.post("/track/start")
async def start_workflow_tracking(
    request: WorkflowTrackingRequest,
    db: AsyncSession = Depends(get_db)
):
    """Start tracking a new workflow execution"""
    try:
        execution_id = await workflow_data_service.start_tracking(
            execution_id=request.execution_id,
            original_request=request.original_request,
            generation_settings=request.generation_settings,
            original_workflow=request.original_workflow,
            validation_result=request.validation_result,
            user_id=request.user_id
        )
        
        return {
            "success": True,
            "execution_id": execution_id,
            "message": "Workflow tracking started successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start workflow tracking: {str(e)}")


@router.put("/track/{execution_id}/status")
async def update_execution_status(
    execution_id: str,
    update: ExecutionStatusUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update execution status"""
    try:
        success = await workflow_data_service.update_execution_status(
            execution_id=execution_id,
            status=update.status,
            additional_data=update.additional_data
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        return {
            "success": True,
            "execution_id": execution_id,
            "status": update.status,
            "message": "Execution status updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update execution status: {str(e)}")


@router.post("/track/{execution_id}/complete")
async def complete_workflow_tracking(
    execution_id: str,
    completion: ExecutionCompletion,
    db: AsyncSession = Depends(get_db)
):
    """Complete workflow tracking with final results"""
    try:
        success = await workflow_data_service.complete_tracking(
            execution_id=execution_id,
            result_data=completion.dict()
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        return {
            "success": True,
            "execution_id": execution_id,
            "message": "Workflow tracking completed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to complete workflow tracking: {str(e)}")


@router.get("/executions/{execution_id}")
async def get_execution_by_id(
    execution_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get execution record by ID"""
    try:
        execution = await workflow_data_service.get_execution_by_id(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        return {
            "success": True,
            "execution": execution
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get execution: {str(e)}")


@router.get("/executions")
async def get_executions(
    status: Optional[str] = Query(None, description="Filter by execution status"),
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    date_from: Optional[datetime] = Query(None, description="Filter from date"),
    date_to: Optional[datetime] = Query(None, description="Filter to date"),
    limit: int = Query(100, le=1000, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    db: AsyncSession = Depends(get_db)
):
    """Get executions with filtering and pagination"""
    try:
        result = await workflow_data_service.get_executions(
            limit=limit,
            offset=offset,
            status=status,
            model_type=model_type,
            date_from=date_from,
            date_to=date_to
        )
        
        return {
            "success": True,
            **result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get executions: {str(e)}")


@router.get("/analytics")
async def get_workflow_analytics(
    date_from: Optional[datetime] = Query(None, description="Analytics from date"),
    date_to: Optional[datetime] = Query(None, description="Analytics to date"),
    db: AsyncSession = Depends(get_db)
):
    """Get workflow analytics for optimization"""
    try:
        analytics = await workflow_data_service.get_analytics(
            date_from=date_from,
            date_to=date_to
        )
        
        return {
            "success": True,
            "analytics": analytics,
            "date_range": {
                "from": date_from.isoformat() if date_from else None,
                "to": date_to.isoformat() if date_to else None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")


@router.get("/troubleshooting")
async def get_troubleshooting_data(
    status: Optional[str] = Query(None, description="Filter by status"),
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    error_type: Optional[str] = Query(None, description="Filter by error type"),
    limit: int = Query(50, le=200, description="Number of records to return"),
    db: AsyncSession = Depends(get_db)
):
    """Get troubleshooting data for specific issues"""
    try:
        data = await workflow_data_service.get_troubleshooting_data(
            status=status,
            model_type=model_type,
            error_type=error_type,
            limit=limit
        )
        
        return {
            "success": True,
            **data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get troubleshooting data: {str(e)}")


@router.delete("/cleanup")
async def cleanup_old_records(
    days_to_keep: int = Query(30, ge=1, le=365, description="Number of days to keep"),
    db: AsyncSession = Depends(get_db)
):
    """Clean up old workflow execution records"""
    try:
        deleted_count = await workflow_data_service.cleanup_old_records(days_to_keep)
        
        return {
            "success": True,
            "deleted_count": deleted_count,
            "days_kept": days_to_keep,
            "message": f"Cleaned up {deleted_count} old records"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cleanup old records: {str(e)}")


@router.get("/health")
async def workflow_tracking_health(db: AsyncSession = Depends(get_db)):
    """Check workflow tracking system health"""
    try:
        from app.core.database import health_check, get_database_stats
        
        # Database health
        db_health = await health_check()
        
        # Database statistics
        db_stats = await get_database_stats()
        
        # Service health
        service_health = {
            "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
            "database": db_health,
            "statistics": db_stats
        }
        
        return {
            "success": True,
            "health": service_health
        }
        
    except Exception as e:
        return {
            "success": False,
            "health": {
                "status": "unhealthy",
                "error": str(e)
            }
        }


# Export router
__all__ = ["router"]