"""
LLaVA Vision Service for Multimodal Image Analysis

This service provides comprehensive image analysis using the LLaVA (Large Language and Vision Assistant) model.
It handles image encoding, API communication with Ollama, and structured parsing of visual analysis results.

Key Features:
- Comprehensive image analysis (style, mood, composition, lighting, objects)
- Specialized analysis modes (style-focused, mood-focused, composition-focused)
- Image comparison for quality assessment
- Structured result parsing for integration with other services
- Async operation with proper error handling and logging

Dependencies:
- Ollama server running with LLaVA model loaded
- aiohttp for async HTTP communication
- Base64 encoding for image data transmission

Usage:
    async with LLaVAService() as llava:
        analysis = await llava.analyze_image('path/to/image.jpg', 'comprehensive')
        print(analysis.style_analysis)
"""

import aiohttp
import asyncio
import base64
import json
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class LLaVAAnalysisResult:
    """
    Structured result container for LLaVA image analysis
    
    Parses raw LLaVA response text into structured categories for easy access
    and integration with other services.
    """
    
    def __init__(self, raw_response: str, analysis_type: str = 'comprehensive'):
        self.raw_response = raw_response
        self.analysis_type = analysis_type
        self.timestamp = datetime.now().isoformat()
        
        # Parse structured elements from response
        self.style_analysis = self._extract_style()
        self.mood_analysis = self._extract_mood()
        self.composition = self._extract_composition()
        self.lighting = self._extract_lighting()
        self.objects = self._extract_objects()
        self.technical_quality = self._extract_quality()
        self.color_palette = self._extract_colors()
        self.keywords = self._extract_keywords()
        
        # Generate summary for quick reference
        self.summary = self._generate_summary()
    
    def _extract_style(self) -> Dict[str, Any]:
        """Extract artistic style information from analysis"""
        style_patterns = [
            r'style[:\s]+(.*?)(?:\n|\.)',
            r'artistic style[:\s]+(.*?)(?:\n|\.)',
            r'art movement[:\s]+(.*?)(?:\n|\.)',
            r'technique[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_styles = []
        for pattern in style_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_styles.extend(matches)
        
        return {
            'primary_style': extracted_styles[0] if extracted_styles else 'unknown',
            'all_styles': extracted_styles,
            'confidence': self._calculate_extraction_confidence(extracted_styles)
        }
    
    def _extract_mood(self) -> Dict[str, Any]:
        """Extract mood and atmosphere information"""
        mood_patterns = [
            r'mood[:\s]+(.*?)(?:\n|\.)',
            r'atmosphere[:\s]+(.*?)(?:\n|\.)',
            r'emotional tone[:\s]+(.*?)(?:\n|\.)',
            r'feeling[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_moods = []
        for pattern in mood_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_moods.extend(matches)
        
        return {
            'primary_mood': extracted_moods[0] if extracted_moods else 'neutral',
            'all_moods': extracted_moods,
            'confidence': self._calculate_extraction_confidence(extracted_moods)
        }
    
    def _extract_composition(self) -> Dict[str, Any]:
        """Extract composition and layout information"""
        composition_patterns = [
            r'composition[:\s]+(.*?)(?:\n|\.)',
            r'layout[:\s]+(.*?)(?:\n|\.)',
            r'arrangement[:\s]+(.*?)(?:\n|\.)',
            r'balance[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_comp = []
        for pattern in composition_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_comp.extend(matches)
        
        return {
            'primary_composition': extracted_comp[0] if extracted_comp else 'unknown',
            'all_elements': extracted_comp,
            'confidence': self._calculate_extraction_confidence(extracted_comp)
        }
    
    def _extract_lighting(self) -> Dict[str, Any]:
        """Extract lighting information"""
        lighting_patterns = [
            r'lighting[:\s]+(.*?)(?:\n|\.)',
            r'illumination[:\s]+(.*?)(?:\n|\.)',
            r'light[:\s]+(.*?)(?:\n|\.)',
            r'shadows[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_lighting = []
        for pattern in lighting_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_lighting.extend(matches)
        
        return {
            'primary_lighting': extracted_lighting[0] if extracted_lighting else 'unknown',
            'all_lighting': extracted_lighting,
            'confidence': self._calculate_extraction_confidence(extracted_lighting)
        }
    
    def _extract_objects(self) -> Dict[str, Any]:
        """Extract object and subject information"""
        object_patterns = [
            r'subjects?[:\s]+(.*?)(?:\n|\.)',
            r'objects?[:\s]+(.*?)(?:\n|\.)',
            r'main.*?(?:subject|element)[:\s]+(.*?)(?:\n|\.)',
            r'focal point[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_objects = []
        for pattern in object_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_objects.extend(matches)
        
        return {
            'primary_subjects': extracted_objects[0] if extracted_objects else 'unknown',
            'all_objects': extracted_objects,
            'confidence': self._calculate_extraction_confidence(extracted_objects)
        }
    
    def _extract_quality(self) -> Dict[str, Any]:
        """Extract technical quality information"""
        quality_patterns = [
            r'quality[:\s]+(.*?)(?:\n|\.)',
            r'technical[:\s]+(.*?)(?:\n|\.)',
            r'resolution[:\s]+(.*?)(?:\n|\.)',
            r'sharpness[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_quality = []
        for pattern in quality_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_quality.extend(matches)
        
        return {
            'overall_quality': extracted_quality[0] if extracted_quality else 'unknown',
            'quality_aspects': extracted_quality,
            'confidence': self._calculate_extraction_confidence(extracted_quality)
        }
    
    def _extract_colors(self) -> Dict[str, Any]:
        """Extract color palette information"""
        color_patterns = [
            r'color[s]?[:\s]+(.*?)(?:\n|\.)',
            r'palette[:\s]+(.*?)(?:\n|\.)',
            r'hue[:\s]+(.*?)(?:\n|\.)',
            r'tone[:\s]+(.*?)(?:\n|\.)'
        ]
        
        extracted_colors = []
        for pattern in color_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            extracted_colors.extend(matches)
        
        return {
            'primary_palette': extracted_colors[0] if extracted_colors else 'unknown',
            'color_descriptions': extracted_colors,
            'confidence': self._calculate_extraction_confidence(extracted_colors)
        }
    
    def _extract_keywords(self) -> List[str]:
        """Extract descriptive keywords from analysis"""
        # Look for explicit keyword sections
        keyword_patterns = [
            r'keywords?[:\s]+(.*?)(?:\n\n|\.$)',
            r'descriptive terms[:\s]+(.*?)(?:\n\n|\.$)',
            r'tags[:\s]+(.*?)(?:\n\n|\.$)'
        ]
        
        keywords = []
        for pattern in keyword_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE | re.DOTALL)
            for match in matches:
                # Split by common delimiters
                words = re.split(r'[,;·•\n-]+', match)
                keywords.extend([word.strip() for word in words if word.strip()])
        
        # If no explicit keywords found, extract important adjectives and nouns
        if not keywords:
            keywords = self._extract_implicit_keywords()
        
        return list(set(keywords))[:15]  # Return unique keywords, max 15
    
    def _extract_implicit_keywords(self) -> List[str]:
        """Extract keywords from the overall analysis text"""
        # Common descriptive words that make good keywords
        descriptive_patterns = [
            r'\b(realistic|abstract|impressionist|modern|vintage|classic)\b',
            r'\b(dramatic|peaceful|energetic|calm|vibrant|muted)\b',
            r'\b(warm|cool|bright|dark|colorful|monochrome)\b',
            r'\b(sharp|soft|detailed|blurred|textured|smooth)\b'
        ]
        
        keywords = []
        for pattern in descriptive_patterns:
            matches = re.findall(pattern, self.raw_response, re.IGNORECASE)
            keywords.extend(matches)
        
        return keywords
    
    def _calculate_extraction_confidence(self, extracted_items: List[str]) -> float:
        """Calculate confidence score for extracted information"""
        if not extracted_items:
            return 0.0
        
        # Base confidence on number of matches and content quality
        base_confidence = min(len(extracted_items) * 0.2, 0.8)
        
        # Boost confidence if extractions seem substantial
        avg_length = sum(len(item) for item in extracted_items) / len(extracted_items)
        length_bonus = min(avg_length / 50, 0.2)
        
        return min(base_confidence + length_bonus, 1.0)
    
    def _generate_summary(self) -> str:
        """Generate a concise summary of the analysis"""
        summary_parts = []
        
        if self.style_analysis['primary_style'] != 'unknown':
            summary_parts.append(f"Style: {self.style_analysis['primary_style']}")
        
        if self.mood_analysis['primary_mood'] != 'neutral':
            summary_parts.append(f"Mood: {self.mood_analysis['primary_mood']}")
        
        if self.objects['primary_subjects'] != 'unknown':
            summary_parts.append(f"Subject: {self.objects['primary_subjects']}")
        
        return "; ".join(summary_parts) if summary_parts else "Analysis completed"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert analysis result to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'analysis_type': self.analysis_type,
            'summary': self.summary,
            'style_analysis': self.style_analysis,
            'mood_analysis': self.mood_analysis,
            'composition': self.composition,
            'lighting': self.lighting,
            'objects': self.objects,
            'technical_quality': self.technical_quality,
            'color_palette': self.color_palette,
            'keywords': self.keywords,
            'raw_response': self.raw_response
        }

class LLaVAService:
    """
    Service for interfacing with LLaVA vision-language model via Ollama
    
    Provides comprehensive image analysis capabilities with structured results.
    Handles async communication, error management, and result parsing.
    """
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model_name = "llava:latest"
        self.session = None
        
        # Predefined analysis prompts for different use cases
        self.analysis_prompts = {
            'comprehensive': """
Analyze this image in comprehensive detail. Provide structured information about:

1. ARTISTIC STYLE: What art style, movement, or technique does this represent? (realistic, impressionist, digital art, photography, etc.)

2. MOOD AND ATMOSPHERE: What emotions and feelings does this image convey? What is the overall atmosphere?

3. COMPOSITION: How are elements arranged? What compositional techniques are used? Where does the eye focus?

4. LIGHTING: Describe the lighting conditions, direction, quality, and mood created by the lighting.

5. COLOR PALETTE: What colors dominate? Is it warm/cool, vibrant/muted, monochromatic/diverse?

6. MAIN SUBJECTS: What are the primary objects, people, or elements in the image?

7. TECHNICAL QUALITY: Comment on sharpness, detail level, resolution, and overall technical execution.

8. KEYWORDS: List 10-15 descriptive keywords that best capture this image.

Be specific and detailed in your analysis. Focus on visual elements that would help someone understand or recreate this image's characteristics.
            """,
            
            'style_focused': """
Focus specifically on analyzing the artistic style of this image:

1. ART MOVEMENT: What art movement, period, or style does this represent?
2. TECHNIQUE: What artistic techniques or methods were likely used?
3. AESTHETIC: How would you describe the overall visual aesthetic?
4. INFLUENCES: What artistic influences or inspirations are evident?
5. STYLE KEYWORDS: List specific style-related terms that describe this image.
6. MEDIUM: What medium or materials appear to have been used?
7. ARTISTIC APPROACH: Is it realistic, stylized, abstract, impressionistic, etc.?

Provide detailed analysis focused on artistic and stylistic elements.
            """,
            
            'mood_focused': """
Analyze the emotional and atmospheric qualities of this image:

1. EMOTIONAL TONE: What primary emotions does this image evoke?
2. ATMOSPHERE: Describe the overall atmosphere or mood of the scene.
3. COLOR MOOD: How do the colors contribute to the emotional impact?
4. LIGHTING MOOD: How does the lighting affect the emotional tone?
5. COMPOSITIONAL MOOD: How does the arrangement of elements create mood?
6. PSYCHOLOGICAL IMPACT: What psychological effects might this image have on viewers?
7. MOOD KEYWORDS: List terms that capture the emotional essence of this image.

Focus on the emotional and atmospheric aspects of the visual elements.
            """,
            
            'composition_focused': """
Analyze the composition and visual structure of this image:

1. COMPOSITIONAL TECHNIQUE: What compositional rules or techniques are used? (rule of thirds, golden ratio, symmetry, etc.)
2. FOCAL POINTS: Where does the eye naturally focus? What creates these focal points?
3. VISUAL FLOW: How does the eye move through the image? What guides this movement?
4. BALANCE: How is visual weight distributed? Is it balanced or intentionally unbalanced?
5. DEPTH: How is depth and dimension created in the image?
6. FRAMING: How are elements framed within the image boundaries?
7. STRUCTURAL ELEMENTS: What lines, shapes, and forms create the underlying structure?

Provide detailed analysis of the compositional and structural elements.
            """,
            
            'prompt_generation': """
Analyze this image to help generate a detailed text prompt that could recreate similar artwork:

1. STYLE DESCRIPTION: Detailed description of the artistic style and approach
2. SUBJECT MATTER: Precise description of what is depicted in the image
3. COMPOSITION DETAILS: Specific compositional elements and arrangements
4. COLOR SPECIFICATIONS: Detailed color palette and color relationships
5. LIGHTING DESCRIPTION: Specific lighting conditions and effects
6. MOOD DESCRIPTORS: Words that capture the emotional tone and atmosphere
7. TECHNICAL DETAILS: Quality, finish, and technical characteristics
8. PROMPT KEYWORDS: 15-20 specific keywords perfect for AI image generation

Focus on providing descriptions that would be useful for recreating this image through text prompts.
Be as specific and descriptive as possible.
            """
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session exists"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def analyze_image(
        self, 
        image_path: str, 
        analysis_type: str = 'comprehensive',
        custom_prompt: Optional[str] = None
    ) -> LLaVAAnalysisResult:
        """
        Analyze an image using LLaVA model
        
        Args:
            image_path: Path to the image file
            analysis_type: Type of analysis ('comprehensive', 'style_focused', etc.)
            custom_prompt: Custom analysis prompt if provided
            
        Returns:
            LLaVAAnalysisResult: Structured analysis results
            
        Raises:
            Exception: If analysis fails due to file access, API errors, or timeouts
        """
        await self._ensure_session()
        
        start_time = datetime.now()
        logger.info(f"Starting LLaVA image analysis: {image_path} ({analysis_type})")
        
        try:
            # Validate image file exists
            image_file = Path(image_path)
            if not image_file.exists():
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Read and encode image
            image_data = await self._encode_image(image_path)
            
            # Select analysis prompt
            prompt = custom_prompt or self.analysis_prompts.get(
                analysis_type, 
                self.analysis_prompts['comprehensive']
            )
            
            # Prepare request payload
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_data],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Low temperature for consistent analysis
                    "top_p": 0.9,
                    "max_tokens": 2048,
                    "num_predict": 2048
                }
            }
            
            # Make API request with timeout
            timeout = aiohttp.ClientTimeout(total=60)  # 60 second timeout
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=timeout
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"LLaVA API error {response.status}: {error_text}")
                    raise Exception(f"LLaVA API error {response.status}: {error_text}")
                
                result = await response.json()
                analysis_text = result.get('response', '')
                
                if not analysis_text or analysis_text.strip() == '':
                    logger.error("Empty response from LLaVA")
                    raise Exception("Empty response from LLaVA analysis")
                
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                logger.info(f"LLaVA analysis completed successfully in {processing_time:.2f}s")
                
                # Create structured result
                analysis_result = LLaVAAnalysisResult(analysis_text, analysis_type)
                
                return analysis_result
                
        except asyncio.TimeoutError:
            logger.error(f"LLaVA analysis timed out for {image_path}")
            raise Exception("Image analysis timed out after 60 seconds")
        except FileNotFoundError as e:
            logger.error(f"Image file not found: {str(e)}")
            raise Exception(f"Image file not found: {str(e)}")
        except aiohttp.ClientError as e:
            logger.error(f"Network error during LLaVA analysis: {str(e)}")
            raise Exception(f"Network error during analysis: {str(e)}")
        except Exception as e:
            logger.error(f"LLaVA analysis failed for {image_path}: {str(e)}")
            raise Exception(f"Image analysis failed: {str(e)}")
    
    async def _encode_image(self, image_path: str) -> str:
        """
        Encode image file to base64 for API transmission
        
        Args:
            image_path: Path to image file
            
        Returns:
            str: Base64 encoded image data
            
        Raises:
            Exception: If file cannot be read or encoded
        """
        try:
            image_path = Path(image_path)
            
            # Check file size (warn if very large)
            file_size = image_path.stat().st_size
            if file_size > 10 * 1024 * 1024:  # 10MB
                logger.warning(f"Large image file detected: {file_size / (1024*1024):.1f}MB")
            
            # Read and encode image
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded = base64.b64encode(image_data).decode('utf-8')
                
                logger.debug(f"Image encoded successfully: {len(encoded)} characters")
                return encoded
                
        except PermissionError:
            logger.error(f"Permission denied reading image: {image_path}")
            raise Exception(f"Permission denied accessing image file: {image_path}")
        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {str(e)}")
            raise Exception(f"Image encoding failed: {str(e)}")
    
    async def analyze_for_prompt_generation(self, image_path: str) -> Dict[str, Any]:
        """
        Specialized analysis for generating prompts from reference images
        
        Args:
            image_path: Path to reference image
            
        Returns:
            Dict containing structured data optimized for prompt generation
        """
        logger.info(f"Starting prompt generation analysis for: {image_path}")
        
        try:
            # Use specialized prompt for prompt generation
            analysis = await self.analyze_image(
                image_path, 
                analysis_type='prompt_generation'
            )
            
            # Convert analysis to prompt-generation friendly format
            prompt_data = {
                'image_path': image_path,
                'analysis_summary': analysis.summary,
                'style_elements': analysis.style_analysis,
                'mood_elements': analysis.mood_analysis,
                'composition_elements': analysis.composition,
                'color_elements': analysis.color_palette,
                'lighting_elements': analysis.lighting,
                'subject_elements': analysis.objects,
                'keywords': analysis.keywords,
                'raw_analysis': analysis.raw_response,
                'confidence_score': self._calculate_overall_confidence(analysis),
                'timestamp': analysis.timestamp
            }
            
            logger.info("Prompt generation analysis completed successfully")
            return prompt_data
            
        except Exception as e:
            logger.error(f"Prompt generation analysis failed: {str(e)}")
            raise Exception(f"Prompt generation analysis failed: {str(e)}")
    
    async def compare_images(
        self, 
        image1_path: str, 
        image2_path: str
    ) -> Dict[str, Any]:
        """
        Compare two images for similarity analysis
        Used for quality assessment and iterative refinement
        
        Args:
            image1_path: Path to first image (reference)
            image2_path: Path to second image (comparison)
            
        Returns:
            Dict containing comparison analysis
        """
        logger.info(f"Starting image comparison: {image1_path} vs {image2_path}")
        
        try:
            # Analyze both images separately (LLaVA doesn't support multi-image input)
            comparison_prompt = """
Compare and analyze this image focusing on:
1. Overall visual style and aesthetic
2. Composition and layout structure  
3. Color palette and color relationships
4. Lighting conditions and mood
5. Technical quality and execution
6. Subject matter and content
7. Artistic techniques used

Provide detailed analysis that would be useful for comparing with another similar image.
            """
            
            # Analyze both images with comparison-focused prompts
            analysis1 = await self.analyze_image(
                image1_path, 
                custom_prompt=comparison_prompt
            )
            
            analysis2 = await self.analyze_image(
                image2_path, 
                custom_prompt=comparison_prompt
            )
            
            # Create comparison result
            comparison_result = {
                'image1': {
                    'path': image1_path,
                    'analysis': analysis1.to_dict()
                },
                'image2': {
                    'path': image2_path,
                    'analysis': analysis2.to_dict()
                },
                'comparison_type': 'sequential_analysis',
                'similarity_indicators': self._calculate_similarity_indicators(
                    analysis1, analysis2
                ),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("Image comparison completed successfully")
            return comparison_result
            
        except Exception as e:
            logger.error(f"Image comparison failed: {str(e)}")
            raise Exception(f"Image comparison failed: {str(e)}")
    
    def _calculate_overall_confidence(self, analysis: LLaVAAnalysisResult) -> float:
        """Calculate overall confidence score for analysis quality"""
        confidence_scores = [
            analysis.style_analysis['confidence'],
            analysis.mood_analysis['confidence'],
            analysis.composition['confidence'],
            analysis.lighting['confidence'],
            analysis.objects['confidence'],
            analysis.technical_quality['confidence'],
            analysis.color_palette['confidence']
        ]
        
        # Filter out zero scores and calculate average
        valid_scores = [score for score in confidence_scores if score > 0]
        if not valid_scores:
            return 0.3  # Minimum confidence if no extractions succeeded
            
        return sum(valid_scores) / len(valid_scores)
    
    def _calculate_similarity_indicators(
        self, 
        analysis1: LLaVAAnalysisResult, 
        analysis2: LLaVAAnalysisResult
    ) -> Dict[str, Any]:
        """Calculate similarity indicators between two analyses"""
        
        # Simple keyword-based similarity (can be enhanced with embeddings)
        keywords1 = set(analysis1.keywords)
        keywords2 = set(analysis2.keywords)
        
        if keywords1 and keywords2:
            keyword_overlap = len(keywords1.intersection(keywords2))
            keyword_union = len(keywords1.union(keywords2))
            keyword_similarity = keyword_overlap / keyword_union if keyword_union > 0 else 0
        else:
            keyword_similarity = 0
        
        return {
            'keyword_similarity': keyword_similarity,
            'style_similarity_score': self._compare_analysis_elements(
                analysis1.style_analysis, analysis2.style_analysis
            ),
            'mood_similarity_score': self._compare_analysis_elements(
                analysis1.mood_analysis, analysis2.mood_analysis
            ),
            'overall_similarity_estimate': (keyword_similarity * 0.6 + 
                                          self._text_similarity_estimate(
                                              analysis1.raw_response, 
                                              analysis2.raw_response
                                          ) * 0.4)
        }
    
    def _compare_analysis_elements(
        self, 
        element1: Dict[str, Any], 
        element2: Dict[str, Any]
    ) -> float:
        """Compare individual analysis elements for similarity"""
        
        # Simple text-based comparison
        text1 = str(element1.get('primary_style', '') or element1.get('primary_mood', ''))
        text2 = str(element2.get('primary_style', '') or element2.get('primary_mood', ''))
        
        if not text1 or not text2:
            return 0.0
            
        return self._text_similarity_estimate(text1, text2)
    
    def _text_similarity_estimate(self, text1: str, text2: str) -> float:
        """Simple text similarity estimation using word overlap"""
        if not text1 or not text2:
            return 0.0
            
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
            
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check if LLaVA service is available and responding
        
        Returns:
            Dict containing health status information
        """
        try:
            await self._ensure_session()
            
            # Simple health check request
            async with self.session.get(
                f"{self.base_url}/api/tags",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    models = result.get('models', [])
                    
                    # Check if LLaVA model is available
                    llava_available = any(
                        model.get('name', '').startswith('llava') 
                        for model in models
                    )
                    
                    return {
                        'status': 'healthy',
                        'ollama_available': True,
                        'llava_model_available': llava_available,
                        'available_models': [model.get('name', '') for model in models],
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'error': f"Ollama responded with status {response.status}",
                        'timestamp': datetime.now().isoformat()
                    }
                    
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Singleton instance for dependency injection
llava_service = LLaVAService()
