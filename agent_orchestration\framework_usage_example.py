#!/usr/bin/env python3
"""
Cross Platform Agent Framework - Usage Example
Demonstrates how to use the framework in your projects.
"""

import asyncio
import json
from pathlib import Path
import sys

# Add framework to path
sys.path.insert(0, str(Path(__file__).parent))

from Cross_Platform_Agent_Framework import (
    UniversalAgentOrchestrator,
    EnhancedDocumentationOverseer
)

async def main():
    """Example usage of the Cross Platform Agent Framework."""
    print("Cross Platform Agent Framework - Usage Example")
    print("=" * 50)
    
    # Initialize the orchestrator
    orchestrator = UniversalAgentOrchestrator()
    
    try:
        # 1. Deploy an agent
        print("\n1. Deploying Enhanced Documentation Overseer...")
        
        # Set up context for the agent
        context = {
            "project_root": str(Path.cwd().parent),  # Point to comfyui-custom-frontend
            "generate_embeddings": False,  # Skip for faster operation
            "cluster_topics": True
        }
        
        success = await orchestrator.deploy_agent(
            EnhancedDocumentationOverseer,
            "doc-overseer",
            [
                "build_knowledge_base",
                "generate_context_files",
                "audit_documentation",
                "context_extraction"
            ],
            context
        )
        
        if success:
            print("   [OK] Agent deployed successfully")
        else:
            print("   [FAILED] Agent deployment failed")
            return
        
        # 2. List available agents
        print("\n2. Listing available agents...")
        agents = await orchestrator.list_agents()
        for agent in agents:
            print(f"   - {agent['name']}: {agent['status']}")
            print(f"     Capabilities: {', '.join(agent['capabilities'][:5])}...")
        
        # 3. Use agent capabilities
        print("\n3. Using agent capabilities...")
        
        # Build knowledge base
        print("\n   Building knowledge base...")
        result = await orchestrator.call_agent(
            "doc-overseer",
            "execute", 
            {
                "task": "build_knowledge_base",
                "parameters": {
                    "generate_embeddings": False,
                    "cluster_topics": True
                }
            }
        )
        
        if "data" in result:
            kb_stats = result["data"].get("knowledge_base_stats", {})
            print(f"   [OK] Knowledge base built:")
            print(f"        - Entries: {kb_stats.get('total_entries', 0)}")
            print(f"        - Topics: {kb_stats.get('unique_topics', 0)}")
            print(f"        - Files: {kb_stats.get('files_processed', 0)}")
        
        # Extract context for a specific query
        print("\n   Extracting context for 'React components'...")
        result = await orchestrator.call_agent(
            "doc-overseer",
            "execute",
            {
                "task": "context_extraction", 
                "parameters": {
                    "query": "React components",
                    "max_size": 1000
                }
            }
        )
        
        if "data" in result:
            context_data = result["data"]
            metadata = context_data.get("context_metadata", {})
            print(f"   [OK] Context extracted:")
            print(f"        - Entries found: {metadata.get('entries_found', 0)}")
            print(f"        - Context size: {metadata.get('total_size', 0)} chars")
        
        print("\n4. Framework Integration Options:")
        print("   [CLI] python Cross_Platform_Agent_Framework.py --deploy")
        print("   [CLI] python Cross_Platform_Agent_Framework.py --list")
        print("   [CLI] python Cross_Platform_Agent_Framework.py --call agent_name action '{\"param\": \"value\"}'")
        print("   [Python] import and use UniversalAgentOrchestrator class")
        print("   [HTTP] Connect via HTTP API on port 8765")
        
        print("\n5. Supported Platforms:")
        platforms = [
            "Claude Code", "VS Code Augment", "Windsurf", 
            "GitHub Copilot", "Cursor", "Tabnine", "Qodo",
            "Gemini CLI", "Amazon Q", "Cline", "Roo Code"
        ]
        for platform in platforms:
            print(f"   [OK] {platform}")
        
        print("\n[SUCCESS] Framework is fully operational and ready to use!")
        
    except Exception as e:
        print(f"\n[ERROR] {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n[CLEANUP] Shutting down...")
        orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())