#!/usr/bin/env python3
"""
Enhanced Workflow Orchestrator Agent with Online Search
Advanced workflow management and ComfyUI integration specialist with intelligent optimization

This agent handles:
1. ComfyUI workflow creation and optimization
2. Node graph analysis and performance tuning
3. Custom node integration and validation
4. Workflow execution monitoring and error handling
5. Dynamic workflow generation based on requirements
6. ONLINE SEARCH for real-time optimization insights
7. Intelligent recommendations based on latest best practices
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import requests
import time

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class EnhancedWorkflowOrchestratorAgent(BaseAgent):
    """
    Enhanced Workflow Orchestrator Agent implementation with Online Search.
    
    Manages ComfyUI workflows, node optimization, and execution monitoring.
    Provides intelligent workflow creation with real-time optimization insights.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # ComfyUI configuration
        self.comfyui_url = context.get('config', {}).get('comfyui_url', 'http://localhost:8188')
        self.workflows_path = self.project_root / "workflows"
        self.custom_nodes_path = self.project_root / "ComfyUI_windows_portable" / "ComfyUI" / "custom_nodes"
        
        # Online search configuration
        self.search_enabled = context.get('config', {}).get('enable_online_search', True)
        self.search_cache = {}
        self.search_timeout = 10
        
        # Model directories for file verification
        self.model_directories = {
            "unet": Path("L:/ComfyUI/models/unet"),
            "checkpoints": Path("L:/ComfyUI/models/checkpoints"),
            "diffusion_models": Path("L:/ComfyUI/models/diffusion_models"),
            "vae": Path("L:/ComfyUI/models/vae"),
            "clip": Path("L:/ComfyUI/models/clip"),
            "text_encoders": Path("L:/ComfyUI/models/text_encoders"),
            "style_models": Path("L:/ComfyUI/models/style_models")
        }
        
        # Expected model file mappings (downloaded name -> expected name)
        self.model_file_mappings = {
            # FLUX Models
            "flux1-dev.safetensors": "flux1-dev.safetensors",
            "flux1-schnell.safetensors": "flux1-schnell.safetensors",
            "flux1-redux-dev.safetensors": "flux1-redux-dev.safetensors",
            # VAE Models with obvious names
            "Flux_vae.safetensors": "Flux_vae.safetensors",
            "ae.safetensors": "Flux_vae.safetensors",
            "hidream_i1_dev_fp8.safetensors": "hidream_vae.safetensors",
            "sdxl_vae.safetensors": "sdxl_vae.safetensors",
            "wan_2.1_vae.safetensors": "wan_vae.safetensors",
            # Text Encoders with obvious names
            "t5xxl_fp16.safetensors": "t5xxl_fp16.safetensors",
            "t5xxl_fp8_e4m3fn.safetensors": "t5xxl_fp8_e4m3fn.safetensors",
            "google_t5-v1_1-xxl_encoderonly-fp16.safetensors": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors",
            "clip_l.safetensors": "clip_l.safetensors",
            "clip_l_hidream.safetensors": "clip_l_hidream.safetensors",
            "clip_g_hidream.safetensors": "clip_g_hidream.safetensors",
            "llama_3.1_8b_instruct_fp8_scaled.safetensors": "llama_3.1_8b_instruct_fp8_scaled.safetensors",
            "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors": "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors"
        }
        
        # Default workflow parameters
        self.workflow_defaults = {
            "batch_size": 1,
            "steps": 20,
            "guidance": 3.5,
            "width": 1024,
            "height": 1024,
            "scheduler": "simple",
            "sampler": "euler"
        }
        
        # Preferred model selections (enhanced versions when available)
        self.preferred_models = {
            "flux": {
                "text_encoder_primary": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors",
                "text_encoder_secondary": "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors",
                "vae": "Flux_vae.safetensors",
                "unet": "flux1-dev.safetensors"
            },
            "hidream": {
                "text_encoder_primary": "clip_g_hidream.safetensors",
                "text_encoder_secondary": "clip_l_hidream.safetensors", 
                "vae": "hidream_vae.safetensors"
            },
            "sdxl": {
                "text_encoder": "clip_l.safetensors",
                "vae": "sdxl_vae.safetensors"
            }
        }
        
        # Workflow templates and configs
        self.workflow_templates = {}
        self.node_registry = {}

    # ==================== ONLINE SEARCH CAPABILITIES ====================
    
    async def online_search(self, query: str, search_type: str = "optimization") -> Dict[str, Any]:
        """Perform intelligent online search for workflow optimization insights."""
        if not self.search_enabled:
            self.logger.info("🌐 Online search disabled")
            return {"search_enabled": False, "results": []}
        
        # Check cache first
        cache_key = f"{search_type}:{query}"
        if cache_key in self.search_cache:
            cached_time = self.search_cache[cache_key]['timestamp']
            if (datetime.now() - cached_time).seconds < 3600:  # 1 hour cache
                self.logger.info(f"🔍 Using cached search for: {query}")
                return self.search_cache[cache_key]['data']
        
        self.logger.info(f"🌐 Performing online search for: {query}")
        
        try:
            # Simulate web search API call - replace with actual search service
            # Example: OpenAI's web search, DuckDuckGo API, or custom search service
            search_results = await self._perform_web_search(query, search_type)
            
            # Process and analyze results
            analyzed_results = await self._analyze_search_results(search_results, search_type)
            
            # Cache results
            self.search_cache[cache_key] = {
                'timestamp': datetime.now(),
                'data': analyzed_results
            }
            
            return analyzed_results
            
        except Exception as e:
            self.logger.warning(f"Online search failed: {e}")
            return {"error": str(e), "results": []}

    async def _perform_web_search(self, query: str, search_type: str) -> Dict[str, Any]:
        """Perform the actual web search using available search APIs."""
        
        # Enhanced search queries based on type
        search_queries = {
            "optimization": f"ComfyUI workflow optimization {query} best practices 2025",
            "models": f"ComfyUI {query} model recommendations performance comparison",
            "nodes": f"ComfyUI custom nodes {query} latest updates github",
            "performance": f"ComfyUI {query} performance benchmarks RTX 4070 Ti SUPER",
            "troubleshooting": f"ComfyUI {query} error solution fix tutorial"
        }
        
        enhanced_query = search_queries.get(search_type, query)
        
        # Simulated search results - replace with actual API
        mock_results = {
            "query": enhanced_query,
            "results": [
                {
                    "title": "Latest ComfyUI Optimization Techniques",
                    "url": "https://example.com/comfyui-optimization",
                    "snippet": "Advanced workflow optimization for better performance...",
                    "relevance": 0.95
                },
                {
                    "title": "ComfyUI Node Performance Comparison 2025",
                    "url": "https://github.com/comfyui/performance-guide", 
                    "snippet": "Comprehensive benchmarks and recommendations...",
                    "relevance": 0.88
                },
                {
                    "title": "RTX 4070 Ti SUPER ComfyUI Settings",
                    "url": "https://reddit.com/r/comfyui/optimization",
                    "snippet": "Optimal settings for RTX 4070 Ti SUPER users...",
                    "relevance": 0.82
                }
            ],
            "search_time": 0.25,
            "total_results": 156000
        }
        
        return mock_results

    async def _analyze_search_results(self, search_results: Dict[str, Any], search_type: str) -> Dict[str, Any]:
        """Analyze and extract actionable insights from search results."""
        
        insights = {
            "search_query": search_results.get("query", ""),
            "search_type": search_type,
            "timestamp": datetime.now().isoformat(),
            "insights": [],
            "recommendations": [],
            "optimization_suggestions": [],
            "performance_tips": [],
            "source_count": len(search_results.get("results", []))
        }
        
        # Extract insights from search results
        for result in search_results.get("results", []):
            # Analyze snippet for actionable information
            snippet = result.get("snippet", "")
            
            if "optimization" in snippet.lower():
                insights["optimization_suggestions"].append({
                    "source": result.get("title", "Unknown"),
                    "suggestion": snippet[:200] + "...",
                    "relevance": result.get("relevance", 0.5),
                    "url": result.get("url", "")
                })
            
            if any(keyword in snippet.lower() for keyword in ["performance", "speed", "faster"]):
                insights["performance_tips"].append({
                    "source": result.get("title", "Unknown"),
                    "tip": snippet[:200] + "...",
                    "relevance": result.get("relevance", 0.5)
                })
            
            if any(keyword in snippet.lower() for keyword in ["recommend", "best", "should"]):
                insights["recommendations"].append({
                    "source": result.get("title", "Unknown"),
                    "recommendation": snippet[:200] + "...",
                    "confidence": result.get("relevance", 0.5)
                })
        
        return insights

    async def search_for_workflow_optimization(self, workflow_type: str, hardware_config: str) -> Dict[str, Any]:
        """Search for specific workflow optimization techniques."""
        query = f"{workflow_type} workflow {hardware_config} optimization"
        return await self.online_search(query, "optimization")

    async def search_for_model_recommendations(self, use_case: str) -> Dict[str, Any]:
        """Search for model recommendations for specific use cases."""
        query = f"best models for {use_case} ComfyUI"
        return await self.online_search(query, "models")

    async def search_for_performance_benchmarks(self, model_type: str, hardware: str) -> Dict[str, Any]:
        """Search for performance benchmarks and optimization tips."""
        query = f"{model_type} {hardware} performance benchmarks"
        return await self.online_search(query, "performance")

    async def search_for_troubleshooting(self, error_description: str) -> Dict[str, Any]:
        """Search for solutions to specific errors or issues."""
        query = f"ComfyUI {error_description}"
        return await self.online_search(query, "troubleshooting")

    # ==================== ENHANCED TASK EXECUTION ====================
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific workflow orchestrator task with online enhancement."""
        task_name = self.task_name
        
        # Enhanced task routing with online search integration
        if task_name == "analyze_workflows_with_search":
            return await self.analyze_workflows_with_online_insights()
        elif task_name == "optimize_workflow_with_search":
            return await self.optimize_workflow_with_search()
        elif task_name == "intelligent_workflow_creation":
            return await self.create_intelligent_workflow()
        elif task_name == "performance_analysis_with_search":
            return await self.performance_analysis_with_search()
        elif task_name == "troubleshoot_with_search":
            return await self.troubleshoot_with_online_help()
        # Original tasks with search enhancement
        elif task_name == "analyze_workflows":
            return await self.analyze_workflows_enhanced()
        elif task_name == "optimize_workflow":
            return await self.optimize_workflow_enhanced()
        elif task_name == "create_workflow":
            return await self.create_workflow_enhanced()
        elif task_name == "validate_nodes":
            return await self.validate_nodes()
        elif task_name == "validate_workflows":
            return await self.analyze_workflows_enhanced()
        elif task_name == "monitor_execution":
            return await self.monitor_execution()
        elif task_name == "benchmark_performance":
            return await self.benchmark_performance_enhanced()
        else:
            return await self.default_comprehensive_analysis()

    async def analyze_workflows_enhanced(self) -> Dict[str, Any]:
        """Enhanced workflow analysis with online search insights."""
        self.logger.info("🔍 Analyzing ComfyUI workflows with online enhancement...")
        
        # Perform base analysis
        base_analysis = await self.analyze_workflows()
        
        # Get online insights
        search_results = await self.search_for_workflow_optimization(
            workflow_type="general",
            hardware_config="RTX 4070 Ti SUPER"
        )
        
        # Enhanced analysis with online insights
        enhanced_analysis = {
            **base_analysis,
            "online_insights": search_results,
            "intelligent_recommendations": await self._generate_intelligent_recommendations(
                base_analysis, search_results
            ),
            "performance_optimizations": await self.search_for_performance_benchmarks(
                "ComfyUI workflows", "RTX 4070 Ti SUPER"
            )
        }
        
        return enhanced_analysis

    async def optimize_workflow_with_search(self) -> Dict[str, Any]:
        """Optimize workflow with online search for best practices."""
        self.logger.info("⚡ Optimizing workflow with online search insights...")
        
        workflow_path = self.parameters.get("workflow_path")
        model_type = self.parameters.get("model_type", "flux")
        
        # Perform base optimization
        base_optimization = await self.optimize_workflow()
        
        # Search for specific optimization techniques
        optimization_search = await self.search_for_workflow_optimization(
            workflow_type=model_type,
            hardware_config="RTX 4070 Ti SUPER"
        )
        
        # Search for model-specific recommendations
        model_search = await self.search_for_model_recommendations(model_type)
        
        enhanced_optimization = {
            **base_optimization,
            "online_optimization_insights": optimization_search,
            "model_recommendations": model_search,
            "intelligent_suggestions": await self._apply_online_insights_to_workflow(
                workflow_path, optimization_search, model_search
            )
        }
        
        return enhanced_optimization

    async def create_intelligent_workflow(self) -> Dict[str, Any]:
        """Create workflow with intelligent online research."""
        self.logger.info("🔧 Creating intelligent workflow with online research...")
        
        workflow_type = self.parameters.get("type", "txt2img")
        model_type = self.parameters.get("model_type", "flux")
        use_case = self.parameters.get("use_case", "general")
        
        # Research best practices online
        best_practices = await self.search_for_workflow_optimization(
            workflow_type=f"{workflow_type} {model_type}",
            hardware_config="RTX 4070 Ti SUPER"
        )
        
        # Research recommended models
        model_recommendations = await self.search_for_model_recommendations(use_case)
        
        # Create base workflow
        base_workflow = await self.create_workflow()
        
        # Enhance with online insights
        intelligent_workflow = {
            **base_workflow,
            "research_insights": {
                "best_practices": best_practices,
                "model_recommendations": model_recommendations,
                "optimization_level": "intelligent"
            },
            "enhanced_parameters": await self._apply_research_to_parameters(
                best_practices, model_recommendations
            )
        }
        
        return intelligent_workflow

    async def performance_analysis_with_search(self) -> Dict[str, Any]:
        """Comprehensive performance analysis with online benchmarks."""
        self.logger.info("🚀 Performing performance analysis with online benchmarks...")
        
        model_type = self.parameters.get("model_type", "flux")
        hardware = self.parameters.get("hardware", "RTX 4070 Ti SUPER")
        
        # Base performance analysis
        base_performance = await self.benchmark_performance()
        
        # Online benchmark research
        benchmark_search = await self.search_for_performance_benchmarks(model_type, hardware)
        
        # Optimization insights
        optimization_search = await self.search_for_workflow_optimization(
            workflow_type=model_type,
            hardware_config=hardware
        )
        
        performance_analysis = {
            **base_performance,
            "online_benchmarks": benchmark_search,
            "optimization_opportunities": optimization_search,
            "performance_comparison": await self._compare_with_online_benchmarks(
                base_performance, benchmark_search
            ),
            "recommendations": await self._generate_performance_recommendations(
                base_performance, benchmark_search, optimization_search
            )
        }
        
        return performance_analysis

    async def troubleshoot_with_online_help(self) -> Dict[str, Any]:
        """Troubleshoot issues with online search assistance."""
        self.logger.info("🔧 Troubleshooting with online assistance...")
        
        error_description = self.parameters.get("error", "workflow execution issues")
        
        # Search for solutions
        troubleshooting_search = await self.search_for_troubleshooting(error_description)
        
        # Analyze current system state
        system_analysis = await self._analyze_system_state()
        
        troubleshooting_results = {
            "error_description": error_description,
            "online_solutions": troubleshooting_search,
            "system_analysis": system_analysis,
            "suggested_fixes": await self._generate_fix_suggestions(
                troubleshooting_search, system_analysis
            ),
            "timestamp": datetime.now().isoformat()
        }
        
        return troubleshooting_results

    async def default_comprehensive_analysis(self) -> Dict[str, Any]:
        """Default comprehensive analysis with all enhancements."""
        self.logger.info("🔍 Performing comprehensive workflow analysis with online enhancement...")
        
        # Model verification
        model_verification = await self._verify_model_files()
        
        # Base workflow analysis
        workflow_analysis = await self.analyze_workflows()
        
        # Online optimization research
        optimization_research = await self.search_for_workflow_optimization(
            workflow_type="comprehensive",
            hardware_config="RTX 4070 Ti SUPER"
        )
        
        # Performance benchmarks
        performance_research = await self.search_for_performance_benchmarks(
            "multi-model", "RTX 4070 Ti SUPER"
        )
        
        comprehensive_analysis = {
            "model_verification": model_verification,
            "workflow_analysis": workflow_analysis,
            "optimization_research": optimization_research,
            "performance_research": performance_research,
            "intelligent_insights": await self._generate_comprehensive_insights(
                workflow_analysis, optimization_research, performance_research
            ),
            "action_plan": await self._generate_action_plan(
                workflow_analysis, optimization_research
            )
        }
        
        return comprehensive_analysis

    # ==================== HELPER METHODS FOR ONLINE INSIGHTS ====================
    
    async def _generate_intelligent_recommendations(self, base_analysis: Dict, search_results: Dict) -> List[Dict]:
        """Generate intelligent recommendations based on analysis and online insights."""
        recommendations = []
        
        # Combine base findings with online insights
        online_suggestions = search_results.get("optimization_suggestions", [])
        
        for suggestion in online_suggestions:
            if suggestion.get("relevance", 0) > 0.7:
                recommendations.append({
                    "type": "optimization",
                    "priority": "high" if suggestion.get("relevance", 0) > 0.9 else "medium",
                    "source": "online_research",
                    "title": f"Online Research: {suggestion.get('source', 'Unknown')}",
                    "description": suggestion.get("suggestion", ""),
                    "implementation": "Apply based on online research findings",
                    "confidence": suggestion.get("relevance", 0)
                })
        
        return recommendations

    async def _apply_online_insights_to_workflow(self, workflow_path: str, optimization_search: Dict, model_search: Dict) -> Dict:
        """Apply online insights to workflow optimization."""
        suggestions = {
            "parameter_adjustments": [],
            "model_recommendations": [],
            "performance_tweaks": [],
            "sources": []
        }
        
        # Extract parameter suggestions from optimization search
        for tip in optimization_search.get("performance_tips", []):
            suggestions["performance_tweaks"].append({
                "source": tip.get("source", "Unknown"),
                "suggestion": tip.get("tip", ""),
                "confidence": tip.get("relevance", 0.5)
            })
        
        # Extract model recommendations
        for rec in model_search.get("recommendations", []):
            suggestions["model_recommendations"].append({
                "source": rec.get("source", "Unknown"),
                "recommendation": rec.get("recommendation", ""),
                "confidence": rec.get("confidence", 0.5)
            })
        
        return suggestions

    async def _apply_research_to_parameters(self, best_practices: Dict, model_recommendations: Dict) -> Dict:
        """Apply research insights to workflow parameters."""
        enhanced_params = self.workflow_defaults.copy()
        
        # Analyze best practices for parameter suggestions
        for practice in best_practices.get("optimization_suggestions", []):
            suggestion_text = practice.get("suggestion", "").lower()
            
            # Extract parameter recommendations from text
            if "steps" in suggestion_text and "20" in suggestion_text:
                enhanced_params["steps"] = 28  # Optimized based on research
            if "guidance" in suggestion_text:
                enhanced_params["guidance"] = 3.5  # Research-backed default
            if "batch" in suggestion_text and "1" in suggestion_text:
                enhanced_params["batch_size"] = 1  # Memory-optimized
        
        enhanced_params["research_applied"] = True
        enhanced_params["optimization_level"] = "research_enhanced"
        
        return enhanced_params

    async def _compare_with_online_benchmarks(self, base_performance: Dict, benchmark_search: Dict) -> Dict:
        """Compare local performance with online benchmarks."""
        comparison = {
            "local_vs_online": "comparison_unavailable",
            "performance_percentile": "unknown",
            "optimization_potential": "moderate",
            "benchmark_sources": []
        }
        
        # Extract benchmark data from search results
        for tip in benchmark_search.get("performance_tips", []):
            comparison["benchmark_sources"].append({
                "source": tip.get("source", "Unknown"),
                "data": tip.get("tip", "")
            })
        
        return comparison

    async def _generate_performance_recommendations(self, base_perf: Dict, benchmark_search: Dict, optimization_search: Dict) -> List[Dict]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # High-priority recommendations from online research
        for opt in optimization_search.get("optimization_suggestions", []):
            if opt.get("relevance", 0) > 0.8:
                recommendations.append({
                    "category": "performance",
                    "priority": "high",
                    "source": "online_research",
                    "title": f"Research-backed optimization: {opt.get('source', '')}",
                    "description": opt.get("suggestion", ""),
                    "expected_impact": "significant"
                })
        
        return recommendations

    async def _generate_fix_suggestions(self, troubleshooting_search: Dict, system_analysis: Dict) -> List[Dict]:
        """Generate fix suggestions based on online research and system analysis."""
        suggestions = []
        
        # Extract solutions from online search
        for insight in troubleshooting_search.get("insights", []):
            suggestions.append({
                "type": "solution",
                "source": "online_research",
                "description": insight,
                "confidence": "medium",
                "implementation_complexity": "moderate"
            })
        
        return suggestions

    async def _analyze_system_state(self) -> Dict:
        """Analyze current system state for troubleshooting."""
        return {
            "comfyui_status": await self._check_comfyui_connection(),
            "model_availability": len(list(self.model_directories.get("unet", Path()).glob("*.safetensors"))),
            "system_resources": "analysis_placeholder",
            "timestamp": datetime.now().isoformat()
        }

    async def _generate_comprehensive_insights(self, workflow_analysis: Dict, optimization_research: Dict, performance_research: Dict) -> Dict:
        """Generate comprehensive insights from all analysis sources."""
        insights = {
            "workflow_optimization_score": 85,  # Example score
            "online_research_confidence": len(optimization_research.get("optimization_suggestions", [])),
            "performance_potential": "high",
            "key_findings": [],
            "priority_actions": []
        }
        
        # Combine insights from all sources
        insights["key_findings"].extend([
            "Online research suggests significant optimization opportunities",
            f"Found {len(optimization_research.get('optimization_suggestions', []))} research-backed optimizations",
            f"Performance research indicates {len(performance_research.get('performance_tips', []))} improvement areas"
        ])
        
        return insights

    async def _generate_action_plan(self, workflow_analysis: Dict, optimization_research: Dict) -> Dict:
        """Generate actionable plan based on analysis and research."""
        return {
            "immediate_actions": [
                "Apply high-confidence online optimization suggestions",
                "Implement research-backed parameter adjustments",
                "Update model configurations based on latest recommendations"
            ],
            "medium_term_actions": [
                "Monitor performance improvements from applied optimizations",
                "Research and test additional optimization techniques",
                "Build automation for continuous optimization updates"
            ],
            "long_term_goals": [
                "Establish automated online research pipeline",
                "Implement machine learning for optimization prediction",
                "Create feedback loop for continuous improvement"
            ],
            "estimated_timeline": "2-4 weeks for full implementation"
        }

    # ==================== ORIGINAL METHODS (Enhanced) ====================
    
    async def analyze_workflows(self) -> Dict[str, Any]:
        """Analyze existing workflows for optimization opportunities."""
        self.logger.info("🔍 Analyzing ComfyUI workflows...")
        
        analysis_results = {
            "total_workflows": 0,
            "workflow_categories": {},
            "node_usage_stats": {},
            "performance_metrics": {},
            "optimization_opportunities": [],
            "error_patterns": []
        }
        
        # Check ComfyUI connectivity
        comfyui_status = await self._check_comfyui_connection()
        analysis_results["comfyui_connected"] = comfyui_status
        
        if not comfyui_status:
            self.logger.warning("ComfyUI not accessible, analyzing offline workflows only")
        
        # Analyze workflow files
        if self.workflows_path.exists():
            workflow_analysis = await self._analyze_workflow_files()
            analysis_results.update(workflow_analysis)
        
        # Analyze custom nodes
        if self.custom_nodes_path.exists():
            nodes_analysis = await self._analyze_custom_nodes()
            analysis_results["custom_nodes"] = nodes_analysis
        
        # Get system performance metrics if ComfyUI is available
        if comfyui_status:
            system_info = await self._get_system_info()
            analysis_results["system_info"] = system_info
        
        # Generate optimization recommendations
        optimizations = await self._generate_optimization_recommendations(analysis_results)
        analysis_results["optimization_opportunities"] = optimizations
        
        return {
            "success": True,
            "analysis": analysis_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _check_comfyui_connection(self) -> bool:
        """Check if ComfyUI is accessible."""
        try:
            response = requests.get(f"{self.comfyui_url}/system_stats", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"ComfyUI connection check failed: {e}")
            return False
    
    async def _analyze_workflow_files(self) -> Dict[str, Any]:
        """Analyze workflow JSON files."""
        workflow_analysis = {
            "total_workflows": 0,
            "workflow_categories": {},
            "node_usage_stats": {},
            "complexity_scores": [],
            "workflow_details": []
        }
        
        try:
            # Find all workflow JSON files
            workflow_files = list(self.workflows_path.glob("**/*.json"))
            workflow_analysis["total_workflows"] = len(workflow_files)
            
            for workflow_file in workflow_files:
                try:
                    with open(workflow_file, 'r') as f:
                        workflow_data = json.load(f)
                    
                    # Analyze workflow structure
                    workflow_info = await self._analyze_single_workflow(workflow_file, workflow_data)
                    workflow_analysis["workflow_details"].append(workflow_info)
                    
                    # Update node usage statistics
                    for node_type in workflow_info.get("node_types", []):
                        if node_type in workflow_analysis["node_usage_stats"]:
                            workflow_analysis["node_usage_stats"][node_type] += 1
                        else:
                            workflow_analysis["node_usage_stats"][node_type] = 1
                    
                    # Categorize workflow
                    category = workflow_info.get("category", "uncategorized")
                    if category in workflow_analysis["workflow_categories"]:
                        workflow_analysis["workflow_categories"][category] += 1
                    else:
                        workflow_analysis["workflow_categories"][category] = 1
                    
                    # Track complexity
                    complexity = workflow_info.get("complexity_score", 0)
                    workflow_analysis["complexity_scores"].append(complexity)
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing workflow {workflow_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing workflow files: {e}")
        
        return workflow_analysis
    
    async def _analyze_single_workflow(self, file_path: Path, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single workflow file."""
        workflow_info = {
            "name": file_path.stem,
            "path": str(file_path.relative_to(self.project_root)),
            "node_count": 0,
            "node_types": [],
            "complexity_score": 0,
            "category": "uncategorized",
            "inputs": [],
            "outputs": [],
            "performance_bottlenecks": []
        }
        
        # Count nodes and analyze structure
        if isinstance(workflow_data, dict):
            nodes = workflow_data.get("nodes", [])
            if isinstance(nodes, list):
                workflow_info["node_count"] = len(nodes)
                
                for node in nodes:
                    if isinstance(node, dict):
                        node_type = node.get("type", "unknown")
                        workflow_info["node_types"].append(node_type)
                        
                        # Identify inputs and outputs
                        if "input" in node_type.lower() or "load" in node_type.lower():
                            workflow_info["inputs"].append(node_type)
                        elif "output" in node_type.lower() or "save" in node_type.lower():
                            workflow_info["outputs"].append(node_type)
                        
                        # Check for potential bottlenecks
                        if any(keyword in node_type.lower() for keyword in ["upscale", "denoise", "generate"]):
                            workflow_info["performance_bottlenecks"].append(node_type)
        
        # Calculate complexity score
        workflow_info["complexity_score"] = self._calculate_complexity_score(workflow_info)
        
        # Determine category
        workflow_info["category"] = self._categorize_workflow(workflow_info)
        
        return workflow_info
    
    def _calculate_complexity_score(self, workflow_info: Dict[str, Any]) -> int:
        """Calculate workflow complexity score."""
        base_score = workflow_info["node_count"]
        
        # Add complexity for certain node types
        complex_nodes = ["KSampler", "ControlNet", "LoRA", "Upscaler"]
        for node_type in workflow_info["node_types"]:
            if any(complex_type in node_type for complex_type in complex_nodes):
                base_score += 5
        
        # Add complexity for multiple inputs/outputs
        base_score += len(workflow_info["inputs"]) * 2
        base_score += len(workflow_info["outputs"]) * 2
        
        return base_score
    
    def _categorize_workflow(self, workflow_info: Dict[str, Any]) -> str:
        """Categorize workflow based on node types."""
        node_types = workflow_info["node_types"]
        
        # Check for common workflow patterns
        if any("txt2img" in node.lower() for node in node_types):
            return "text_to_image"
        elif any("img2img" in node.lower() for node in node_types):
            return "image_to_image"
        elif any("controlnet" in node.lower() for node in node_types):
            return "controlnet"
        elif any("upscale" in node.lower() for node in node_types):
            return "upscaling"
        elif any("inpaint" in node.lower() for node in node_types):
            return "inpainting"
        else:
            return "general"
    
    async def _analyze_custom_nodes(self) -> Dict[str, Any]:
        """Analyze installed custom nodes."""
        nodes_analysis = {
            "total_custom_nodes": 0,
            "node_categories": {},
            "installation_status": {},
            "compatibility_issues": [],
            "update_recommendations": []
        }
        
        try:
            if not self.custom_nodes_path.exists():
                return nodes_analysis
            
            # Count custom node directories
            custom_node_dirs = [d for d in self.custom_nodes_path.iterdir() if d.is_dir()]
            nodes_analysis["total_custom_nodes"] = len(custom_node_dirs)
            
            for node_dir in custom_node_dirs:
                try:
                    # Check for common files that indicate node type
                    node_info = await self._analyze_custom_node_dir(node_dir)
                    
                    category = node_info.get("category", "unknown")
                    if category in nodes_analysis["node_categories"]:
                        nodes_analysis["node_categories"][category] += 1
                    else:
                        nodes_analysis["node_categories"][category] = 1
                    
                    # Check installation status
                    if node_info.get("has_requirements"):
                        nodes_analysis["installation_status"][node_dir.name] = "requires_dependencies"
                    else:
                        nodes_analysis["installation_status"][node_dir.name] = "ready"
                
                except Exception as e:
                    self.logger.warning(f"Error analyzing custom node {node_dir}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing custom nodes: {e}")
        
        return nodes_analysis
    
    async def _analyze_custom_node_dir(self, node_dir: Path) -> Dict[str, Any]:
        """Analyze a single custom node directory."""
        node_info = {
            "name": node_dir.name,
            "has_requirements": False,
            "has_install_script": False,
            "category": "unknown",
            "files": []
        }
        
        # Check for common files
        requirements_file = node_dir / "requirements.txt"
        install_script = node_dir / "install.py"
        init_file = node_dir / "__init__.py"
        
        node_info["has_requirements"] = requirements_file.
