#!/usr/bin/env python3
"""
Simple test script for the Universal Cross-Platform Agent Framework
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the framework to path
sys.path.insert(0, str(Path(__file__).parent))

from Cross_Platform_Agent_Framework import (
    UniversalAgentOrchestrator,
    EnhancedDocumentationOverseer
)

async def simple_test():
    """Simple test of the universal agent framework."""
    print("Testing Universal Cross-Platform Agent Framework")
    print("=" * 50)
    
    orchestrator = UniversalAgentOrchestrator()
    
    try:
        # Deploy Enhanced Documentation Overseer
        print("Deploying Enhanced Documentation Overseer...")
        context = {"project_root": str(Path.cwd().parent)}
        success = await orchestrator.deploy_agent(
            EnhancedDocumentationOverseer,
            "enhanced-documentation-overseer",
            [
                "build_knowledge_base",
                "generate_context_files", 
                "llm_context_delivery",
                "intelligent_file_organization",
                "audit_documentation",
                "semantic_organization",
                "knowledge_synthesis",
                "maintain_knowledge_graph",
                "content_summarization",
                "topic_clustering",
                "context_extraction"
            ],
            context
        )
        
        if success:
            print("SUCCESS: Enhanced Documentation Overseer deployed")
        else:
            print("ERROR: Failed to deploy Enhanced Documentation Overseer")
            return
        
        # Wait a moment for registration
        await asyncio.sleep(2)
        
        # List available agents
        print("\nListing available agents...")
        agents = await orchestrator.list_agents()
        print(f"Found {len(agents)} agents:")
        for agent in agents:
            print(f"  - {agent['name']} ({agent['status']})")
        
        # Test agent call - simple documentation audit
        print("\nTesting documentation audit...")
        result = await orchestrator.call_agent(
            "enhanced-documentation-overseer",
            "execute",
            {
                "task": "audit_documentation",
                "parameters": {}
            }
        )
        
        if result.get("status") == "success":
            audit_data = result.get("data", {})
            audit_results = audit_data.get("audit_results", {})
            print(f"SUCCESS: Documentation audit completed")
            print(f"  Total files: {audit_results.get('total_files', 0)}")
            print(f"  Documented files: {audit_results.get('documented_files', 0)}")
            print(f"  Quality score: {audit_data.get('quality_score', 0):.2f}")
        else:
            print(f"ERROR: Documentation audit failed - {result.get('error', 'Unknown error')}")
        
        # Test platform detection
        print("\nTesting platform detection...")
        adapter = orchestrator.active_agents.get("enhanced-documentation-overseer")
        if adapter:
            from Cross_Platform_Agent_Framework import CrossPlatformAgentAdapter
            test_adapter = CrossPlatformAgentAdapter("test", ["test"])
            platform = await test_adapter._detect_platform()
            print(f"Detected platform: {platform}")
        
        print("\nFramework testing completed successfully!")
        print("\nSupported Platforms:")
        platforms = [
            "Claude Code", "VS Code Augment", "Windsurf", "GitHub Copilot",
            "Cursor", "Tabnine", "Qodo", "Gemini CLI", "Amazon Q", 
            "Cline (Continue)", "Roo Code"
        ]
        for platform in platforms:
            print(f"  + {platform}")
        
    except Exception as e:
        print(f"ERROR: Framework test failed - {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\nShutting down...")
        orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(simple_test())