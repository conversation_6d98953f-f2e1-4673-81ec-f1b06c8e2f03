"""
Multimodal API Routes

This module provides REST API endpoints for multimodal AI features that combine
LLaVA vision analysis with Nomic embeddings and LLM enhancement.

Key Endpoints:
- POST /multimodal/analyze-image: Comprehensive image analysis
- POST /multimodal/image-to-prompt: Generate enhanced prompts from reference images  
- POST /multimodal/iterative-refinement: Refine prompts based on results and feedback
- POST /multimodal/visual-similarity-search: Find visually similar images and prompts
- GET /multimodal/health: Health check for all multimodal services

Features:
- File upload handling for reference images
- Structured response formatting with comprehensive error handling
- Request validation and sanitization
- Performance monitoring and logging
- Caching support for frequently accessed analyses

Dependencies:
- multimodal_enhancement_service: Core multimodal processing
- FastAPI: Web framework with automatic documentation
- Pydantic: Request/response validation and serialization

Usage:
    # Include in FastAPI app
    app.include_router(multimodal_router, prefix="/api/v1")
    
    # Example request:
    POST /api/v1/multimodal/image-to-prompt
    {
        "user_prompt": "a beautiful landscape",
        "enhancement_mode": "visual_matching"
    }
    files: {"reference_image": image_file}
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Any, Union
import json
import logging
import tempfile
import shutil
from pathlib import Path
import uuid
from datetime import datetime
import asyncio

from ..services.multimodal_enhancement_service import multimodal_service

logger = logging.getLogger(__name__)

# Create router
multimodal_router = APIRouter(tags=["multimodal"])

# Pydantic models for request/response validation

class ImageAnalysisRequest(BaseModel):
    """Request model for image analysis"""
    analysis_type: str = Field(
        default="comprehensive",
        description="Type of analysis to perform",
        pattern="^(comprehensive|style_focused|mood_focused|composition_focused|prompt_generation)$"
    )
    use_cache: bool = Field(
        default=True,
        description="Whether to use cached results if available"
    )

class ImageAnalysisResponse(BaseModel):
    """Response model for image analysis"""
    success: bool
    analysis: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    timestamp: str

class ImageToPromptRequest(BaseModel):
    """Request model for image-to-prompt enhancement"""
    user_prompt: str = Field(
        min_length=1,
        max_length=1000,
        description="User's original prompt"
    )
    enhancement_mode: str = Field(
        default="visual_matching",
        description="Type of enhancement to apply",
        pattern="^(visual_matching|style_transfer|mood_matching|composition_guide)$"
    )
    additional_context: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional additional context for enhancement"
    )
    
    @validator('user_prompt')
    def validate_user_prompt(cls, v):
        if not v or v.strip() == "":
            raise ValueError("User prompt cannot be empty")
        return v.strip()

class ImageToPromptResponse(BaseModel):
    """Response model for image-to-prompt enhancement"""
    success: bool
    original_prompt: str
    enhanced_prompt: str
    enhancement_explanation: Optional[str] = None
    enhancement_mode: str
    visual_analysis: Optional[Dict[str, Any]] = None
    similar_prompts: Optional[List[Dict[str, Any]]] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None
    improvement_suggestions: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str

class IterativeRefinementRequest(BaseModel):
    """Request model for iterative refinement"""
    original_prompt: str = Field(
        min_length=1,
        max_length=1000,
        description="Original prompt that was used"
    )
    user_feedback: str = Field(
        min_length=1,
        max_length=500,
        description="User's feedback on the current result"
    )
    refinement_context: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional additional context for refinement"
    )

class IterativeRefinementResponse(BaseModel):
    """Response model for iterative refinement"""
    success: bool
    current_analysis: Optional[Dict[str, Any]] = None
    target_analysis: Optional[Dict[str, Any]] = None
    feedback_interpretation: Optional[Dict[str, Any]] = None
    refinement_suggestions: Optional[List[Dict[str, Any]]] = None
    recommended_prompt_modifications: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str

class VisualSimilaritySearchRequest(BaseModel):
    """Request model for visual similarity search"""
    search_scope: str = Field(
        default="both",
        description="Where to search for similar items",
        pattern="^(history|database|both)$"
    )
    similarity_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum similarity score (0.0 to 1.0)"
    )
    limit: int = Field(
        default=10,
        ge=1,
        le=50,
        description="Maximum number of results to return"
    )
    search_filters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional filters for search results"
    )

class VisualSimilaritySearchResponse(BaseModel):
    """Response model for visual similarity search"""
    success: bool
    query_image: str
    query_analysis: Optional[Dict[str, Any]] = None
    similar_items: Optional[List[Dict[str, Any]]] = None
    search_scope: str
    total_found: int
    similarity_threshold: float
    search_terms: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str

class HealthCheckResponse(BaseModel):
    """Response model for health check"""
    multimodal_service: str
    llava_service: str
    embedding_service: str
    prompt_enhancement_service: str
    timestamp: str

# Utility functions

def create_temp_file(upload_file: UploadFile) -> str:
    """
    Create a temporary file from uploaded file
    
    Args:
        upload_file: Uploaded file from FastAPI
        
    Returns:
        str: Path to temporary file
        
    Raises:
        HTTPException: If file processing fails
    """
    try:
        # Validate file type
        if not upload_file.content_type or not upload_file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file type: {upload_file.content_type}. Only images are supported."
            )
        
        # Check file size (limit to 10MB)
        if upload_file.size and upload_file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail=f"File too large: {upload_file.size / (1024*1024):.1f}MB. Maximum size is 10MB."
            )
        
        # Create temporary file with appropriate extension
        file_extension = Path(upload_file.filename).suffix if upload_file.filename else '.jpg'
        temp_file = tempfile.NamedTemporaryFile(
            delete=False,
            suffix=file_extension,
            prefix="multimodal_"
        )
        
        # Copy uploaded file to temporary file
        shutil.copyfileobj(upload_file.file, temp_file)
        temp_file.close()
        
        logger.info(f"Created temporary file: {temp_file.name} for {upload_file.filename}")
        return temp_file.name
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create temporary file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"File processing failed: {str(e)}"
        )

def cleanup_temp_file(file_path: str):
    """
    Clean up temporary file
    
    Args:
        file_path: Path to temporary file to delete
    """
    try:
        if file_path and Path(file_path).exists():
            Path(file_path).unlink()
            logger.debug(f"Cleaned up temporary file: {file_path}")
    except Exception as e:
        logger.warning(f"Failed to cleanup temporary file {file_path}: {str(e)}")

def generate_request_id() -> str:
    """Generate unique request ID for tracking"""
    return str(uuid.uuid4())[:8]

# API Endpoints

@multimodal_router.post(
    "/analyze-image",
    response_model=ImageAnalysisResponse,
    summary="Analyze image using LLaVA vision model",
    description="""
    Perform comprehensive analysis of an uploaded image using the LLaVA vision-language model.
    
    Supports different analysis types:
    - comprehensive: Full analysis including style, mood, composition, lighting, etc.
    - style_focused: Focus on artistic style and techniques
    - mood_focused: Focus on emotional tone and atmosphere  
    - composition_focused: Focus on visual structure and arrangement
    - prompt_generation: Optimized for generating text prompts
    
    Returns structured analysis results with confidence scores and processing metrics.
    """
)
async def analyze_image(
    background_tasks: BackgroundTasks,
    image: UploadFile = File(..., description="Image file to analyze"),
    request_data: str = Form(..., description="JSON string with analysis parameters")
):
    """Analyze uploaded image using LLaVA vision model"""
    
    request_id = generate_request_id()
    temp_file_path = None
    
    logger.info(f"[{request_id}] Starting image analysis request")
    
    try:
        # Parse request data
        try:
            request_params = json.loads(request_data)
            analysis_request = ImageAnalysisRequest(**request_params)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON in request_data: {str(e)}")
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid request parameters: {str(e)}")
        
        # Create temporary file from upload
        temp_file_path = create_temp_file(image)
        
        # Perform analysis
        logger.info(f"[{request_id}] Analyzing image: {image.filename} ({analysis_request.analysis_type})")
        
        analysis_result = await multimodal_service.analyze_reference_image(
            temp_file_path,
            analysis_request.analysis_type,
            analysis_request.use_cache
        )
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.info(f"[{request_id}] Image analysis completed successfully")
        
        return ImageAnalysisResponse(
            success=True,
            analysis=analysis_result,
            processing_time=analysis_result.get('processing_time'),
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        raise
    except Exception as e:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.error(f"[{request_id}] Image analysis failed: {str(e)}")
        return ImageAnalysisResponse(
            success=False,
            error=str(e),
            timestamp=datetime.now().isoformat()
        )

@multimodal_router.post(
    "/image-to-prompt",
    response_model=ImageToPromptResponse,
    summary="Generate enhanced prompt from reference image",
    description="""
    Generate an enhanced text prompt based on a reference image and user's original prompt.
    
    Uses LLaVA for visual analysis and combines it with semantic similarity search to find
    relevant historical prompts, then enhances the user's prompt using LLM processing.
    
    Enhancement modes:
    - visual_matching: Match overall visual characteristics
    - style_transfer: Focus on transferring artistic style
    - mood_matching: Match emotional tone and atmosphere
    - composition_guide: Learn compositional techniques
    
    Returns the enhanced prompt with detailed analysis and similarity matches.
    """
)
async def image_to_prompt_enhancement(
    background_tasks: BackgroundTasks,
    reference_image: UploadFile = File(..., description="Reference image for prompt enhancement"),
    request_data: str = Form(..., description="JSON string with enhancement parameters")
):
    """Generate enhanced prompt based on reference image"""
    
    request_id = generate_request_id()
    temp_file_path = None
    
    logger.info(f"[{request_id}] Starting image-to-prompt enhancement")
    
    try:
        # Parse request data
        try:
            request_params = json.loads(request_data)
            enhancement_request = ImageToPromptRequest(**request_params)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON in request_data: {str(e)}")
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid request parameters: {str(e)}")
        
        # Create temporary file from upload
        temp_file_path = create_temp_file(reference_image)
        
        # Perform enhancement
        logger.info(f"[{request_id}] Enhancing prompt: '{enhancement_request.user_prompt}' with mode: {enhancement_request.enhancement_mode}")
        
        enhancement_result = await multimodal_service.image_to_prompt_enhancement(
            enhancement_request.user_prompt,
            temp_file_path,
            enhancement_request.enhancement_mode,
            enhancement_request.additional_context
        )
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.info(f"[{request_id}] Image-to-prompt enhancement completed successfully")
        
        # Format response
        response = ImageToPromptResponse(
            success=enhancement_result.get('success', True),
            original_prompt=enhancement_result['original_prompt'],
            enhanced_prompt=enhancement_result['enhanced_prompt'],
            enhancement_explanation=enhancement_result.get('enhancement_explanation'),
            enhancement_mode=enhancement_result['enhancement_mode'],
            visual_analysis=enhancement_result.get('visual_analysis'),
            similar_prompts=enhancement_result.get('similar_prompts'),
            confidence_score=enhancement_result.get('confidence_score'),
            processing_time=enhancement_result.get('processing_time'),
            improvement_suggestions=enhancement_result.get('improvement_suggestions'),
            metadata=enhancement_result.get('metadata'),
            error=enhancement_result.get('error'),
            timestamp=datetime.now().isoformat()
        )
        
        return response
        
    except HTTPException:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        raise
    except Exception as e:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.error(f"[{request_id}] Image-to-prompt enhancement failed: {str(e)}")
        return ImageToPromptResponse(
            success=False,
            original_prompt=request_params.get('user_prompt', ''),
            enhanced_prompt=request_params.get('user_prompt', ''),
            enhancement_mode=request_params.get('enhancement_mode', 'visual_matching'),
            error=str(e),
            timestamp=datetime.now().isoformat()
        )

@multimodal_router.post(
    "/iterative-refinement",
    response_model=IterativeRefinementResponse,
    summary="Refine prompt based on results and feedback",
    description="""
    Refine a prompt based on the current generation result and user feedback.
    
    Analyzes the current generated image, interprets user feedback semantically,
    and provides specific suggestions for improving the prompt to better match
    user intentions.
    
    Optionally can compare against a target reference image for more precise
    refinement suggestions.
    
    Returns detailed analysis and specific modification recommendations.
    """
)
async def iterative_refinement(
    background_tasks: BackgroundTasks,
    current_image: UploadFile = File(..., description="Currently generated image"),
    target_image: Optional[UploadFile] = File(None, description="Optional target reference image"),
    request_data: str = Form(..., description="JSON string with refinement parameters")
):
    """Refine prompt based on current results and user feedback"""
    
    request_id = generate_request_id()
    current_temp_path = None
    target_temp_path = None
    
    logger.info(f"[{request_id}] Starting iterative refinement")
    
    try:
        # Parse request data
        try:
            request_params = json.loads(request_data)
            refinement_request = IterativeRefinementRequest(**request_params)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON in request_data: {str(e)}")
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid request parameters: {str(e)}")
        
        # Create temporary files
        current_temp_path = create_temp_file(current_image)
        
        if target_image:
            target_temp_path = create_temp_file(target_image)
        
        # Perform refinement
        logger.info(f"[{request_id}] Processing refinement for feedback: '{refinement_request.user_feedback}'")
        
        refinement_result = await multimodal_service.iterative_refinement(
            current_temp_path,
            refinement_request.original_prompt,
            refinement_request.user_feedback,
            target_temp_path,
            refinement_request.refinement_context
        )
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_temp_file, current_temp_path)
        if target_temp_path:
            background_tasks.add_task(cleanup_temp_file, target_temp_path)
        
        logger.info(f"[{request_id}] Iterative refinement completed successfully")
        
        # Format response
        response = IterativeRefinementResponse(
            success=refinement_result.get('success', True),
            current_analysis=refinement_result.get('current_analysis'),
            target_analysis=refinement_result.get('target_analysis'),
            feedback_interpretation=refinement_result.get('feedback_interpretation'),
            refinement_suggestions=refinement_result.get('refinement_suggestions'),
            recommended_prompt_modifications=refinement_result.get('recommended_prompt_modifications'),
            confidence_score=refinement_result.get('confidence_score'),
            processing_time=refinement_result.get('processing_time'),
            metadata=refinement_result.get('metadata'),
            error=refinement_result.get('error'),
            timestamp=datetime.now().isoformat()
        )
        
        return response
        
    except HTTPException:
        if current_temp_path:
            background_tasks.add_task(cleanup_temp_file, current_temp_path)
        if target_temp_path:
            background_tasks.add_task(cleanup_temp_file, target_temp_path)
        raise
    except Exception as e:
        if current_temp_path:
            background_tasks.add_task(cleanup_temp_file, current_temp_path)
        if target_temp_path:
            background_tasks.add_task(cleanup_temp_file, target_temp_path)
        
        logger.error(f"[{request_id}] Iterative refinement failed: {str(e)}")
        return IterativeRefinementResponse(
            success=False,
            error=str(e),
            timestamp=datetime.now().isoformat()
        )

@multimodal_router.post(
    "/visual-similarity-search",
    response_model=VisualSimilaritySearchResponse,
    summary="Find visually similar images and prompts",
    description="""
    Search for visually similar images and their associated prompts based on a query image.
    
    Uses LLaVA to analyze the query image, converts the analysis to semantic embeddings,
    and searches through the prompt/image database for similar items.
    
    Search scopes:
    - history: Search through user's generation history
    - database: Search through the full prompt database
    - both: Search through both history and database
    
    Returns ranked similar items with similarity scores and usage suggestions.
    """
)
async def visual_similarity_search(
    background_tasks: BackgroundTasks,
    query_image: UploadFile = File(..., description="Query image for similarity search"),
    request_data: str = Form(..., description="JSON string with search parameters")
):
    """Find visually similar images and prompts"""
    
    request_id = generate_request_id()
    temp_file_path = None
    
    logger.info(f"[{request_id}] Starting visual similarity search")
    
    try:
        # Parse request data
        try:
            request_params = json.loads(request_data)
            search_request = VisualSimilaritySearchRequest(**request_params)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON in request_data: {str(e)}")
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid request parameters: {str(e)}")
        
        # Create temporary file from upload
        temp_file_path = create_temp_file(query_image)
        
        # Perform similarity search
        logger.info(f"[{request_id}] Searching for similar images: scope={search_request.search_scope}, threshold={search_request.similarity_threshold}")
        
        search_result = await multimodal_service.visual_similarity_search(
            temp_file_path,
            search_request.search_scope,
            search_request.similarity_threshold,
            search_request.limit,
            search_request.search_filters
        )
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.info(f"[{request_id}] Visual similarity search completed: found {search_result.get('total_found', 0)} results")
        
        # Format response
        response = VisualSimilaritySearchResponse(
            success=search_result.get('success', True),
            query_image=query_image.filename or 'uploaded_image',
            query_analysis=search_result.get('query_analysis'),
            similar_items=search_result.get('similar_items', []),
            search_scope=search_result['search_scope'],
            total_found=search_result.get('total_found', 0),
            similarity_threshold=search_result['similarity_threshold'],
            search_terms=search_result.get('search_terms'),
            processing_time=search_result.get('processing_time'),
            metadata=search_result.get('metadata'),
            error=search_result.get('error'),
            timestamp=datetime.now().isoformat()
        )
        
        return response
        
    except HTTPException:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        raise
    except Exception as e:
        if temp_file_path:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)
        
        logger.error(f"[{request_id}] Visual similarity search failed: {str(e)}")
        return VisualSimilaritySearchResponse(
            success=False,
            query_image=query_image.filename or 'uploaded_image',
            similar_items=[],
            search_scope=request_params.get('search_scope', 'both'),
            total_found=0,
            similarity_threshold=request_params.get('similarity_threshold', 0.7),
            error=str(e),
            timestamp=datetime.now().isoformat()
        )

@multimodal_router.get(
    "/health",
    response_model=HealthCheckResponse,
    summary="Health check for multimodal services",
    description="""
    Check the health status of all multimodal service components:
    - LLaVA vision analysis service
    - Nomic embedding service  
    - Prompt enhancement service
    - Overall multimodal service coordination
    
    Returns the current status of each component for monitoring and debugging.
    """
)
async def health_check():
    """Check health of all multimodal service components"""
    
    logger.info("Performing multimodal services health check")
    
    try:
        health_status = await multimodal_service.health_check()
        
        return HealthCheckResponse(
            multimodal_service=health_status['multimodal_service'],
            llava_service=health_status['llava_service'],
            embedding_service=health_status['embedding_service'],
            prompt_enhancement_service=health_status['prompt_enhancement_service'],
            timestamp=health_status['timestamp']
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthCheckResponse(
            multimodal_service=f"unhealthy: {str(e)}",
            llava_service="unknown",
            embedding_service="unknown", 
            prompt_enhancement_service="unknown",
            timestamp=datetime.now().isoformat()
        )

@multimodal_router.get(
    "/enhancement-modes",
    summary="Get available enhancement modes",
    description="Get list of available enhancement modes with descriptions"
)
async def get_enhancement_modes():
    """Get available enhancement modes and their descriptions"""
    
    enhancement_modes = {
        'visual_matching': {
            'name': 'Visual Matching',
            'description': 'Match overall visual characteristics from reference image',
            'use_cases': ['Recreating similar artwork', 'Style consistency', 'Visual inspiration']
        },
        'style_transfer': {
            'name': 'Style Transfer',
            'description': 'Transfer artistic style from reference image',
            'use_cases': ['Artistic style learning', 'Medium transformation', 'Aesthetic adaptation']
        },
        'mood_matching': {
            'name': 'Mood Matching',
            'description': 'Match emotional tone and atmosphere',
            'use_cases': ['Emotional consistency', 'Atmosphere recreation', 'Mood enhancement']
        },
        'composition_guide': {
            'name': 'Composition Guide',
            'description': 'Learn compositional techniques from reference',
            'use_cases': ['Layout learning', 'Structural guidance', 'Visual balance']
        }
    }
    
    return {
        'enhancement_modes': enhancement_modes,
        'default_mode': 'visual_matching',
        'timestamp': datetime.now().isoformat()
    }

@multimodal_router.get(
    "/analysis-types",
    summary="Get available analysis types",
    description="Get list of available image analysis types with descriptions"
)
async def get_analysis_types():
    """Get available image analysis types and their descriptions"""
    
    analysis_types = {
        'comprehensive': {
            'name': 'Comprehensive Analysis',
            'description': 'Full analysis including style, mood, composition, lighting, and more',
            'output_includes': ['Artistic style', 'Mood/atmosphere', 'Composition', 'Lighting', 'Color palette', 'Objects', 'Technical quality', 'Keywords']
        },
        'style_focused': {
            'name': 'Style-Focused Analysis',
            'description': 'Detailed analysis of artistic style and techniques',
            'output_includes': ['Art movement', 'Artistic techniques', 'Visual aesthetic', 'Style influences', 'Medium identification']
        },
        'mood_focused': {
            'name': 'Mood-Focused Analysis',
            'description': 'Analysis of emotional tone and atmospheric qualities',
            'output_includes': ['Emotional tone', 'Atmosphere', 'Color mood impact', 'Lighting mood', 'Psychological effects']
        },
        'composition_focused': {
            'name': 'Composition-Focused Analysis',
            'description': 'Analysis of visual structure and compositional techniques',
            'output_includes': ['Compositional rules', 'Focal points', 'Visual flow', 'Balance', 'Depth creation', 'Framing']
        },
        'prompt_generation': {
            'name': 'Prompt Generation Analysis',
            'description': 'Analysis optimized for generating text prompts',
            'output_includes': ['Style description', 'Subject matter', 'Composition details', 'Color specifications', 'Lighting description', 'Mood descriptors', 'Technical details', 'AI prompt keywords']
        }
    }
    
    return {
        'analysis_types': analysis_types,
        'default_type': 'comprehensive',
        'timestamp': datetime.now().isoformat()
    }

# Error handlers for the router

@multimodal_router.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions with detailed error response"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            'success': False,
            'error': exc.detail,
            'error_type': 'http_error',
            'timestamp': datetime.now().isoformat()
        }
    )

@multimodal_router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions with error logging"""
    logger.error(f"Unhandled exception in multimodal API: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            'success': False,
            'error': 'Internal server error occurred',
            'error_type': 'server_error',
            'timestamp': datetime.now().isoformat()
        }
    )
