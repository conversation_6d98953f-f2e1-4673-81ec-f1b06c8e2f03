"""
Prompt Enhancement Service for ComfyUI Custom Frontend
Integrates with Ollama LLMs to enhance user prompts for different image generation models
Enhanced with semantic similarity and embedding-based context
"""

from typing import Dict, List, Optional, Union, Literal
from dataclasses import dataclass
from pathlib import Path
import asyncio
import json
import logging
import aiohttp
from typing import Any, Dict
from enum import Enum

# Import embedding service
from .embedding_service import embedding_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageModel(Enum):
    FLUX_DEV = "flux_dev"
    FLUX_KONTEXT = "flux_kontext"
    SD15 = "sd15"
    SDXL = "sdxl"

class EnhancementMode(Enum):
    CREATIVE = "creative"
    TECHNICAL = "technical"
    BALANCED = "balanced"

class LLMModel(Enum):
    MISTRAL_7B = "mistral:7b-instruct"
    LLAMA32 = "llama3.2:latest"
    NOUS_HERMES = "nous-hermes2-mixtral:latest"
    CODELLAMA = "codellama:latest"

@dataclass
class EnhancementRequest:
    user_prompt: str
    target_model: ImageModel
    enhancement_mode: EnhancementMode = EnhancementMode.BALANCED
    style_preference: Optional[str] = None
    quality_level: str = "high"
    reference_image_context: Optional[str] = None
    preservation_elements: Optional[List[str]] = None

@dataclass
class EnhancementResult:
    original_prompt: str
    enhanced_prompt: str
    negative_prompt: Optional[str] = None
    explanation: str = ""
    technical_settings: Optional[Dict] = None
    confidence_score: float = 0.0
    llm_model_used: str = ""
    processing_time: float = 0.0

class PromptEnhancementService:
    """
    Main service class for prompt enhancement using Ollama LLMs
    """
    
    def __init__(self, ollama_base_url: str = "http://localhost:11434"):
        self.ollama_base_url = ollama_base_url
        self.templates_dir = Path(__file__).parent.parent / "docs" / "models" / "enhancement-templates"
        self.templates = self._load_templates()
        
        # Model preferences based on analysis
        self.model_preferences = {
            "primary": LLMModel.MISTRAL_7B,
            "creative": LLMModel.LLAMA32,
            "premium": LLMModel.NOUS_HERMES,
            "technical": LLMModel.CODELLAMA
        }
        
    def _load_templates(self) -> Dict[str, str]:
        """Load enhancement templates for different image models"""
        templates = {}
        template_files = {
            ImageModel.FLUX_DEV: "flux-dev-template.md",
            ImageModel.FLUX_KONTEXT: "flux-kontext-template.md", 
            ImageModel.SD15: "sd15-template.md"
        }
        
        for model, filename in template_files.items():
            template_path = self.templates_dir / filename
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    templates[model.value] = f.read()
            else:
                logger.warning(f"Template file not found: {template_path}")
                
        return templates
    
    async def check_ollama_health(self) -> bool:
        """Check if Ollama service is available"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.ollama_base_url}/api/version") as response:
                    return response.status == 200
        except Exception as e:
            logger.error(f"Ollama health check failed: {e}")
            return False
    
    async def list_available_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.ollama_base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        return [model["name"] for model in data.get("models", [])]
                    return []
        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return []
    
    def _select_llm_model(self, enhancement_mode: EnhancementMode, available_models: List[str]) -> str:
        """Select the best available LLM model based on mode and availability"""
        preference_order = {
            EnhancementMode.CREATIVE: [
                LLMModel.LLAMA32.value,
                LLMModel.MISTRAL_7B.value,
                LLMModel.NOUS_HERMES.value
            ],
            EnhancementMode.TECHNICAL: [
                LLMModel.MISTRAL_7B.value,
                LLMModel.CODELLAMA.value,
                LLMModel.LLAMA32.value
            ],
            EnhancementMode.BALANCED: [
                LLMModel.MISTRAL_7B.value,
                LLMModel.LLAMA32.value,
                LLMModel.NOUS_HERMES.value
            ]
        }
        
        # Find first available model from preference order
        for model in preference_order[enhancement_mode]:
            if model in available_models:
                return model
                
        # Fallback to any available model
        if available_models:
            return available_models[0]
            
        raise Exception("No Ollama models available")
    
    def _build_enhancement_prompt(self, request: EnhancementRequest) -> str:
        """Build the complete prompt for the LLM"""
        template = self.templates.get(request.target_model.value, "")
        
        if not template:
            # Fallback template
            template = """
            You are a prompt enhancement AI. Enhance the following prompt for {target_model}:
            
            User Prompt: {user_prompt}
            Enhancement Mode: {enhancement_mode}
            Style: {style_preference}
            
            Provide an enhanced prompt that is detailed, creative, and optimized for the target model.
            """
        
        # Extract the system instructions from template
        if "```" in template:
            # Extract content between first set of triple backticks
            parts = template.split("```")
            if len(parts) >= 2:
                system_prompt = parts[1].strip()
            else:
                system_prompt = template
        else:
            system_prompt = template
            
        # Replace placeholders
        enhanced_prompt = system_prompt.format(
            user_prompt=request.user_prompt,
            target_model=request.target_model.value,
            enhancement_mode=request.enhancement_mode.value,
            style_preference=request.style_preference or "default",
            quality_level=request.quality_level,
            image_context=request.reference_image_context or "none",
            preservation=", ".join(request.preservation_elements or [])
        )
        
        return enhanced_prompt
    
    async def _call_ollama(self, model: str, prompt: str, **kwargs) -> str:
        """Make API call to Ollama"""
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.9),
                "max_tokens": kwargs.get("max_tokens", 500)
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_base_url}/api/generate", 
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("response", "")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API error: {response.status} - {error_text}")
        except Exception as e:
            logger.error(f"Ollama API call failed: {e}")
            raise
    
    def _parse_enhancement_response(self, response: str, request: EnhancementRequest) -> EnhancementResult:
        """Parse the LLM response into structured result"""
        lines = response.strip().split('\n')
        
        enhanced_prompt = ""
        negative_prompt = ""
        explanation = ""
        technical_settings = {}
        
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            
            # Detect sections
            if "Enhanced Prompt:" in line or "enhanced prompt:" in line.lower():
                if current_section and current_content:
                    self._process_section(current_section, current_content, 
                                        enhanced_prompt, negative_prompt, explanation, technical_settings)
                current_section = "enhanced"
                current_content = []
            elif "Negative Prompt:" in line or "negative prompt:" in line.lower():
                if current_section and current_content:
                    self._process_section(current_section, current_content,
                                        enhanced_prompt, negative_prompt, explanation, technical_settings)
                current_section = "negative"
                current_content = []
            elif "Explanation:" in line or "Changes Made:" in line:
                if current_section and current_content:
                    self._process_section(current_section, current_content,
                                        enhanced_prompt, negative_prompt, explanation, technical_settings)
                current_section = "explanation"
                current_content = []
            elif "Technical Settings:" in line or "Recommended Settings:" in line:
                if current_section and current_content:
                    self._process_section(current_section, current_content,
                                        enhanced_prompt, negative_prompt, explanation, technical_settings)
                current_section = "technical"
                current_content = []
            else:
                if line:  # Skip empty lines
                    current_content.append(line)
        
        # Process final section
        if current_section and current_content:
            self._process_section(current_section, current_content,
                                enhanced_prompt, negative_prompt, explanation, technical_settings)
        
        # If no structured response, use entire response as enhanced prompt
        if not enhanced_prompt and response:
            enhanced_prompt = response.strip()
        
        return EnhancementResult(
            original_prompt=request.user_prompt,
            enhanced_prompt=enhanced_prompt,
            negative_prompt=negative_prompt if negative_prompt else None,
            explanation=explanation,
            technical_settings=technical_settings if technical_settings else None,
            confidence_score=0.8,  # Default confidence
            llm_model_used="",  # Will be set by caller
            processing_time=0.0   # Will be set by caller
        )
    
    def _process_section(self, section: str, content: List[str], 
                        enhanced_prompt: str, negative_prompt: str, 
                        explanation: str, technical_settings: dict):
        """Process a section of the LLM response"""
        content_text = " ".join(content).strip()
        
        if section == "enhanced":
            enhanced_prompt = content_text
        elif section == "negative":
            negative_prompt = content_text
        elif section == "explanation":
            explanation = content_text
        elif section == "technical":
            # Try to parse technical settings
            try:
                # Simple parsing for common patterns
                settings = {}
                for line in content:
                    if ":" in line:
                        key, value = line.split(":", 1)
                        settings[key.strip()] = value.strip()
                technical_settings.update(settings)
            except:
                # If parsing fails, store as text
                technical_settings["raw"] = content_text
    
    async def enhance_prompt(self, request: EnhancementRequest) -> EnhancementResult:
        """
        Main method to enhance a user prompt
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Check Ollama availability
            if not await self.check_ollama_health():
                raise Exception("Ollama service is not available")
            
            # Get available models
            available_models = await self.list_available_models()
            if not available_models:
                raise Exception("No Ollama models available")
            
            # Select best LLM model
            selected_model = self._select_llm_model(request.enhancement_mode, available_models)
            logger.info(f"Using LLM model: {selected_model}")
            
            # Build enhancement prompt
            enhancement_prompt = self._build_enhancement_prompt(request)
            
            # Call Ollama
            response = await self._call_ollama(
                model=selected_model,
                prompt=enhancement_prompt,
                temperature=0.7 if request.enhancement_mode == EnhancementMode.CREATIVE else 0.6,
                max_tokens=600
            )
            
            # Parse response
            result = self._parse_enhancement_response(response, request)
            result.llm_model_used = selected_model
            result.processing_time = asyncio.get_event_loop().time() - start_time
            
            logger.info(f"Prompt enhancement completed in {result.processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Prompt enhancement failed: {e}")
            # Return fallback result
            return EnhancementResult(
                original_prompt=request.user_prompt,
                enhanced_prompt=request.user_prompt,  # Fallback to original
                explanation=f"Enhancement failed: {str(e)}",
                confidence_score=0.0,
                processing_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def enhance_with_semantic_context(self, request: EnhancementRequest) -> EnhancementResult:
        """Enhanced version that uses semantic similarity for better context"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Get semantic context from embedding service
            semantic_context = await embedding_service.get_enhanced_context_for_prompt(
                request.user_prompt,
                include_successful=True
            )
            
            # Get similar prompts for inspiration
            similar_prompts = semantic_context['similar_prompts']
            enhancement_hints = semantic_context['enhancement_hints']
            
            # Build enhanced prompt with semantic context
            enhanced_request = request
            if similar_prompts:
                # Add successful examples to the enhancement template
                examples_text = "\n".join([
                    f"Similar successful prompt (similarity: {p['similarity']:.2f}): {p['prompt']}"
                    for p in similar_prompts[:3]
                ])
                
                enhanced_prompt_context = f"""
Based on these similar successful prompts:
{examples_text}

Enhancement hints: {', '.join(enhancement_hints)}

User prompt to enhance: {request.user_prompt}
"""
                # Update the user prompt with context
                enhanced_request.user_prompt = enhanced_prompt_context
            
            # Get the regular enhancement
            result = await self.enhance_prompt(enhanced_request)
            
            # Store this prompt and its enhancement for future reference
            await embedding_service.store_prompt_embedding(
                prompt=result.enhanced_prompt,
                model_used=request.target_model.value,
                quality_score=result.confidence_score,
                category=await embedding_service.auto_categorize_prompt(request.user_prompt)
            )
            
            # Add semantic metadata to result
            result.semantic_context = {
                'similar_prompts_count': len(similar_prompts),
                'enhancement_hints': enhancement_hints,
                'suggested_category': semantic_context.get('suggested_categories', [])
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Semantic enhancement failed: {e}")
            # Fallback to regular enhancement
            return await self.enhance_prompt(request)
    
    async def enhance_with_multimodal_context(
        self, 
        user_prompt: str, 
        multimodal_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Enhanced prompt enhancement using multimodal context (visual + semantic)
        
        Args:
            user_prompt: Original user prompt
            multimodal_context: Dict containing visual analysis and semantic context
            
        Returns:
            Dict containing enhanced prompt and analysis
        """
        
        logger.info("Starting multimodal context enhancement")
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Extract multimodal context components
            visual_analysis = multimodal_context.get('visual_analysis', {})
            similar_prompts = multimodal_context.get('similar_prompts', [])
            enhancement_mode = multimodal_context.get('enhancement_mode', 'visual_matching')
            config = multimodal_context.get('config', {})
            
            # Build comprehensive enhancement prompt
            enhancement_prompt = self._build_multimodal_enhancement_prompt(
                user_prompt, visual_analysis, similar_prompts, enhancement_mode
            )
            
            # Select appropriate model (use primary model for multimodal)
            available_models = await self.list_available_models()
            model_name = self._select_llm_model(EnhancementMode.BALANCED, available_models)
            
            # Generate enhanced prompt with multimodal context
            enhanced_response = await self._call_ollama(
                model_name, 
                enhancement_prompt,
                temperature=0.7,
                max_tokens=800
            )
            
            # Parse the response
            enhanced_prompt, explanation = self._parse_multimodal_response(enhanced_response)
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            # Calculate confidence based on available context
            confidence = self._calculate_multimodal_confidence(
                visual_analysis, similar_prompts, enhanced_response
            )
            
            # Extract visual elements that were incorporated
            visual_elements = self._extract_incorporated_visual_elements(
                visual_analysis, enhanced_prompt
            )
            
            return {
                'enhanced_prompt': enhanced_prompt or user_prompt,  # Fallback to original
                'explanation': explanation,
                'confidence': confidence,
                'multimodal_context_used': True,
                'enhancement_mode': enhancement_mode,
                'visual_elements_incorporated': visual_elements,
                'processing_time': processing_time,
                'model_used': model_name
            }
            
        except Exception as e:
            logger.error(f"Multimodal enhancement failed: {str(e)}")
            
            # Fallback to basic enhancement if available
            try:
                request = EnhancementRequest(
                    user_prompt=user_prompt,
                    target_model=ImageModel.FLUX_DEV,  # Default model
                    enhancement_mode=EnhancementMode.BALANCED
                )
                result = await self.enhance_prompt(request)
                
                return {
                    'enhanced_prompt': result.enhanced_prompt,
                    'explanation': result.explanation,
                    'confidence': result.confidence_score,
                    'multimodal_context_used': False,
                    'enhancement_mode': 'fallback',
                    'visual_elements_incorporated': [],
                    'processing_time': result.processing_time,
                    'model_used': result.llm_model_used
                }
            except Exception as fallback_error:
                logger.error(f"Fallback enhancement also failed: {str(fallback_error)}")
                
                # Final fallback - return original prompt
                return {
                    'enhanced_prompt': user_prompt,
                    'explanation': 'Enhancement failed, returned original prompt',
                    'confidence': 0.3,
                    'multimodal_context_used': False,
                    'enhancement_mode': 'failed',
                    'visual_elements_incorporated': [],
                    'processing_time': 0.0,
                    'model_used': 'none'
                }
    
    def _build_multimodal_enhancement_prompt(
        self, 
        user_prompt: str, 
        visual_analysis: Dict[str, Any], 
        similar_prompts: List[Dict[str, Any]], 
        enhancement_mode: str
    ) -> str:
        """Build comprehensive prompt for multimodal enhancement"""
        
        # Extract visual elements
        visual_elements = visual_analysis.get('visual_elements', {})
        
        # Format visual analysis for LLM
        visual_description = self._format_visual_analysis_for_llm(visual_elements)
        
        # Format similar prompts
        similar_prompts_text = self._format_similar_prompts_for_llm(similar_prompts)
        
        # Create mode-specific instructions
        mode_instructions = self._get_mode_specific_instructions(enhancement_mode)
        
        # Build the complete enhancement prompt
        enhancement_prompt = f"""
You are an expert AI prompt engineer specializing in image generation prompts. Your task is to enhance a user's prompt by incorporating visual analysis from a reference image and learning from similar successful prompts.

REFERENCE IMAGE ANALYSIS:
{visual_description}

SIMILAR SUCCESSFUL PROMPTS:
{similar_prompts_text}

ENHANCEMENT MODE: {enhancement_mode}
{mode_instructions}

USER'S ORIGINAL PROMPT: "{user_prompt}"

TASK:
Enhance the user's prompt by thoughtfully incorporating relevant visual elements from the reference image analysis. The enhanced prompt should:

1. Maintain the user's original intent and core subject matter
2. Incorporate relevant visual characteristics from the reference image
3. Use insights from similar successful prompts
4. Be optimized for AI image generation
5. Be specific, detailed, and creative

RESPONSE FORMAT:
Enhanced Prompt: [Your enhanced prompt here]

Explanation: [Brief explanation of what you incorporated from the visual analysis and why]

Important: Focus on enhancing, not replacing. The goal is to make the prompt more effective while respecting the user's original vision.
"""
        
        return enhancement_prompt
    
    def _format_visual_analysis_for_llm(self, visual_elements: Dict[str, Any]) -> str:
        """Format visual analysis data for LLM consumption"""
        
        formatted_parts = []
        
        for element_type, element_data in visual_elements.items():
            if isinstance(element_data, dict):
                primary_key = None
                for key in element_data.keys():
                    if key.startswith('primary_'):
                        primary_key = key
                        break
                
                if primary_key and element_data[primary_key] != 'unknown':
                    value = element_data[primary_key]
                    formatted_parts.append(f"- {element_type.replace('_', ' ').title()}: {value}")
            elif isinstance(element_data, list) and element_data:
                formatted_parts.append(f"- {element_type.replace('_', ' ').title()}: {', '.join(element_data[:5])}")
        
        return '\n'.join(formatted_parts) if formatted_parts else "- Visual analysis not available"
    
    def _format_similar_prompts_for_llm(self, similar_prompts: List[Dict[str, Any]]) -> str:
        """Format similar prompts for LLM consumption"""
        
        if not similar_prompts:
            return "- No similar prompts found"
        
        formatted_prompts = []
        for i, prompt_data in enumerate(similar_prompts[:3], 1):  # Limit to top 3
            prompt_text = prompt_data.get('prompt', 'No prompt text')
            similarity = prompt_data.get('similarity_score', 0.0)
            formatted_prompts.append(f"{i}. (Similarity: {similarity:.2f}) {prompt_text}")
        
        return '\n'.join(formatted_prompts)
    
    def _get_mode_specific_instructions(self, enhancement_mode: str) -> str:
        """Get specific instructions based on enhancement mode"""
        
        instructions = {
            'visual_matching': """
Focus on matching the overall visual characteristics from the reference image.
Pay attention to style, composition, lighting, and color palette.
Incorporate visual elements that would recreate similar aesthetic qualities.
""",
            'style_transfer': """
Focus primarily on transferring the artistic style from the reference image.
Emphasize artistic techniques, medium, and stylistic approach.
Adapt the style to work with the user's subject matter.
""",
            'mood_matching': """
Focus on capturing and recreating the emotional tone and atmosphere.
Pay special attention to mood descriptors, lighting quality, and color relationships.
Ensure the enhanced prompt conveys similar emotional impact.
""",
            'composition_guide': """
Focus on compositional elements and visual structure from the reference.
Incorporate layout principles, balance, and visual flow.
Adapt compositional techniques to enhance the user's concept.
"""
        }
        
        return instructions.get(enhancement_mode, instructions['visual_matching'])
    
    def _parse_multimodal_response(self, response: str) -> tuple:
        """Parse the multimodal enhancement response"""
        
        enhanced_prompt = ""
        explanation = ""
        
        lines = response.strip().split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('Enhanced Prompt:'):
                if current_section == 'explanation' and current_content:
                    explanation = '\n'.join(current_content).strip()
                current_section = 'enhanced'
                current_content = []
                # Extract prompt from same line if present
                prompt_part = line.replace('Enhanced Prompt:', '').strip()
                if prompt_part:
                    current_content.append(prompt_part)
            elif line.startswith('Explanation:'):
                if current_section == 'enhanced' and current_content:
                    enhanced_prompt = '\n'.join(current_content).strip()
                current_section = 'explanation'
                current_content = []
                # Extract explanation from same line if present
                explanation_part = line.replace('Explanation:', '').strip()
                if explanation_part:
                    current_content.append(explanation_part)
            elif line and current_section:
                current_content.append(line)
        
        # Handle final section
        if current_section == 'enhanced' and current_content:
            enhanced_prompt = '\n'.join(current_content).strip()
        elif current_section == 'explanation' and current_content:
            explanation = '\n'.join(current_content).strip()
        
        # Fallback parsing if structured format not found
        if not enhanced_prompt and not explanation:
            # Try to extract from unstructured response
            response_lines = response.strip().split('\n')
            if response_lines:
                # Use first substantial line as enhanced prompt
                for line in response_lines:
                    line = line.strip()
                    if line and not line.startswith(('Enhanced', 'Explanation', '-', '*')):
                        enhanced_prompt = line
                        break
                
                # Use remaining content as explanation
                explanation = "Enhanced based on visual analysis and similar prompts"
        
        return enhanced_prompt, explanation
    
    def _calculate_multimodal_confidence(
        self, 
        visual_analysis: Dict[str, Any], 
        similar_prompts: List[Dict[str, Any]], 
        enhanced_response: str
    ) -> float:
        """Calculate confidence score for multimodal enhancement"""
        
        confidence_factors = []
        
        # Visual analysis quality
        visual_confidence = visual_analysis.get('confidence_score', 0.5)
        confidence_factors.append(visual_confidence * 0.3)
        
        # Similar prompts availability
        similar_prompts_factor = min(len(similar_prompts) * 0.15, 0.3)
        confidence_factors.append(similar_prompts_factor)
        
        # Response quality (length and structure)
        response_quality = min(len(enhanced_response) / 200, 1.0) * 0.2
        confidence_factors.append(response_quality)
        
        # Structured response bonus
        if 'Enhanced Prompt:' in enhanced_response and 'Explanation:' in enhanced_response:
            confidence_factors.append(0.2)
        else:
            confidence_factors.append(0.1)
        
        return min(sum(confidence_factors), 1.0)
    
    def _extract_incorporated_visual_elements(
        self, 
        visual_analysis: Dict[str, Any], 
        enhanced_prompt: str
    ) -> List[str]:
        """Extract which visual elements were incorporated into the enhanced prompt"""
        
        incorporated_elements = []
        visual_elements = visual_analysis.get('visual_elements', {})
        enhanced_lower = enhanced_prompt.lower()
        
        # Check for style elements
        style_data = visual_elements.get('style', {})
        if isinstance(style_data, dict):
            primary_style = style_data.get('primary_style', '')
            if primary_style and primary_style.lower() in enhanced_lower:
                incorporated_elements.append(f"Style: {primary_style}")
        
        # Check for mood elements
        mood_data = visual_elements.get('mood', {})
        if isinstance(mood_data, dict):
            primary_mood = mood_data.get('primary_mood', '')
            if primary_mood and primary_mood.lower() in enhanced_lower:
                incorporated_elements.append(f"Mood: {primary_mood}")
        
        # Check for color elements
        color_data = visual_elements.get('color_palette', {})
        if isinstance(color_data, dict):
            primary_colors = color_data.get('primary_palette', '')
            if primary_colors:
                color_words = primary_colors.lower().split()
                for color_word in color_words:
                    if len(color_word) > 3 and color_word in enhanced_lower:
                        incorporated_elements.append(f"Color: {color_word}")
                        break
        
        # Check for lighting elements
        lighting_data = visual_elements.get('lighting', {})
        if isinstance(lighting_data, dict):
            primary_lighting = lighting_data.get('primary_lighting', '')
            if primary_lighting and any(word in enhanced_lower for word in primary_lighting.lower().split()):
                incorporated_elements.append(f"Lighting: {primary_lighting[:30]}")
        
        # Check for keywords
        keywords = visual_elements.get('keywords', [])
        if isinstance(keywords, list):
            for keyword in keywords[:5]:  # Check first 5 keywords
                if keyword.lower() in enhanced_lower:
                    incorporated_elements.append(f"Keyword: {keyword}")
        
        return incorporated_elements[:8]  # Limit to 8 elements
    
    async def get_prompt_suggestions(self, partial_prompt: str, limit: int = 5) -> List[Dict]:
        """Get prompt suggestions based on semantic similarity"""
        try:
            similar_prompts = await embedding_service.find_similar_prompts(
                partial_prompt,
                limit=limit,
                min_similarity=0.2
            )
            
            suggestions = []
            for prompt in similar_prompts:
                suggestions.append({
                    'text': prompt['prompt'],
                    'similarity': prompt['similarity'],
                    'quality_score': prompt['quality_score'],
                    'model_used': prompt['model_used'],
                    'category': prompt['category']
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to get prompt suggestions: {e}")
            return []
    
    async def analyze_prompt_quality(self, prompt: str) -> Dict:
        """Analyze prompt quality and suggest improvements"""
        try:
            improvements = await embedding_service.suggest_prompt_improvements(prompt)
            return {
                'quality_prediction': improvements['confidence'],
                'suggestions': improvements['suggestions'],
                'enhancement_hints': improvements['enhancement_hints'],
                'similar_successful_count': improvements['similar_count']
            }
        except Exception as e:
            logger.error(f"Failed to analyze prompt quality: {e}")
            return {
                'quality_prediction': 0.0,
                'suggestions': [],
                'enhancement_hints': [],
                'similar_successful_count': 0
            }

# Convenience functions for common use cases
async def enhance_for_flux_dev(prompt: str, mode: str = "balanced") -> EnhancementResult:
    """Quick enhancement for FLUX.1 Dev"""
    service = PromptEnhancementService()
    request = EnhancementRequest(
        user_prompt=prompt,
        target_model=ImageModel.FLUX_DEV,
        enhancement_mode=EnhancementMode(mode)
    )
    return await service.enhance_prompt(request)

async def enhance_for_flux_kontext(prompt: str, image_context: str, mode: str = "balanced") -> EnhancementResult:
    """Quick enhancement for FLUX.1 Kontext"""
    service = PromptEnhancementService()
    request = EnhancementRequest(
        user_prompt=prompt,
        target_model=ImageModel.FLUX_KONTEXT,
        enhancement_mode=EnhancementMode(mode),
        reference_image_context=image_context
    )
    return await service.enhance_prompt(request)

async def enhance_for_sd15(prompt: str, style: str = "photorealistic") -> EnhancementResult:
    """Quick enhancement for Stable Diffusion 1.5"""
    service = PromptEnhancementService()
    request = EnhancementRequest(
        user_prompt=prompt,
        target_model=ImageModel.SD15,
        style_preference=style
    )
    return await service.enhance_prompt(request)

# Example usage
if __name__ == "__main__":
    async def test_enhancement():
        service = PromptEnhancementService()
        
        # Test basic enhancement
        request = EnhancementRequest(
            user_prompt="a cat sitting in a garden",
            target_model=ImageModel.FLUX_DEV,
            enhancement_mode=EnhancementMode.CREATIVE
        )
        
        result = await service.enhance_prompt(request)
        print("Original:", result.original_prompt)
        print("Enhanced:", result.enhanced_prompt)
        print("Model used:", result.llm_model_used)
        print("Time:", f"{result.processing_time:.2f}s")
    
    # Run test
    asyncio.run(test_enhancement())
