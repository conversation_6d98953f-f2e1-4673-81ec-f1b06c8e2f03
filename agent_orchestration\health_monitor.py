#!/usr/bin/env python3
"""
Enhanced Agent Health Monitoring System
========================================

Provides comprehensive health monitoring, performance tracking, and automatic
recovery capabilities for the agent orchestration system.

Features:
- Real-time health monitoring
- Performance metrics collection
- Automatic recovery procedures
- Predictive failure analysis
- Resource usage tracking
- Alert system integration

Author: System Enhancement Module
Version: 2.0
"""

import asyncio
import json
import logging
import psutil
import sqlite3
import time
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import yaml
import subprocess
import requests
from collections import deque
import threading
import statistics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/agent_health_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class HealthMetrics:
    """Health metrics for an agent"""
    agent_id: str
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    execution_time: float
    error_count: int
    success_count: int
    last_execution: datetime
    status: str  # 'healthy', 'warning', 'critical', 'offline'
    response_time: float
    resource_efficiency: float

@dataclass
class HealthAlert:
    """Health alert definition"""
    alert_id: str
    agent_id: str
    severity: str  # 'info', 'warning', 'critical'
    message: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None

class PerformanceTracker:
    """Tracks performance metrics with rolling windows"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.metrics: Dict[str, deque] = {}
        
    def add_metric(self, metric_name: str, value: float):
        """Add a metric value"""
        if metric_name not in self.metrics:
            self.metrics[metric_name] = deque(maxlen=self.window_size)
        self.metrics[metric_name].append((time.time(), value))
        
    def get_average(self, metric_name: str, time_window: int = 300) -> float:
        """Get average value for a metric within time window (seconds)"""
        if metric_name not in self.metrics:
            return 0.0
            
        current_time = time.time()
        values = [
            value for timestamp, value in self.metrics[metric_name]
            if current_time - timestamp <= time_window
        ]
        
        return statistics.mean(values) if values else 0.0
        
    def get_trend(self, metric_name: str, time_window: int = 300) -> str:
        """Get trend direction for a metric"""
        if metric_name not in self.metrics or len(self.metrics[metric_name]) < 2:
            return "stable"
            
        current_time = time.time()
        values = [
            (timestamp, value) for timestamp, value in self.metrics[metric_name]
            if current_time - timestamp <= time_window
        ]
        
        if len(values) < 2:
            return "stable"
            
        # Simple linear trend calculation
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = statistics.mean([v for _, v in first_half])
        second_avg = statistics.mean([v for _, v in second_half])
        
        if second_avg > first_avg * 1.1:
            return "increasing"
        elif second_avg < first_avg * 0.9:
            return "decreasing"
        else:
            return "stable"

class PredictiveAnalyzer:
    """Predictive analysis for potential failures"""
    
    def __init__(self):
        self.patterns: Dict[str, List[Dict]] = {}
        
    def analyze_failure_risk(self, metrics: HealthMetrics, historical_data: List[HealthMetrics]) -> float:
        """Analyze risk of failure based on current and historical metrics"""
        if not historical_data:
            return 0.0
            
        risk_factors = []
        
        # CPU usage trend risk
        cpu_values = [m.cpu_usage for m in historical_data[-10:]]
        if cpu_values and metrics.cpu_usage > statistics.mean(cpu_values) * 1.5:
            risk_factors.append(0.3)
            
        # Memory usage trend risk
        memory_values = [m.memory_usage for m in historical_data[-10:]]
        if memory_values and metrics.memory_usage > statistics.mean(memory_values) * 1.5:
            risk_factors.append(0.4)
            
        # Error rate risk
        recent_errors = sum(1 for m in historical_data[-5:] if m.error_count > 0)
        if recent_errors >= 3:
            risk_factors.append(0.5)
            
        # Response time degradation
        response_times = [m.response_time for m in historical_data[-10:]]
        if response_times and metrics.response_time > statistics.mean(response_times) * 2:
            risk_factors.append(0.3)
            
        return min(sum(risk_factors), 1.0)
        
    def predict_failure_time(self, agent_id: str, historical_data: List[HealthMetrics]) -> Optional[datetime]:
        """Predict when an agent might fail based on trends"""
        if len(historical_data) < 10:
            return None
            
        # Analyze resource consumption trends
        recent_data = historical_data[-20:]
        cpu_trend = self._calculate_trend([m.cpu_usage for m in recent_data])
        memory_trend = self._calculate_trend([m.memory_usage for m in recent_data])
        
        # If both CPU and memory are increasing rapidly, predict failure
        if cpu_trend > 0.05 and memory_trend > 0.05:  # 5% increase per measurement
            # Rough estimation: when will resources hit 95%?
            current_cpu = recent_data[-1].cpu_usage
            current_memory = recent_data[-1].memory_usage
            
            if cpu_trend > 0:
                time_to_cpu_limit = (95 - current_cpu) / cpu_trend
                return datetime.now() + timedelta(minutes=time_to_cpu_limit * 5)  # Assuming 5-min intervals
                
        return None
        
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope for a series of values"""
        if len(values) < 2:
            return 0.0
            
        n = len(values)
        x_values = list(range(n))
        
        # Simple linear regression slope
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        return numerator / denominator if denominator != 0 else 0.0

class RecoveryManager:
    """Manages automatic recovery procedures"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.recovery_actions = self._load_recovery_actions()
        
    def _load_recovery_actions(self) -> Dict[str, List[Callable]]:
        """Load recovery actions from configuration"""
        return {
            'restart_agent': self._restart_agent,
            'clear_cache': self._clear_cache,
            'reset_database_connections': self._reset_database_connections,
            'free_memory': self._free_memory,
            'restart_dependencies': self._restart_dependencies
        }
        
    async def execute_recovery(self, agent_id: str, recovery_type: str) -> bool:
        """Execute recovery action for an agent"""
        try:
            if recovery_type in self.recovery_actions:
                action = self.recovery_actions[recovery_type]
                success = await action(agent_id)
                logger.info(f"Recovery action '{recovery_type}' for agent '{agent_id}': {'success' if success else 'failed'}")
                return success
            else:
                logger.warning(f"Unknown recovery type: {recovery_type}")
                return False
        except Exception as e:
            logger.error(f"Recovery action failed for agent '{agent_id}': {e}")
            return False
            
    async def _restart_agent(self, agent_id: str) -> bool:
        """Restart a specific agent"""
        try:
            # Kill existing agent process
            result = subprocess.run(
                ['pkill', '-f', f'agent_{agent_id}'],
                capture_output=True, text=True
            )
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Restart agent
            result = subprocess.run(
                ['python', f'scripts/{agent_id}.py'],
                capture_output=True, text=True
            )
            
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Failed to restart agent {agent_id}: {e}")
            return False
            
    async def _clear_cache(self, agent_id: str) -> bool:
        """Clear cache for an agent"""
        try:
            cache_dir = Path(f"cache/{agent_id}")
            if cache_dir.exists():
                import shutil
                shutil.rmtree(cache_dir)
                cache_dir.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to clear cache for agent {agent_id}: {e}")
            return False
            
    async def _reset_database_connections(self, agent_id: str) -> bool:
        """Reset database connections for an agent"""
        try:
            # This would depend on your specific database setup
            db_path = f"data/{agent_id}.db"
            if Path(db_path).exists():
                # Close and reopen database connections
                # Implementation depends on your database layer
                pass
            return True
        except Exception as e:
            logger.error(f"Failed to reset database connections for agent {agent_id}: {e}")
            return False
            
    async def _free_memory(self, agent_id: str) -> bool:
        """Attempt to free memory for an agent"""
        try:
            # Force garbage collection and memory cleanup
            import gc
            gc.collect()
            return True
        except Exception as e:
            logger.error(f"Failed to free memory for agent {agent_id}: {e}")
            return False
            
    async def _restart_dependencies(self, agent_id: str) -> bool:
        """Restart dependencies for an agent"""
        try:
            # This would restart services like ComfyUI, FastAPI, etc.
            # Implementation depends on your service management
            return True
        except Exception as e:
            logger.error(f"Failed to restart dependencies for agent {agent_id}: {e}")
            return False

class AgentHealthMonitor:
    """Main health monitoring system"""
    
    def __init__(self, config_path: str = "agent_orchestration/agent_config_enhanced.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.db_path = "data/agent_health.db"
        self.performance_tracker = PerformanceTracker()
        self.predictive_analyzer = PredictiveAnalyzer()
        self.recovery_manager = RecoveryManager(config_path)
        self.alerts: List[HealthAlert] = []
        self.monitoring_active = False
        
        # Initialize database
        self._init_database()
        
    def _load_config(self) -> Dict:
        """Load monitoring configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}
            
    def _init_database(self):
        """Initialize health monitoring database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS health_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        agent_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        cpu_usage REAL,
                        memory_usage REAL,
                        execution_time REAL,
                        error_count INTEGER,
                        success_count INTEGER,
                        last_execution TEXT,
                        status TEXT,
                        response_time REAL,
                        resource_efficiency REAL
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS health_alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        alert_id TEXT UNIQUE NOT NULL,
                        agent_id TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        message TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        resolved BOOLEAN DEFAULT FALSE,
                        resolution_time TEXT
                    )
                ''')
                
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            
    def start_monitoring(self):
        """Start the health monitoring system"""
        self.monitoring_active = True
        logger.info("Agent health monitoring started")
        
        # Start monitoring loop in background thread
        threading.Thread(target=self._monitoring_loop, daemon=True).start()
        
    def stop_monitoring(self):
        """Stop the health monitoring system"""
        self.monitoring_active = False
        logger.info("Agent health monitoring stopped")
        
    def _monitoring_loop(self):
        """Main monitoring loop"""
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()
        
        while self.monitoring_active:
            try:
                loop.run_until_complete(self._check_all_agents())
                time.sleep(self.config.get('global', {}).get('health_monitoring', {}).get('check_interval', 300))
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait before retrying
                
    async def _check_all_agents(self):
        """Check health of all configured agents"""
        agents = self.config.get('agents', {})
        
        for agent_id, agent_config in agents.items():
            if agent_config.get('health', {}).get('enabled', True):
                await self._check_agent_health(agent_id, agent_config)
                
    async def _check_agent_health(self, agent_id: str, agent_config: Dict):
        """Check health of a specific agent"""
        try:
            # Collect metrics
            metrics = await self._collect_agent_metrics(agent_id, agent_config)
            
            # Store metrics
            self._store_metrics(metrics)
            
            # Update performance tracker
            self.performance_tracker.add_metric(f"{agent_id}_cpu", metrics.cpu_usage)
            self.performance_tracker.add_metric(f"{agent_id}_memory", metrics.memory_usage)
            self.performance_tracker.add_metric(f"{agent_id}_response_time", metrics.response_time)
            
            # Analyze health status
            await self._analyze_health_status(metrics)
            
            # Predictive analysis
            await self._run_predictive_analysis(agent_id, metrics)
            
        except Exception as e:
            logger.error(f"Error checking health for agent {agent_id}: {e}")
            
    async def _collect_agent_metrics(self, agent_id: str, agent_config: Dict) -> HealthMetrics:
        """Collect metrics for an agent"""
        # Get process information
        cpu_usage = 0.0
        memory_usage = 0.0
        
        try:
            # Find agent process
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if any(agent_id in str(cmd) for cmd in proc.info['cmdline'] or []):
                    cpu_usage = proc.cpu_percent()
                    memory_usage = proc.memory_percent()
                    break
        except Exception as e:
            logger.warning(f"Could not get process metrics for {agent_id}: {e}")
            
        # Test agent responsiveness
        response_time = await self._test_agent_responsiveness(agent_id, agent_config)
        
        # Get execution history from database
        error_count, success_count, last_execution = self._get_execution_history(agent_id)
        
        # Determine status
        status = self._determine_status(cpu_usage, memory_usage, response_time, error_count)
        
        # Calculate resource efficiency
        resource_efficiency = self._calculate_resource_efficiency(cpu_usage, memory_usage, response_time)
        
        return HealthMetrics(
            agent_id=agent_id,
            timestamp=datetime.now(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            execution_time=response_time,
            error_count=error_count,
            success_count=success_count,
            last_execution=last_execution,
            status=status,
            response_time=response_time,
            resource_efficiency=resource_efficiency
        )
        
    async def _test_agent_responsiveness(self, agent_id: str, agent_config: Dict) -> float:
        """Test agent responsiveness"""
        start_time = time.time()
        
        try:
            # Try to ping agent health endpoint if available
            health_commands = agent_config.get('health', {}).get('check_commands', [])
            
            if health_commands:
                # Execute first health check command
                result = subprocess.run(
                    health_commands[0].split(),
                    capture_output=True,
                    text=True,
                    timeout=agent_config.get('health', {}).get('timeout', 30)
                )
                
                if result.returncode == 0:
                    return (time.time() - start_time) * 1000  # Convert to milliseconds
                else:
                    return float('inf')  # Indicates failure
                    
        except Exception as e:
            logger.warning(f"Health check failed for {agent_id}: {e}")
            return float('inf')
            
        return (time.time() - start_time) * 1000
        
    def _get_execution_history(self, agent_id: str) -> tuple:
        """Get execution history for an agent"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get recent error and success counts
                cursor.execute('''
                    SELECT SUM(error_count), SUM(success_count), MAX(timestamp)
                    FROM health_metrics 
                    WHERE agent_id = ? AND timestamp > datetime('now', '-1 hour')
                ''', (agent_id,))
                
                result = cursor.fetchone()
                error_count = result[0] or 0
                success_count = result[1] or 0
                last_execution = datetime.fromisoformat(result[2]) if result[2] else datetime.min
                
                return error_count, success_count, last_execution
                
        except Exception as e:
            logger.error(f"Error getting execution history for {agent_id}: {e}")
            return 0, 0, datetime.min
            
    def _determine_status(self, cpu_usage: float, memory_usage: float, response_time: float, error_count: int) -> str:
        """Determine agent health status"""
        if response_time == float('inf'):
            return 'offline'
        elif cpu_usage > 90 or memory_usage > 90 or error_count > 5:
            return 'critical'
        elif cpu_usage > 70 or memory_usage > 70 or error_count > 2 or response_time > 5000:
            return 'warning'
        else:
            return 'healthy'
            
    def _calculate_resource_efficiency(self, cpu_usage: float, memory_usage: float, response_time: float) -> float:
        """Calculate resource efficiency score (0-100)"""
        if response_time == float('inf'):
            return 0.0
            
        # Efficiency is inversely related to resource usage and response time
        cpu_efficiency = max(0, 100 - cpu_usage)
        memory_efficiency = max(0, 100 - memory_usage)
        time_efficiency = max(0, 100 - min(response_time / 100, 100))  # Normalize response time
        
        return (cpu_efficiency + memory_efficiency + time_efficiency) / 3
        
    def _store_metrics(self, metrics: HealthMetrics):
        """Store metrics in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO health_metrics 
                    (agent_id, timestamp, cpu_usage, memory_usage, execution_time, 
                     error_count, success_count, last_execution, status, response_time, resource_efficiency)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metrics.agent_id,
                    metrics.timestamp.isoformat(),
                    metrics.cpu_usage,
                    metrics.memory_usage,
                    metrics.execution_time,
                    metrics.error_count,
                    metrics.success_count,
                    metrics.last_execution.isoformat(),
                    metrics.status,
                    metrics.response_time,
                    metrics.resource_efficiency
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error storing metrics: {e}")
            
    async def _analyze_health_status(self, metrics: HealthMetrics):
        """Analyze health status and trigger alerts/recovery if needed"""
        # Check for critical issues
        if metrics.status == 'critical':
            await self._handle_critical_status(metrics)
        elif metrics.status == 'warning':
            await self._handle_warning_status(metrics)
        elif metrics.status == 'offline':
            await self._handle_offline_status(metrics)
            
    async def _handle_critical_status(self, metrics: HealthMetrics):
        """Handle critical health status"""
        alert = HealthAlert(
            alert_id=f"{metrics.agent_id}_critical_{int(time.time())}",
            agent_id=metrics.agent_id,
            severity='critical',
            message=f"Agent {metrics.agent_id} is in critical state: CPU={metrics.cpu_usage:.1f}%, Memory={metrics.memory_usage:.1f}%, Errors={metrics.error_count}",
            timestamp=datetime.now()
        )
        
        self._create_alert(alert)
        
        # Attempt automatic recovery
        recovery_config = self.config.get('global', {}).get('error_handling', {})
        if recovery_config.get('retry_attempts', 0) > 0:
            await self.recovery_manager.execute_recovery(metrics.agent_id, 'free_memory')
            
    async def _handle_warning_status(self, metrics: HealthMetrics):
        """Handle warning health status"""
        alert = HealthAlert(
            alert_id=f"{metrics.agent_id}_warning_{int(time.time())}",
            agent_id=metrics.agent_id,
            severity='warning',
            message=f"Agent {metrics.agent_id} performance degraded: CPU={metrics.cpu_usage:.1f}%, Memory={metrics.memory_usage:.1f}%, Response={metrics.response_time:.0f}ms",
            timestamp=datetime.now()
        )
        
        self._create_alert(alert)
        
    async def _handle_offline_status(self, metrics: HealthMetrics):
        """Handle offline agent status"""
        alert = HealthAlert(
            alert_id=f"{metrics.agent_id}_offline_{int(time.time())}",
            agent_id=metrics.agent_id,
            severity='critical',
            message=f"Agent {metrics.agent_id} is offline and not responding",
            timestamp=datetime.now()
        )
        
        self._create_alert(alert)
        
        # Attempt to restart agent
        await self.recovery_manager.execute_recovery(metrics.agent_id, 'restart_agent')
        
    async def _run_predictive_analysis(self, agent_id: str, current_metrics: HealthMetrics):
        """Run predictive analysis for potential issues"""
        # Get historical data
        historical_data = self._get_historical_metrics(agent_id, hours=24)
        
        # Calculate failure risk
        failure_risk = self.predictive_analyzer.analyze_failure_risk(current_metrics, historical_data)
        
        if failure_risk > 0.7:  # High risk threshold
            alert = HealthAlert(
                alert_id=f"{agent_id}_prediction_{int(time.time())}",
                agent_id=agent_id,
                severity='warning',
                message=f"High failure risk detected for agent {agent_id}: {failure_risk:.2f}",
                timestamp=datetime.now()
            )
            self._create_alert(alert)
            
        # Predict failure time
        predicted_failure = self.predictive_analyzer.predict_failure_time(agent_id, historical_data)
        if predicted_failure and predicted_failure < datetime.now() + timedelta(hours=2):
            alert = HealthAlert(
                alert_id=f"{agent_id}_failure_prediction_{int(time.time())}",
                agent_id=agent_id,
                severity='critical',
                message=f"Agent {agent_id} predicted to fail around {predicted_failure.strftime('%Y-%m-%d %H:%M')}",
                timestamp=datetime.now()
            )
            self._create_alert(alert)
            
    def _get_historical_metrics(self, agent_id: str, hours: int = 24) -> List[HealthMetrics]:
        """Get historical metrics for an agent"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM health_metrics 
                    WHERE agent_id = ? AND timestamp > datetime('now', '-{} hours')
                    ORDER BY timestamp DESC
                    LIMIT 100
                '''.format(hours), (agent_id,))
                
                results = cursor.fetchall()
                
                metrics_list = []
                for row in results:
                    metrics = HealthMetrics(
                        agent_id=row[1],
                        timestamp=datetime.fromisoformat(row[2]),
                        cpu_usage=row[3],
                        memory_usage=row[4],
                        execution_time=row[5],
                        error_count=row[6],
                        success_count=row[7],
                        last_execution=datetime.fromisoformat(row[8]),
                        status=row[9],
                        response_time=row[10],
                        resource_efficiency=row[11]
                    )
                    metrics_list.append(metrics)
                    
                return metrics_list
                
        except Exception as e:
            logger.error(f"Error getting historical metrics: {e}")
            return []
            
    def _create_alert(self, alert: HealthAlert):
        """Create and store an alert"""
        try:
            self.alerts.append(alert)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO health_alerts 
                    (alert_id, agent_id, severity, message, timestamp, resolved)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    alert.alert_id,
                    alert.agent_id,
                    alert.severity,
                    alert.message,
                    alert.timestamp.isoformat(),
                    alert.resolved
                ))
                conn.commit()
                
            logger.warning(f"Alert created: {alert.message}")
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            
    def get_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """Get current status for an agent"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM health_metrics 
                    WHERE agent_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''', (agent_id,))
                
                result = cursor.fetchone()
                if result:
                    return {
                        'agent_id': result[1],
                        'status': result[9],
                        'cpu_usage': result[3],
                        'memory_usage': result[4],
                        'response_time': result[10],
                        'resource_efficiency': result[11],
                        'last_check': result[2]
                    }
                else:
                    return {'agent_id': agent_id, 'status': 'unknown'}
                    
        except Exception as e:
            logger.error(f"Error getting agent status: {e}")
            return {'agent_id': agent_id, 'status': 'error'}
            
    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get overall system health summary"""
        try:
            agents = self.config.get('agents', {})
            summary = {
                'total_agents': len(agents),
                'healthy_agents': 0,
                'warning_agents': 0,
                'critical_agents': 0,
                'offline_agents': 0,
                'average_response_time': 0.0,
                'average_resource_efficiency': 0.0,
                'active_alerts': len([a for a in self.alerts if not a.resolved])
            }
            
            response_times = []
            efficiencies = []
            
            for agent_id in agents.keys():
                status = self.get_agent_status(agent_id)
                
                if status['status'] == 'healthy':
                    summary['healthy_agents'] += 1
                elif status['status'] == 'warning':
                    summary['warning_agents'] += 1
                elif status['status'] == 'critical':
                    summary['critical_agents'] += 1
                else:
                    summary['offline_agents'] += 1
                    
                if 'response_time' in status and status['response_time'] != float('inf'):
                    response_times.append(status['response_time'])
                    
                if 'resource_efficiency' in status:
                    efficiencies.append(status['resource_efficiency'])
                    
            if response_times:
                summary['average_response_time'] = statistics.mean(response_times)
                
            if efficiencies:
                summary['average_resource_efficiency'] = statistics.mean(efficiencies)
                
            return summary
            
        except Exception as e:
            logger.error(f"Error getting system health summary: {e}")
            return {}

def main():
    """Main entry point for health monitoring system"""
    monitor = AgentHealthMonitor()
    monitor.start_monitoring()
    
    try:
        # Keep the monitoring running
        while True:
            time.sleep(60)
            # Print periodic summary
            summary = monitor.get_system_health_summary()
            logger.info(f"System Health: {summary['healthy_agents']}/{summary['total_agents']} agents healthy, "
                       f"{summary['active_alerts']} active alerts, "
                       f"avg response: {summary['average_response_time']:.0f}ms")
            
    except KeyboardInterrupt:
        logger.info("Shutting down health monitor...")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
