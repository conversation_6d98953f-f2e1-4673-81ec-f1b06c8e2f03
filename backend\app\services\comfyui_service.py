import httpx
import asyncio
import json
import websockets
import uuid
from typing import Dict, List, Optional, Any
import logging
import traceback
from pathlib import Path

from app.core.config import settings

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComfyUIService:
    def __init__(self):
        self.base_url = settings.COMFYUI_API_URL
        self.ws_url = f"ws://{settings.COMFYUI_HOST}:{settings.COMFYUI_PORT}/ws"
        self.client_id = str(uuid.uuid4())
        
    async def check_connection(self) -> bool:
        """Check if ComfyUI is running and accessible"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/system_stats")
                return response.status_code == 200
        except Exception as e:
            logger.error(f"ComfyUI connection check failed: {e}")
            return False
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/queue")
                if response.status_code == 200:
                    return response.json()
                return {"queue_running": [], "queue_pending": []}
        except Exception as e:
            logger.error(f"Failed to get queue status: {e}")
            return {"queue_running": [], "queue_pending": []}
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get ComfyUI system statistics"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/system_stats")
                if response.status_code == 200:
                    return response.json()
                return {}
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            return {}
    
    async def submit_workflow(self, workflow: Dict[str, Any]) -> str:
        """Submit a workflow to ComfyUI"""
        try:
            logger.info("Submitting workflow to ComfyUI...")
            logger.debug(f"Workflow structure: {json.dumps(workflow, indent=2)}")
            
            prompt_id = str(uuid.uuid4())
            payload = {
                "prompt": workflow,
                "client_id": self.client_id
            }
            
            logger.info(f"Sending request to {self.base_url}/prompt with client_id: {self.client_id}")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/prompt",
                    json=payload
                )
                
                logger.info(f"ComfyUI response status: {response.status_code}")
                logger.debug(f"ComfyUI response text: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    actual_prompt_id = result.get("prompt_id", prompt_id)
                    logger.info(f"Workflow submitted successfully with prompt_id: {actual_prompt_id}")
                    return actual_prompt_id
                else:
                    error_msg = f"ComfyUI API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                    
        except httpx.TimeoutException as e:
            error_msg = f"Timeout connecting to ComfyUI: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except httpx.ConnectError as e:
            error_msg = f"Connection error to ComfyUI: {str(e)} - Is ComfyUI running on {self.base_url}?"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            logger.error(f"Error submitting workflow: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise
    
    async def get_workflow_status(self, prompt_id: str) -> Dict[str, Any]:
        """Get status of a specific workflow with enhanced status parsing"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # First check if ComfyUI is running
                try:
                    health_response = await client.get(f"{self.base_url}/system_stats")
                    if health_response.status_code != 200:
                        return {"status": "error", "error": "ComfyUI not responding"}
                except:
                    return {"status": "error", "error": "ComfyUI not available"}

                # Get workflow history
                response = await client.get(f"{self.base_url}/history/{prompt_id}")
                if response.status_code == 200:
                    history_data = response.json()

                    # Parse ComfyUI history to determine status
                    if prompt_id in history_data:
                        workflow_data = history_data[prompt_id]

                        # Check if workflow completed successfully
                        if "outputs" in workflow_data and workflow_data["outputs"]:
                            return {"status": "completed", "data": workflow_data}

                        # Check if workflow failed
                        if "status" in workflow_data:
                            if workflow_data["status"].get("completed", False):
                                return {"status": "completed", "data": workflow_data}
                            elif "error" in workflow_data["status"]:
                                return {"status": "failed", "error": workflow_data["status"]["error"]}

                        # Still processing
                        return {"status": "processing", "data": workflow_data}
                    else:
                        # Check queue status to get more info
                        queue_response = await client.get(f"{self.base_url}/queue")
                        if queue_response.status_code == 200:
                            queue_data = queue_response.json()

                            # Check if prompt is currently running
                            for item in queue_data.get("queue_running", []):
                                if len(item) >= 2 and item[1] == prompt_id:
                                    return {"status": "processing", "queue_position": 0}

                            # Check if prompt is in pending queue
                            queue_pending = queue_data.get("queue_pending", [])
                            for i, item in enumerate(queue_pending):
                                if len(item) >= 2 and item[1] == prompt_id:
                                    return {"status": "queued", "queue_position": i + 1}

                        # Not found in history or queue, might be processing
                        return {"status": "processing"}

                        return {"status": "unknown", "error": "Prompt not found in history or queue"}

                return {"status": "error", "error": f"HTTP {response.status_code}"}

        except Exception as e:
            logger.error(f"Failed to get workflow status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_generated_images(self, prompt_id: str) -> List[Dict[str, Any]]:
        """Get generated images for a workflow"""
        try:
            # Get the full history data
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/history/{prompt_id}")
                if response.status_code != 200:
                    logger.error(f"Failed to get history for {prompt_id}: HTTP {response.status_code}")
                    return []
                
                history_data = response.json()
                images = []
                
                if prompt_id in history_data:
                    workflow_data = history_data[prompt_id]
                    outputs = workflow_data.get("outputs", {})
                    
                    logger.info(f"Found outputs for {prompt_id}: {list(outputs.keys())}")
                    
                    for node_id, node_output in outputs.items():
                        if "images" in node_output:
                            for image_info in node_output["images"]:
                                logger.info(f"Found image: {image_info}")
                                images.append({
                                    "filename": image_info["filename"],
                                    "subfolder": image_info.get("subfolder", ""),
                                    "type": image_info.get("type", "output"),
                                    "node_id": node_id
                                })
                else:
                    logger.error(f"Prompt ID {prompt_id} not found in history")
            
            return images
        except Exception as e:
            logger.error(f"Failed to get generated images: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return []
    
    async def download_image(self, filename: str, subfolder: str = "", type: str = "output") -> bytes:
        """Download an image from ComfyUI"""
        try:
            params = {
                "filename": filename,
                "type": type
            }
            if subfolder:
                params["subfolder"] = subfolder
                
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/view", params=params)
                if response.status_code == 200:
                    return response.content
                else:
                    raise Exception(f"Failed to download image: {response.status_code}")
        except Exception as e:
            logger.error(f"Error downloading image: {e}")
            raise
    
    def _detect_model_architecture(self, model_name: str) -> str:
        """Detect the model architecture based on filename patterns"""
        model_lower = model_name.lower()
        
        # HiDream models (need specialized workflow)
        hidream_patterns = [
            'hidream', 'hi-dream', 'hidream_i1', 'hidream-i1'
        ]
        if any(pattern in model_lower for pattern in hidream_patterns):
            return 'hidream'
        
        # Flux models (need specific workflow)
        flux_patterns = [
            'flux1-dev', 'flux1-schnell', 'flux1-kontext', 'fluxmania', 
            'fluxartfusion', 'fluxfill', 'pixelwave_flux'
        ]
        if any(pattern in model_lower for pattern in flux_patterns):
            return 'flux'
        
        # SDXL models (1024x1024 native, need SDXL workflow)
        sdxl_patterns = [
            'sdxl', 'xl', 'juggernaut', 'realvis', 'dreamshaper', 
            'proteus', 'nightvision', 'copax', 'animagine'
        ]
        if any(pattern in model_lower for pattern in sdxl_patterns):
            return 'sdxl'
        
        # SD 1.5 models (512x512 native)
        sd15_patterns = [
            'sd15', 'v1-5', 'stable-diffusion-v1', 'deliberate', 
            'realistic', 'anything', 'chillout', 'perfectworld'
        ]
        if any(pattern in model_lower for pattern in sd15_patterns):
            return 'sd15'
        
        # SD 2.1 models (768x768 native, different text encoder)
        sd21_patterns = ['sd21', 'v2-1', 'stable-diffusion-2']
        if any(pattern in model_lower for pattern in sd21_patterns):
            return 'sd21'
        
        # Default to SDXL for unknown models (most compatible)
        return 'sdxl'
    
    def _get_required_dependencies(self, architecture: str) -> Dict[str, List[str]]:
        """Get required dependencies for each architecture"""
        dependencies = {
            'hidream': {
                'vae': ['ae.safetensors'],
                'clip': ['clip_l_hidream.safetensors', 'clip_g_hidream.safetensors', 't5xxl_fp8_e4m3fn_scaled.safetensors', 'llama_3.1_8b_instruct_fp8_scaled.safetensors'],
                'unet': []  # HiDream models include UNet
            },
            'flux': {
                'vae': ['Flux_vae.safetensors', 'ae.safetensors'],
                'clip': ['t5xxl_fp8_e4m3fn.safetensors', 'clip_l.safetensors'],
                'unet': []  # Flux models include UNet
            },
            'sdxl': {
                'vae': ['sdxl_vae.safetensors', 'vae-ft-mse-840000-ema-pruned.safetensors'],
                'clip': ['clip_g.safetensors', 'clip_l.safetensors'],
                'unet': []  # SDXL models include UNet
            },
            'sd15': {
                'vae': ['vae-ft-mse-840000-ema-pruned.safetensors', 'sd15_vae.safetensors'],
                'clip': ['clip_l.safetensors'],
                'unet': []  # SD1.5 models include UNet
            },
            'sd21': {
                'vae': ['sd21_vae.safetensors', 'vae-ft-mse-840000-ema-pruned.safetensors'],
                'clip': ['open_clip_pytorch_model.bin'],
                'unet': []  # SD2.1 models include UNet
            }
        }
        return dependencies.get(architecture, dependencies['sdxl'])
    
    def _validate_model_dependencies(self, model_name: str, architecture: str) -> Dict[str, Any]:
        """Validate that a model has all required dependencies"""
        validation = {
            'valid': True,
            'missing_dependencies': [],
            'warnings': [],
            'architecture': architecture
        }
        
        try:
            required_deps = self._get_required_dependencies(architecture)
            
            # Check VAE dependencies - be more flexible with alternatives
            vae_path = Path(settings.VAE_PATH)
            available_vaes = []
            if vae_path.exists():
                available_vaes = [f.name for f in vae_path.iterdir() 
                                if f.suffix.lower() in ['.safetensors', '.pth']]
            
            vae_found = False
            if required_deps['vae']:
                # Check if any of the required VAEs are available
                for required_vae in required_deps['vae']:
                    if required_vae in available_vaes:
                        vae_found = True
                        break
                
                # For some architectures, be more flexible with VAE matching
                if not vae_found:
                    # Check for generic VAE files that might work
                    generic_vaes = [vae for vae in available_vaes if 
                                  any(keyword in vae.lower() for keyword in ['vae', 'ae']) and
                                  vae.endswith('.safetensors')]
                    if generic_vaes:
                        vae_found = True
                        validation['warnings'].append(f"Using alternative VAE: {generic_vaes[0]}")
                
                if not vae_found:
                    validation['warnings'].append(f"Recommended VAE missing: {required_deps['vae'][0]} (but model may still work)")
                    # Don't mark as invalid for missing VAE - many models have built-in VAE
            
            # Check CLIP dependencies - be more flexible
            clip_path = Path(settings.CLIP_PATH)
            available_clips = []
            if clip_path.exists():
                available_clips = [f.name for f in clip_path.iterdir() 
                                 if f.suffix.lower() in ['.safetensors', '.bin']]
            
            # Architecture-specific validations
            if architecture == 'flux':
                # Flux models need T5 text encoder - this is critical
                t5_found = any('t5xxl' in clip.lower() for clip in available_clips)
                if not t5_found:
                    validation['missing_dependencies'].append("T5 Text Encoder: t5xxl_fp8_e4m3fn.safetensors")
                    validation['valid'] = False
                else:
                    # Check for CLIP-L as well
                    clip_l_found = any('clip_l' in clip.lower() for clip in available_clips)
                    if not clip_l_found:
                        validation['warnings'].append("CLIP-L model recommended for better results")
            
            elif architecture == 'hidream':
                # HiDream needs specific CLIP models
                required_clips = ['clip_l_hidream.safetensors', 'clip_g_hidream.safetensors', 
                                't5xxl_fp8_e4m3fn_scaled.safetensors', 'llama_3.1_8b_instruct_fp8_scaled.safetensors']
                missing_clips = [clip for clip in required_clips if clip not in available_clips]
                if missing_clips:
                    validation['missing_dependencies'].extend([f"CLIP: {clip}" for clip in missing_clips])
                    validation['valid'] = False
            
            else:
                # For other architectures, just warn about missing recommended CLIPs
                for required_clip in required_deps['clip']:
                    if required_clip not in available_clips:
                        # Check for similar CLIP files
                        similar_clips = [clip for clip in available_clips if 
                                       any(keyword in clip.lower() for keyword in ['clip', 'text'])]
                        if similar_clips:
                            validation['warnings'].append(f"Using alternative CLIP: {similar_clips[0]}")
                        else:
                            validation['warnings'].append(f"Recommended CLIP model missing: {required_clip}")
        
        except Exception as e:
            logger.error(f"Error validating dependencies for {model_name}: {e}")
            validation['warnings'].append(f"Could not validate dependencies: {str(e)}")
            # Don't mark as invalid due to validation errors
        
        return validation

    async def scan_models(self) -> Dict[str, Any]:
        """Scan for available models and validate their dependencies"""
        from app.utils.path_resolver import model_path_resolver

        models = {
            "checkpoints": [],
            "validated_checkpoints": [],
            "loras": [],
            "controlnet": [],
            "upscale_models": [],
            "vae": [],
            "validation_report": {},
            "path_info": {}
        }

        try:
            # Get path resolution info for debugging
            path_info = model_path_resolver.get_path_info()
            models["path_info"] = path_info
            logger.info(f"Model path resolution: {path_info}")

            # Check if we have a working model base path
            working_base_path = model_path_resolver.get_working_model_base_path()
            if not working_base_path:
                logger.error("No working model base path found!")
                return models

            logger.info(f"Using model base path: {working_base_path}")

            # Scan all diffusion model folders with validation using resolved paths
            model_folders = [
                ("checkpoints", model_path_resolver.resolve_model_directory("checkpoints"), "checkpoints"),
                ("unet", model_path_resolver.resolve_model_directory("unet"), "unet"),
                ("diffusion_models", model_path_resolver.resolve_model_directory("diffusion_models"), "diffusion_models")
            ]

            all_diffusion_models = []

            for folder_type, folder_path, folder_name in model_folders:
                if not folder_path:
                    logger.warning(f"Could not resolve path for {folder_name}")
                    continue

                folder_path_obj = Path(folder_path)
                logger.info(f"Scanning {folder_name} folder: {folder_path}")

                if folder_path_obj.exists():
                    # Use path resolver to scan files (excludes placeholder files)
                    model_files = model_path_resolver.scan_model_files(folder_name, ['.safetensors', '.ckpt', '.pth'])
                    folder_models = [filename for filename, _ in model_files]

                    logger.info(f"Found {len(folder_models)} models in {folder_name}: {folder_models}")
                    all_diffusion_models.extend(folder_models)

                    # Validate each model in this folder
                    for model_name in folder_models:
                        architecture = self._detect_model_architecture(model_name)
                        validation = self._validate_model_dependencies(model_name, architecture)

                        models["validation_report"][model_name] = {
                            **validation,
                            'folder_type': folder_type,
                            'folder_path': folder_name,
                            'full_path': str(folder_path_obj / model_name)
                        }

                        # Only add to validated list if all dependencies are met
                        if validation['valid']:
                            models["validated_checkpoints"].append({
                                'name': model_name,
                                'architecture': architecture,
                                'path': str(folder_path_obj / model_name),
                                'folder_type': folder_type,
                                'folder_name': folder_name
                            })
                        else:
                            logger.warning(f"Model {model_name} in {folder_name} has missing dependencies: {validation['missing_dependencies']}")
                else:
                    logger.warning(f"Model folder does not exist: {folder_path}")

            models["checkpoints"] = all_diffusion_models
            logger.info(f"Total diffusion models found across all folders: {len(all_diffusion_models)}")
            
            # Scan LoRAs using path resolver
            lora_files = model_path_resolver.scan_model_files("loras", ['.safetensors', '.ckpt'])
            models["loras"] = [filename for filename, _ in lora_files]
            logger.info(f"Found {len(models['loras'])} LoRA models")

            # Scan ControlNet models using path resolver
            controlnet_files = model_path_resolver.scan_model_files("controlnet", ['.safetensors', '.pth'])
            models["controlnet"] = [filename for filename, _ in controlnet_files]
            logger.info(f"Found {len(models['controlnet'])} ControlNet models")

            # Scan upscale models using path resolver
            upscale_files = model_path_resolver.scan_model_files("upscale_models", ['.pth', '.safetensors'])
            models["upscale_models"] = [filename for filename, _ in upscale_files]
            logger.info(f"Found {len(models['upscale_models'])} upscale models")

            # Scan VAE models using path resolver
            vae_files = model_path_resolver.scan_model_files("vae", ['.safetensors', '.pth'])
            models["vae"] = [filename for filename, _ in vae_files]
            logger.info(f"Found {len(models['vae'])} VAE models")
            
            # Log summary
            total_checkpoints = len(models["checkpoints"])
            valid_checkpoints = len(models["validated_checkpoints"])
            logger.info(f"Model scan complete: {valid_checkpoints}/{total_checkpoints} checkpoints validated")
                
        except Exception as e:
            logger.error(f"Error scanning models: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
        
        return models
    
    def build_text_to_image_workflow(self, 
                                   prompt: str,
                                   negative_prompt: str = "",
                                   model: str = "flux1-kontext-dev.safetensors",
                                   width: int = 1024,
                                   height: int = 1024,
                                   steps: int = 20,
                                   cfg: float = 7.0,
                                   seed: int = -1,
                                   model_folder_type: str = "checkpoints",
                                   use_preloaded_model: bool = False) -> Dict[str, Any]:
        """Build a text-to-image workflow for ComfyUI based on model architecture and folder type"""
        
        if seed == -1:
            seed = int(asyncio.get_event_loop().time() * 1000000) % 2147483647
        
        # Detect model architecture
        architecture = self._detect_model_architecture(model)
        preload_info = "with preloaded model" if use_preloaded_model else "loading model on demand"
        logger.info(f"Building {architecture} workflow for model: {model} (folder: {model_folder_type}) {preload_info}")
        
        # Get architecture-specific parameters
        arch_params = self._get_architecture_parameters(architecture, width, height, steps, cfg)
        
        if architecture == 'hidream':
            return self._build_hidream_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
        elif architecture == 'flux':
            return self._build_flux_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
        elif architecture == 'sdxl':
            return self._build_sdxl_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
        elif architecture == 'sd15':
            return self._build_sd15_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
        elif architecture == 'sd21':
            return self._build_sd21_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
        else:
            # Default to SDXL workflow for unknown architectures
            return self._build_sdxl_workflow(prompt, negative_prompt, model, arch_params, seed, model_folder_type)
    
    def _get_architecture_parameters(self, architecture: str, width: int, height: int, steps: int, cfg: float) -> Dict[str, Any]:
        """Get optimized parameters for each architecture"""
        params = {
            'hidream': {
                'width': max(1024, width), 'height': max(1024, height),
                'steps': 28, 'cfg': 1.0,  # HiDream dev defaults
                'sampler': 'dpmpp_2m', 'scheduler': 'normal'
            },
            'flux': {
                'width': max(512, width), 'height': max(512, height),
                'steps': min(max(15, steps), 28), 'cfg': max(1.0, min(cfg, 6.0)),  # OPTIMIZED: Reduced max steps and CFG
                'sampler': 'euler', 'scheduler': 'normal'  # OPTIMIZED: Better scheduler for FLUX
            },
            'sdxl': {
                'width': max(512, width), 'height': max(512, height),
                'steps': max(20, steps), 'cfg': max(1.0, min(cfg, 20.0)),
                'sampler': 'dpmpp_2m', 'scheduler': 'karras'
            },
            'sd15': {
                'width': max(512, width), 'height': max(512, height),
                'steps': max(20, steps), 'cfg': max(1.0, min(cfg, 20.0)),
                'sampler': 'dpmpp_2m', 'scheduler': 'karras'
            },
            'sd21': {
                'width': max(512, width), 'height': max(512, height),
                'steps': max(20, steps), 'cfg': max(1.0, min(cfg, 20.0)),
                'sampler': 'dpmpp_2m', 'scheduler': 'karras'
            }
        }
        return params.get(architecture, params['sdxl'])
    
    def _build_flux_workflow(self, prompt: str, negative_prompt: str, model: str, params: Dict[str, Any], seed: int, model_folder_type: str = "checkpoints") -> Dict[str, Any]:
        """Build optimized Flux-specific workflow for better performance and compatibility"""
        
        # OPTIMIZATION: Use fewer steps for faster generation while maintaining quality
        optimized_steps = min(params['steps'], 28)  # Cap at 28 steps for FLUX
        
        # OPTIMIZATION: Use more efficient CFG scale for FLUX models
        optimized_cfg = min(params['cfg'], 6.0)  # FLUX works better with lower CFG
        
        # OPTIMIZATION: Use best samplers for FLUX architecture
        flux_sampler = "euler" if params['sampler'] in ["ddim", "ddpm"] else params['sampler']
        flux_scheduler = "normal" if params['scheduler'] in ["karras", "exponential"] else params['scheduler']
        
        logger.info(f"FLUX Optimization: steps={optimized_steps}, cfg={optimized_cfg}, sampler={flux_sampler}")
        
        return {
            "1": {
                "inputs": {
                    "seed": seed,
                    "steps": optimized_steps,
                    "cfg": optimized_cfg,
                    "sampler_name": flux_sampler,
                    "scheduler": flux_scheduler,
                    "denoise": 1.0,
                    "model": ["2", 0],
                    "positive": ["4", 0],
                    "negative": ["5", 0],
                    "latent_image": ["3", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "Optimized Flux KSampler"}
            },
            "2": {
                "inputs": {
                    "ckpt_name": model
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Flux Checkpoint"}
            },
            "3": {
                "inputs": {
                    "width": params['width'],
                    "height": params['height'],
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "4": {
                "inputs": {
                    "text": prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "5": {
                "inputs": {
                    "text": negative_prompt if negative_prompt else "",
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "6": {
                "inputs": {
                    "samples": ["1", 0],
                    "vae": ["2", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "7": {
                "inputs": {
                    "images": ["6", 0],
                    "filename_prefix": "Flux_Optimized_"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }
    
    def _build_sdxl_workflow(self, prompt: str, negative_prompt: str, model: str, params: Dict[str, Any], seed: int, model_folder_type: str = None) -> Dict[str, Any]:
        """Build SDXL-specific workflow"""
        return {
            "1": {
                "inputs": {
                    "seed": seed,
                    "steps": params['steps'],
                    "cfg": params['cfg'],
                    "sampler_name": params['sampler'],
                    "scheduler": params['scheduler'],
                    "denoise": 1.0,
                    "model": ["2", 0],
                    "positive": ["4", 0],
                    "negative": ["5", 0],
                    "latent_image": ["3", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "SDXL KSampler"}
            },
            "2": {
                "inputs": {
                    "ckpt_name": model
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load SDXL Checkpoint"}
            },
            "3": {
                "inputs": {
                    "width": params['width'],
                    "height": params['height'],
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "4": {
                "inputs": {
                    "text": prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "5": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "6": {
                "inputs": {
                    "samples": ["1", 0],
                    "vae": ["2", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "7": {
                "inputs": {
                    "images": ["6", 0],
                    "filename_prefix": "SDXL_"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }
    
    def _build_sd15_workflow(self, prompt: str, negative_prompt: str, model: str, params: Dict[str, Any], seed: int, model_folder_type: str = None) -> Dict[str, Any]:
        """Build SD1.5-specific workflow"""
        return {
            "1": {
                "inputs": {
                    "seed": seed,
                    "steps": params['steps'],
                    "cfg": params['cfg'],
                    "sampler_name": params['sampler'],
                    "scheduler": params['scheduler'],
                    "denoise": 1.0,
                    "model": ["2", 0],
                    "positive": ["4", 0],
                    "negative": ["5", 0],
                    "latent_image": ["3", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "SD1.5 KSampler"}
            },
            "2": {
                "inputs": {
                    "ckpt_name": model
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load SD1.5 Checkpoint"}
            },
            "3": {
                "inputs": {
                    "width": params['width'],
                    "height": params['height'],
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "4": {
                "inputs": {
                    "text": prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "5": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "6": {
                "inputs": {
                    "samples": ["1", 0],
                    "vae": ["2", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "7": {
                "inputs": {
                    "images": ["6", 0],
                    "filename_prefix": "SD15_"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }
    
    def _build_sd21_workflow(self, prompt: str, negative_prompt: str, model: str, params: Dict[str, Any], seed: int, model_folder_type: str = None) -> Dict[str, Any]:
        """Build SD2.1-specific workflow"""
        return {
            "1": {
                "inputs": {
                    "seed": seed,
                    "steps": params['steps'],
                    "cfg": params['cfg'],
                    "sampler_name": params['sampler'],
                    "scheduler": params['scheduler'],
                    "denoise": 1.0,
                    "model": ["2", 0],
                    "positive": ["4", 0],
                    "negative": ["5", 0],
                    "latent_image": ["3", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "SD2.1 KSampler"}
            },
            "2": {
                "inputs": {
                    "ckpt_name": model
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load SD2.1 Checkpoint"}
            },
            "3": {
                "inputs": {
                    "width": params['width'],
                    "height": params['height'],
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "4": {
                "inputs": {
                    "text": prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "5": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["2", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "6": {
                "inputs": {
                    "samples": ["1", 0],
                    "vae": ["2", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "7": {
                "inputs": {
                    "images": ["6", 0],
                    "filename_prefix": "SD21_"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }
    
    def _build_hidream_workflow(self, prompt: str, negative_prompt: str, model: str, params: Dict[str, Any], seed: int, model_folder_type: str = "diffusion_models") -> Dict[str, Any]:
        """Build HiDream-specific workflow with QuadrupleCLIPLoader and ModelSamplingSD3"""
        
        # Determine shift value based on model variant
        model_lower = model.lower()
        if 'full' in model_lower:
            shift_value = 3.0
            cfg_value = params.get('cfg', 5.0)
            steps_value = params.get('steps', 50)
        elif 'fast' in model_lower:
            shift_value = 3.0
            cfg_value = 1.0
            steps_value = params.get('steps', 16)
        else:  # dev version (default)
            shift_value = 6.0
            cfg_value = 1.0
            steps_value = params.get('steps', 28)
        
        return {
            "1": {
                "inputs": {
                    "seed": seed,
                    "steps": steps_value,
                    "cfg": cfg_value,
                    "sampler_name": params['sampler'],
                    "scheduler": params['scheduler'],
                    "denoise": 1.0,
                    "model": ["3", 0],
                    "positive": ["4", 0],
                    "negative": ["5", 0] if 'full' in model_lower else ["4", 0],  # Use positive for dev/fast
                    "latent_image": ["6", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "HiDream KSampler"}
            },
            "2": {
                "inputs": {
                    "unet_name": model
                },
                "class_type": "UNETLoader",
                "_meta": {"title": "Load HiDream Diffusion Model"}
            },
            "3": {
                "inputs": {
                    "model": ["2", 0],
                    "shift": shift_value
                },
                "class_type": "ModelSamplingSD3",
                "_meta": {"title": "HiDream Model Sampling"}
            },
            "4": {
                "inputs": {
                    "text": prompt,
                    "clip": ["11", 0]  # From QuadrupleCLIPLoader
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "5": {
                "inputs": {
                    "text": negative_prompt if negative_prompt and 'full' in model_lower else "",
                    "clip": ["11", 0]  # From QuadrupleCLIPLoader
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "6": {
                "inputs": {
                    "width": params['width'],
                    "height": params['height'],
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "7": {
                "inputs": {
                    "samples": ["1", 0],
                    "vae": ["10", 0]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "8": {
                "inputs": {
                    "images": ["7", 0],
                    "filename_prefix": "HiDream_"
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            },
            "9": {
                "inputs": {
                    "vae_name": "HiDream_vae.safetensors"
                },
                "class_type": "VAELoader",
                "_meta": {"title": "Load HiDream VAE"}
            },
            "10": {
                "inputs": {},
                "class_type": "VAELoader",
                "_meta": {"title": "VAE Loader"}
            },
            "11": {
                "inputs": {
                    "clip_name1": "clip_l_hidream.safetensors",
                    "clip_name2": "clip_g_hidream.safetensors", 
                    "clip_name3": "t5xxl_fp8_e4m3fn_scaled.safetensors",
                    "clip_name4": "llama_3.1_8b_instruct_fp8_scaled.safetensors"
                },
                "class_type": "QuadrupleCLIPLoader",
                "_meta": {"title": "Load HiDream Text Encoders"}
            }
        }
    
    async def cancel_generation(self, prompt_id: str) -> bool:
        """Cancel a specific generation by prompt_id"""
        try:
            logger.info(f"Attempting to cancel generation with prompt_id: {prompt_id}")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Send interrupt signal to ComfyUI
                response = await client.post(f"{self.base_url}/interrupt")
                
                if response.status_code == 200:
                    logger.info(f"Successfully sent interrupt signal for prompt_id: {prompt_id}")
                    return True
                else:
                    logger.error(f"Failed to send interrupt signal: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error cancelling generation: {e}")
            return False
    
    async def clear_queue(self) -> bool:
        """Clear the ComfyUI queue"""
        try:
            logger.info("Clearing ComfyUI queue...")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Clear the queue
                payload = {"clear": True}
                response = await client.post(f"{self.base_url}/queue", json=payload)
                
                if response.status_code == 200:
                    logger.info("Successfully cleared ComfyUI queue")
                    return True
                else:
                    logger.error(f"Failed to clear queue: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error clearing queue: {e}")
            return False
    
    async def free_memory(self) -> bool:
        """Free ComfyUI GPU memory"""
        try:
            logger.info("Freeing ComfyUI memory...")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Free memory
                response = await client.post(f"{self.base_url}/free", json={})
                
                if response.status_code == 200:
                    logger.info("Successfully freed ComfyUI memory")
                    return True
                else:
                    logger.error(f"Failed to free memory: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error freeing memory: {e}")
            return False
