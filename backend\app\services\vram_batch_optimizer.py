"""
VRAM-Aware Batch Processing Optimizer
Advanced VRAM management and batch processing optimization for ComfyUI workflows
"""

import asyncio
import time
import psutil
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import sqlite3
import json

from app.utils.centralized_logger import get_logger, log_activity, log_error

logger = get_logger()

class VRAMStrategy(Enum):
    AGGRESSIVE = "aggressive"        # Maximize VRAM usage
    CONSERVATIVE = "conservative"    # Safe VRAM usage with buffer
    ADAPTIVE = "adaptive"           # Adapt based on current conditions
    STREAMING = "streaming"         # Stream batches for large jobs

class BatchingMode(Enum):
    FIXED = "fixed"                 # Fixed batch sizes
    DYNAMIC = "dynamic"             # Dynamic batch sizes based on VRAM
    PROGRESSIVE = "progressive"     # Progressive batch size increase
    MEMORY_AWARE = "memory_aware"   # Memory-constrained batching

@dataclass
class VRAMProfile:
    total_vram_mb: int
    available_vram_mb: int
    reserved_vram_mb: int           # Reserve for system/other processes
    max_batch_vram_mb: int          # Maximum VRAM per batch
    safety_buffer_mb: int           # Safety buffer to prevent OOM
    fragmentation_factor: float     # Account for memory fragmentation
    
    @property
    def usable_vram_mb(self) -> int:
        return max(0, self.available_vram_mb - self.reserved_vram_mb - self.safety_buffer_mb)

@dataclass
class BatchOptimizationPlan:
    original_batch_size: int
    optimized_batch_size: int
    total_batches: int
    estimated_vram_per_batch_mb: float
    estimated_total_time_ms: float
    batching_mode: BatchingMode
    vram_strategy: VRAMStrategy
    memory_checkpoints: List[int]   # Batch indices for memory cleanup
    optimization_notes: List[str]

class VRAMBatchOptimizer:
    """Advanced VRAM-aware batch processing optimizer with RTX 4070 Ti SUPER specialization"""
    
    def __init__(self, database_path: str = "data/vram_optimization.db"):
        self.database_path = database_path
        self.gpu_monitoring_enabled = self._check_gpu_monitoring()
        self.current_vram_profile: Optional[VRAMProfile] = None
        self._initialize_database()
        
        # RTX 4070 Ti SUPER specific configuration
        self.rtx_4070ti_super_config = {
            "total_vram_mb": 16384,      # 16GB
            "reserved_vram_mb": 1024,    # 1GB for system
            "safety_buffer_mb": 512,     # 512MB safety buffer
            "fragmentation_factor": 1.2,  # 20% fragmentation overhead
            "optimal_batch_ranges": {
                "flux": (1, 2),
                "sdxl": (1, 4),
                "sd15": (2, 8)
            }
        }
        
        log_activity("VRAM_OPTIMIZER", "VRAM batch optimizer initialized", {
            "gpu_monitoring": self.gpu_monitoring_enabled,
            "rtx_4070ti_super_optimized": True
        })

    def _check_gpu_monitoring(self) -> bool:
        """Check if GPU monitoring is available"""
        try:
            import pynvml
            pynvml.nvmlInit()
            return True
        except ImportError:
            log_activity("VRAM_OPTIMIZER", "GPU monitoring not available (pynvml not installed)")
            return False
        except Exception as e:
            log_activity("VRAM_OPTIMIZER", f"GPU monitoring not available: {str(e)}")
            return False

    def _initialize_database(self):
        """Initialize the VRAM optimization database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # VRAM usage history
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS vram_usage_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT NOT NULL,
                        model_type TEXT NOT NULL,
                        batch_size INTEGER NOT NULL,
                        resolution TEXT NOT NULL,
                        vram_used_mb INTEGER,
                        vram_peak_mb INTEGER,
                        success BOOLEAN,
                        oom_error BOOLEAN DEFAULT FALSE,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Batch optimization history
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS batch_optimization_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT NOT NULL,
                        original_batch_size INTEGER,
                        optimized_batch_size INTEGER,
                        total_batches INTEGER,
                        estimated_vram_mb REAL,
                        actual_vram_mb REAL,
                        batching_mode TEXT,
                        vram_strategy TEXT,
                        optimization_success BOOLEAN,
                        time_saved_percent REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # VRAM capacity benchmarks
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS vram_capacity_benchmarks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_type TEXT NOT NULL,
                        resolution TEXT NOT NULL,
                        max_safe_batch_size INTEGER,
                        vram_per_batch_mb REAL,
                        hardware_profile TEXT,
                        benchmark_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(model_type, resolution, hardware_profile)
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "database_init_failed", f"Failed to initialize database: {str(e)}", {}, e)
            raise

    async def get_current_vram_profile(self) -> VRAMProfile:
        """Get current VRAM profile with real-time data"""
        
        if self.gpu_monitoring_enabled:
            try:
                import pynvml
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                total_vram_mb = memory_info.total // (1024 * 1024)
                used_vram_mb = memory_info.used // (1024 * 1024)
                available_vram_mb = total_vram_mb - used_vram_mb
                
                # Use RTX 4070 Ti SUPER config if detected
                if total_vram_mb >= 15000:  # Likely RTX 4070 Ti SUPER
                    config = self.rtx_4070ti_super_config
                    profile = VRAMProfile(
                        total_vram_mb=total_vram_mb,
                        available_vram_mb=available_vram_mb,
                        reserved_vram_mb=config["reserved_vram_mb"],
                        max_batch_vram_mb=int((total_vram_mb - config["reserved_vram_mb"]) * 0.8),
                        safety_buffer_mb=config["safety_buffer_mb"],
                        fragmentation_factor=config["fragmentation_factor"]
                    )
                else:
                    # Generic profile for other GPUs
                    profile = VRAMProfile(
                        total_vram_mb=total_vram_mb,
                        available_vram_mb=available_vram_mb,
                        reserved_vram_mb=max(512, total_vram_mb // 16),  # 1/16th reserved
                        max_batch_vram_mb=int(total_vram_mb * 0.7),
                        safety_buffer_mb=max(256, total_vram_mb // 32),  # 1/32nd buffer
                        fragmentation_factor=1.3
                    )
                
                self.current_vram_profile = profile
                return profile
                
            except Exception as e:
                log_error("VRAM_OPTIMIZER", "vram_profile_failed", f"Failed to get VRAM profile: {str(e)}", {}, e)
        
        # Fallback to RTX 4070 Ti SUPER defaults
        config = self.rtx_4070ti_super_config
        fallback_profile = VRAMProfile(
            total_vram_mb=config["total_vram_mb"],
            available_vram_mb=config["total_vram_mb"] - 2048,  # Assume 2GB used
            reserved_vram_mb=config["reserved_vram_mb"],
            max_batch_vram_mb=12288,  # 12GB max batch
            safety_buffer_mb=config["safety_buffer_mb"],
            fragmentation_factor=config["fragmentation_factor"]
        )
        
        self.current_vram_profile = fallback_profile
        return fallback_profile

    def estimate_vram_usage(self, model_type: str, width: int, height: int, 
                           batch_size: int, steps: int = 25) -> float:
        """Estimate VRAM usage for given parameters"""
        
        # Base VRAM usage by model type (MB)
        base_vram = {
            "flux": 4096,    # 4GB base
            "sdxl": 3072,    # 3GB base  
            "sd15": 2048     # 2GB base
        }
        
        model_base = base_vram.get(model_type.lower(), 3072)
        
        # Resolution scaling factor
        base_pixels = 512 * 512
        current_pixels = width * height
        resolution_factor = current_pixels / base_pixels
        
        # Steps scaling (minimal impact on VRAM, more on compute time)
        steps_factor = 1.0 + (steps - 25) * 0.01  # 1% per additional step
        
        # Batch scaling (linear for most operations)
        batch_factor = batch_size
        
        # Calculate total VRAM usage
        total_vram = model_base * resolution_factor * steps_factor * batch_factor
        
        # Apply fragmentation factor
        if self.current_vram_profile:
            total_vram *= self.current_vram_profile.fragmentation_factor
        else:
            total_vram *= 1.2  # Default 20% overhead
        
        return total_vram

    async def optimize_batch_processing(self, generation_request: Dict[str, Any],
                                       strategy: VRAMStrategy = VRAMStrategy.ADAPTIVE) -> BatchOptimizationPlan:
        """Optimize batch processing for VRAM efficiency"""
        
        generation_id = generation_request.get("generation_id", "unknown")
        model_type = generation_request.get("model", "sdxl")
        width = generation_request.get("width", 512)
        height = generation_request.get("height", 512)
        steps = generation_request.get("steps", 25)
        original_batch_size = generation_request.get("batch_size", 1)
        
        log_activity("VRAM_OPTIMIZER", f"Optimizing batch processing for {generation_id}", {
            "model_type": model_type,
            "resolution": f"{width}x{height}",
            "original_batch": original_batch_size,
            "strategy": strategy.value
        })
        
        # Get current VRAM profile
        vram_profile = await self.get_current_vram_profile()
        
        # Estimate VRAM usage per batch item
        vram_per_item = self.estimate_vram_usage(model_type, width, height, 1, steps)
        
        # Get historical data for this configuration
        historical_data = await self._get_historical_batch_data(model_type, width, height)
        
        # Calculate optimal batch size based on strategy
        optimal_batch_size = await self._calculate_optimal_batch_size(
            vram_profile, vram_per_item, original_batch_size, strategy, historical_data
        )
        
        # Determine batching mode
        batching_mode = self._determine_batching_mode(optimal_batch_size, original_batch_size, strategy)
        
        # Calculate total batches needed
        total_batches = max(1, (original_batch_size + optimal_batch_size - 1) // optimal_batch_size)
        
        # Estimate performance impact
        estimated_vram_per_batch = vram_per_item * optimal_batch_size
        estimated_total_time = self._estimate_batch_processing_time(
            original_batch_size, optimal_batch_size, total_batches, vram_per_item
        )
        
        # Identify memory checkpoint locations
        memory_checkpoints = self._identify_memory_checkpoints(total_batches, strategy)
        
        # Generate optimization notes
        optimization_notes = self._generate_batch_optimization_notes(
            original_batch_size, optimal_batch_size, total_batches, 
            estimated_vram_per_batch, vram_profile
        )
        
        plan = BatchOptimizationPlan(
            original_batch_size=original_batch_size,
            optimized_batch_size=optimal_batch_size,
            total_batches=total_batches,
            estimated_vram_per_batch_mb=estimated_vram_per_batch,
            estimated_total_time_ms=estimated_total_time,
            batching_mode=batching_mode,
            vram_strategy=strategy,
            memory_checkpoints=memory_checkpoints,
            optimization_notes=optimization_notes
        )
        
        # Record optimization plan
        await self._record_batch_optimization(generation_id, plan)
        
        log_activity("VRAM_OPTIMIZER", f"Batch optimization completed for {generation_id}", {
            "original_batch": original_batch_size,
            "optimized_batch": optimal_batch_size,
            "total_batches": total_batches,
            "estimated_vram_mb": estimated_vram_per_batch
        })
        
        return plan

    async def _calculate_optimal_batch_size(self, vram_profile: VRAMProfile, 
                                          vram_per_item: float, original_batch_size: int,
                                          strategy: VRAMStrategy, 
                                          historical_data: Dict[str, Any]) -> int:
        """Calculate optimal batch size based on VRAM constraints and strategy"""
        
        usable_vram = vram_profile.usable_vram_mb
        
        # Start with maximum theoretical batch size
        max_theoretical_batch = int(usable_vram / vram_per_item)
        
        if strategy == VRAMStrategy.AGGRESSIVE:
            # Use 95% of usable VRAM
            target_vram = usable_vram * 0.95
            optimal_batch = int(target_vram / vram_per_item)
        
        elif strategy == VRAMStrategy.CONSERVATIVE:
            # Use 70% of usable VRAM
            target_vram = usable_vram * 0.70
            optimal_batch = int(target_vram / vram_per_item)
        
        elif strategy == VRAMStrategy.ADAPTIVE:
            # Adapt based on historical success rates
            if historical_data.get("max_safe_batch_size"):
                # Use historical data with some headroom
                historical_max = historical_data["max_safe_batch_size"]
                optimal_batch = min(historical_max, max_theoretical_batch)
            else:
                # Conservative approach without historical data
                target_vram = usable_vram * 0.75
                optimal_batch = int(target_vram / vram_per_item)
        
        elif strategy == VRAMStrategy.STREAMING:
            # Use smaller batches for streaming
            optimal_batch = min(2, max_theoretical_batch)
        
        else:
            # Default to conservative
            optimal_batch = int(usable_vram * 0.70 / vram_per_item)
        
        # Apply constraints
        optimal_batch = max(1, optimal_batch)  # At least 1
        optimal_batch = min(optimal_batch, original_batch_size)  # Don't exceed original
        
        # Apply model-specific constraints for RTX 4070 Ti SUPER
        if vram_profile.total_vram_mb >= 15000:  # RTX 4070 Ti SUPER
            model_type = "sdxl"  # Default assumption
            if "flux" in str(vram_per_item):
                model_type = "flux"
            elif vram_per_item < 2000:
                model_type = "sd15"
            
            min_batch, max_batch = self.rtx_4070ti_super_config["optimal_batch_ranges"].get(model_type, (1, 4))
            optimal_batch = max(min_batch, min(max_batch, optimal_batch))
        
        return optimal_batch

    def _determine_batching_mode(self, optimal_batch_size: int, original_batch_size: int,
                               strategy: VRAMStrategy) -> BatchingMode:
        """Determine the best batching mode for the optimization"""
        
        if optimal_batch_size == original_batch_size:
            return BatchingMode.FIXED
        elif strategy == VRAMStrategy.STREAMING:
            return BatchingMode.MEMORY_AWARE
        elif strategy == VRAMStrategy.ADAPTIVE:
            return BatchingMode.DYNAMIC
        elif original_batch_size > optimal_batch_size * 2:
            return BatchingMode.PROGRESSIVE  # Large reduction suggests progressive batching
        else:
            return BatchingMode.FIXED

    def _estimate_batch_processing_time(self, original_batch: int, optimal_batch: int,
                                      total_batches: int, vram_per_item: float) -> float:
        """Estimate total processing time for batch optimization"""
        
        # Base processing time per item (ms)
        base_time_per_item = 1000  # 1 second base
        
        # Adjust based on VRAM usage (higher VRAM = more processing)
        vram_factor = min(2.0, vram_per_item / 4096)  # Max 2x factor for high VRAM
        
        # Batch efficiency factor (larger batches are more efficient per item)
        batch_efficiency = min(1.5, optimal_batch / 2)  # Up to 1.5x efficiency
        
        # Overhead per batch (setup/teardown time)
        batch_overhead = 500 * total_batches  # 500ms per batch overhead
        
        # Calculate total time
        time_per_item = base_time_per_item * vram_factor / batch_efficiency
        total_processing_time = (original_batch * time_per_item) + batch_overhead
        
        return total_processing_time

    def _identify_memory_checkpoints(self, total_batches: int, strategy: VRAMStrategy) -> List[int]:
        """Identify points where memory cleanup should occur"""
        
        checkpoints = []
        
        if strategy == VRAMStrategy.CONSERVATIVE or total_batches > 4:
            # Add checkpoints every 3-4 batches for conservative strategy
            for i in range(3, total_batches, 3):
                checkpoints.append(i)
        elif strategy == VRAMStrategy.STREAMING:
            # Add checkpoints after every batch for streaming
            checkpoints = list(range(1, total_batches))
        elif total_batches > 8:
            # Add checkpoints for very large batch counts
            checkpoint_interval = max(4, total_batches // 4)
            for i in range(checkpoint_interval, total_batches, checkpoint_interval):
                checkpoints.append(i)
        
        return checkpoints

    def _generate_batch_optimization_notes(self, original_batch: int, optimal_batch: int,
                                         total_batches: int, estimated_vram_mb: float,
                                         vram_profile: VRAMProfile) -> List[str]:
        """Generate human-readable optimization notes"""
        
        notes = []
        
        if optimal_batch != original_batch:
            reduction_percent = ((original_batch - optimal_batch) / original_batch) * 100
            notes.append(f"Batch size optimized from {original_batch} to {optimal_batch} ({reduction_percent:.1f}% reduction)")
        
        if total_batches > 1:
            notes.append(f"Processing will be split into {total_batches} batches")
        
        vram_utilization = (estimated_vram_mb / vram_profile.total_vram_mb) * 100
        notes.append(f"Estimated VRAM usage: {estimated_vram_mb:.0f}MB ({vram_utilization:.1f}% of total)")
        
        if vram_utilization > 80:
            notes.append("High VRAM utilization - consider reducing resolution if OOM errors occur")
        elif vram_utilization < 50:
            notes.append("VRAM underutilized - batch size could potentially be increased")
        
        if vram_profile.total_vram_mb >= 15000:
            notes.append("RTX 4070 Ti SUPER optimizations applied")
        
        return notes

    async def _get_historical_batch_data(self, model_type: str, width: int, height: int) -> Dict[str, Any]:
        """Get historical batch optimization data"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                resolution = f"{width}x{height}"
                
                # Get max safe batch size for this configuration
                cursor.execute("""
                    SELECT max_safe_batch_size, vram_per_batch_mb
                    FROM vram_capacity_benchmarks
                    WHERE model_type = ? AND resolution = ? AND hardware_profile = ?
                """, (model_type, resolution, "rtx_4070ti_super"))
                
                result = cursor.fetchone()
                
                if result:
                    return {
                        "max_safe_batch_size": result[0],
                        "vram_per_batch_mb": result[1]
                    }
                    
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "historical_data_failed", f"Failed to get historical data: {str(e)}", {}, e)
        
        return {}

    async def _record_batch_optimization(self, generation_id: str, plan: BatchOptimizationPlan):
        """Record batch optimization plan"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO batch_optimization_history (
                        generation_id, original_batch_size, optimized_batch_size,
                        total_batches, estimated_vram_mb, batching_mode, vram_strategy
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    generation_id, plan.original_batch_size, plan.optimized_batch_size,
                    plan.total_batches, plan.estimated_vram_per_batch_mb,
                    plan.batching_mode.value, plan.vram_strategy.value
                ))
                
                conn.commit()
                
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "optimization_recording_failed", f"Failed to record optimization: {str(e)}", {
                "generation_id": generation_id
            }, e)

    async def record_actual_vram_usage(self, generation_id: str, model_type: str,
                                      batch_size: int, resolution: str,
                                      actual_vram_mb: int, success: bool,
                                      oom_error: bool = False):
        """Record actual VRAM usage for learning"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO vram_usage_history (
                        generation_id, model_type, batch_size, resolution,
                        vram_used_mb, success, oom_error
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (generation_id, model_type, batch_size, resolution, actual_vram_mb, success, oom_error))
                
                # Update capacity benchmarks if successful
                if success and not oom_error:
                    vram_per_batch = actual_vram_mb / batch_size
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO vram_capacity_benchmarks (
                            model_type, resolution, max_safe_batch_size, vram_per_batch_mb, hardware_profile
                        ) VALUES (?, ?, ?, ?, ?)
                    """, (model_type, resolution, batch_size, vram_per_batch, "rtx_4070ti_super"))
                
                conn.commit()
                
                log_activity("VRAM_OPTIMIZER", f"Recorded VRAM usage for {generation_id}", {
                    "vram_mb": actual_vram_mb,
                    "success": success,
                    "oom_error": oom_error
                })
                
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "usage_recording_failed", f"Failed to record usage: {str(e)}", {
                "generation_id": generation_id
            }, e)

    async def get_vram_optimization_insights(self, days: int = 7) -> Dict[str, Any]:
        """Get VRAM optimization insights and statistics"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Get batch optimization statistics
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_optimizations,
                        AVG(optimized_batch_size) as avg_optimized_batch,
                        AVG(total_batches) as avg_total_batches,
                        AVG(estimated_vram_mb) as avg_estimated_vram,
                        COUNT(CASE WHEN optimization_success THEN 1 END) as successful_optimizations
                    FROM batch_optimization_history
                    WHERE created_at >= datetime('now', '-{} days')
                """.format(days))
                
                batch_stats = cursor.fetchone()
                
                # Get VRAM usage patterns
                cursor.execute("""
                    SELECT 
                        model_type,
                        AVG(vram_used_mb) as avg_vram_usage,
                        MAX(vram_used_mb) as max_vram_usage,
                        AVG(batch_size) as avg_batch_size,
                        COUNT(CASE WHEN success THEN 1 END) as successes,
                        COUNT(CASE WHEN oom_error THEN 1 END) as oom_errors,
                        COUNT(*) as total_generations
                    FROM vram_usage_history
                    WHERE timestamp >= datetime('now', '-{} days')
                    GROUP BY model_type
                """.format(days))
                
                usage_patterns = cursor.fetchall()
                
                # Get capacity benchmarks
                cursor.execute("""
                    SELECT model_type, resolution, max_safe_batch_size, vram_per_batch_mb
                    FROM vram_capacity_benchmarks
                    WHERE hardware_profile = 'rtx_4070ti_super'
                    ORDER BY benchmark_date DESC
                """)
                
                benchmarks = cursor.fetchall()
                
                insights = {
                    "period_days": days,
                    "batch_optimization": {
                        "total_optimizations": batch_stats[0] if batch_stats else 0,
                        "avg_optimized_batch_size": batch_stats[1] if batch_stats else 0,
                        "avg_total_batches": batch_stats[2] if batch_stats else 0,
                        "avg_estimated_vram_mb": batch_stats[3] if batch_stats else 0,
                        "success_rate": (batch_stats[4] / batch_stats[0]) * 100 if batch_stats and batch_stats[0] > 0 else 0
                    },
                    "vram_usage_by_model": {},
                    "capacity_benchmarks": {},
                    "recommendations": []
                }
                
                # Process usage patterns
                for row in usage_patterns:
                    model_type, avg_vram, max_vram, avg_batch, successes, oom_errors, total = row
                    success_rate = (successes / total) * 100 if total > 0 else 0
                    oom_rate = (oom_errors / total) * 100 if total > 0 else 0
                    
                    insights["vram_usage_by_model"][model_type] = {
                        "avg_vram_usage_mb": avg_vram,
                        "max_vram_usage_mb": max_vram,
                        "avg_batch_size": avg_batch,
                        "success_rate": success_rate,
                        "oom_rate": oom_rate,
                        "total_generations": total
                    }
                
                # Process benchmarks
                for row in benchmarks:
                    model_type, resolution, max_batch, vram_per_batch = row
                    key = f"{model_type}_{resolution}"
                    insights["capacity_benchmarks"][key] = {
                        "model_type": model_type,
                        "resolution": resolution,
                        "max_safe_batch_size": max_batch,
                        "vram_per_batch_mb": vram_per_batch
                    }
                
                # Generate recommendations
                insights["recommendations"] = await self._generate_vram_recommendations(insights)
                
                return insights
                
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "insights_failed", f"Failed to generate insights: {str(e)}", {}, e)
            return {"error": str(e)}

    async def _generate_vram_recommendations(self, insights: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate VRAM optimization recommendations"""
        
        recommendations = []
        
        # Check for high OOM rates
        for model_type, stats in insights["vram_usage_by_model"].items():
            if stats["oom_rate"] > 5:  # More than 5% OOM rate
                recommendations.append({
                    "type": "critical",
                    "category": "oom_prevention",
                    "model": model_type,
                    "message": f"High OOM rate ({stats['oom_rate']:.1f}%) for {model_type}",
                    "suggestions": [
                        "Reduce default batch sizes",
                        "Enable more conservative VRAM strategy",
                        "Add more memory checkpoints"
                    ]
                })
        
        # Check for VRAM underutilization
        current_vram_profile = await self.get_current_vram_profile()
        for model_type, stats in insights["vram_usage_by_model"].items():
            vram_utilization = (stats["avg_vram_usage_mb"] / current_vram_profile.total_vram_mb) * 100
            
            if vram_utilization < 40:  # Less than 40% utilization
                recommendations.append({
                    "type": "optimization",
                    "category": "vram_utilization",
                    "model": model_type,
                    "message": f"VRAM underutilized ({vram_utilization:.1f}%) for {model_type}",
                    "suggestions": [
                        "Consider increasing default batch sizes",
                        "Try higher resolutions",
                        "Enable more aggressive VRAM strategy"
                    ]
                })
        
        # Check optimization success rates
        batch_stats = insights["batch_optimization"]
        if batch_stats["success_rate"] < 90:
            recommendations.append({
                "type": "warning",
                "category": "optimization_effectiveness",
                "message": f"Batch optimization success rate is low ({batch_stats['success_rate']:.1f}%)",
                "suggestions": [
                    "Review optimization algorithms",
                    "Update VRAM estimation models",
                    "Increase safety buffers"
                ]
            })
        
        return recommendations

    async def cleanup_vram_resources(self):
        """Cleanup VRAM resources and force garbage collection"""
        
        try:
            # Trigger Python garbage collection
            import gc
            gc.collect()
            
            # If GPU monitoring is available, try to get current VRAM usage
            if self.gpu_monitoring_enabled:
                try:
                    import pynvml
                    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                    memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    
                    used_mb = memory_info.used // (1024 * 1024)
                    total_mb = memory_info.total // (1024 * 1024)
                    
                    log_activity("VRAM_OPTIMIZER", "VRAM cleanup completed", {
                        "vram_used_mb": used_mb,
                        "vram_total_mb": total_mb,
                        "vram_free_mb": total_mb - used_mb
                    })
                    
                except Exception as e:
                    log_activity("VRAM_OPTIMIZER", f"VRAM cleanup completed (monitoring failed: {str(e)})")
            else:
                log_activity("VRAM_OPTIMIZER", "VRAM cleanup completed (no GPU monitoring)")
                
        except Exception as e:
            log_error("VRAM_OPTIMIZER", "cleanup_failed", f"Failed to cleanup VRAM: {str(e)}", {}, e)

    def get_rtx_4070ti_super_recommendations(self, model_type: str, width: int, height: int) -> Dict[str, Any]:
        """Get RTX 4070 Ti SUPER specific recommendations"""
        
        config = self.rtx_4070ti_super_config
        
        # Get optimal batch range for model
        min_batch, max_batch = config["optimal_batch_ranges"].get(model_type.lower(), (1, 4))
        
        # Estimate VRAM usage
        estimated_vram = self.estimate_vram_usage(model_type, width, height, max_batch)
        
        # Check if resolution is optimal for RTX 4070 Ti SUPER
        total_pixels = width * height
        optimal_resolutions = [
            (512, 512), (768, 768), (1024, 1024), (1152, 896), (1344, 768), (1536, 1024)
        ]
        
        current_resolution_optimal = any(
            abs(width - opt_w) <= 64 and abs(height - opt_h) <= 64 
            for opt_w, opt_h in optimal_resolutions
        )
        
        recommendations = {
            "model_type": model_type,
            "hardware": "RTX 4070 Ti SUPER",
            "resolution": f"{width}x{height}",
            "optimal_batch_range": (min_batch, max_batch),
            "estimated_vram_usage_mb": estimated_vram,
            "resolution_optimal": current_resolution_optimal,
            "recommendations": []
        }
        
        # Add specific recommendations
        if estimated_vram > 14000:  # Close to 16GB limit
            recommendations["recommendations"].append({
                "type": "warning",
                "message": "High VRAM usage detected",
                "suggestion": "Consider reducing batch size or resolution"
            })
        
        if not current_resolution_optimal:
            recommendations["recommendations"].append({
                "type": "optimization",
                "message": "Non-optimal resolution detected",
                "suggestion": f"Consider using resolution closer to: {optimal_resolutions[0]}"
            })
        
        if max_batch > 1:
            recommendations["recommendations"].append({
                "type": "optimization", 
                "message": f"Batch processing available",
                "suggestion": f"Can process up to {max_batch} images simultaneously"
            })
        
        return recommendations