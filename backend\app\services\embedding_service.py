"""
Embedding Service for Semantic Features
Uses nomic-embed-text for text embeddings and similarity search
"""

import numpy as np
import json
import sqlite3
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import asyncio
import aiohttp
from datetime import datetime

class EmbeddingService:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        self.ollama_url = ollama_url
        self.db_path = Path("data/embeddings.db")
        self.db_path.parent.mkdir(exist_ok=True)
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database for storing embeddings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Prompts table with embeddings
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS prompt_embeddings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                prompt_text TEXT NOT NULL,
                embedding BLOB NOT NULL,
                model_used TEXT,
                quality_score REAL DEFAULT 0.0,
                user_rating INTEGER DEFAULT 0,
                generation_count INTEGER DEFAULT 0,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                tags TEXT,
                category TEXT
            )
        """)
        
        # Generation history with embeddings
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS generation_embeddings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                generation_id TEXT UNIQUE,
                prompt_text TEXT NOT NULL,
                embedding BLOB NOT NULL,
                model_used TEXT,
                parameters TEXT,
                output_path TEXT,
                user_rating INTEGER DEFAULT 0,
                is_favorite BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Similarity cache for performance
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS similarity_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_hash TEXT UNIQUE,
                similar_prompts TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
    
    async def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for text using nomic-embed-text model"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": "nomic-embed-text:latest",
                    "prompt": text
                }
                
                async with session.post(f"{self.ollama_url}/api/embeddings", json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return np.array(result["embedding"])
                    else:
                        raise Exception(f"Embedding API error: {response.status}")
        except Exception as e:
            print(f"Error getting embedding: {e}")
            # Return zero vector as fallback
            return np.zeros(768)  # nomic-embed-text dimension
    
    def cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    async def store_prompt_embedding(
        self, 
        prompt: str, 
        model_used: str = None,
        quality_score: float = 0.0,
        tags: List[str] = None,
        category: str = None
    ) -> int:
        """Store a prompt and its embedding"""
        embedding = await self.get_embedding(prompt)
        embedding_blob = embedding.tobytes()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO prompt_embeddings 
            (prompt_text, embedding, model_used, quality_score, tags, category)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            prompt, 
            embedding_blob, 
            model_used, 
            quality_score,
            json.dumps(tags) if tags else None,
            category
        ))
        
        prompt_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return prompt_id
    
    async def find_similar_prompts(
        self, 
        query_prompt: str, 
        limit: int = 5,
        min_similarity: float = 0.3,
        exclude_exact: bool = True
    ) -> List[Dict]:
        """Find prompts similar to the query prompt"""
        
        # Get embedding for query
        query_embedding = await self.get_embedding(query_prompt)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all stored embeddings
        cursor.execute("""
            SELECT id, prompt_text, embedding, quality_score, user_rating, 
                   generation_count, model_used, tags, category
            FROM prompt_embeddings
        """)
        
        results = []
        for row in cursor.fetchall():
            stored_embedding = np.frombuffer(row[2], dtype=np.float64)
            
            # Skip exact matches if requested
            if exclude_exact and row[1].strip().lower() == query_prompt.strip().lower():
                continue
            
            similarity = self.cosine_similarity(query_embedding, stored_embedding)
            
            if similarity >= min_similarity:
                results.append({
                    'id': row[0],
                    'prompt': row[1],
                    'similarity': similarity,
                    'quality_score': row[3],
                    'user_rating': row[4],
                    'generation_count': row[5],
                    'model_used': row[6],
                    'tags': json.loads(row[7]) if row[7] else [],
                    'category': row[8]
                })
        
        conn.close()
        
        # Sort by similarity (and quality as tiebreaker)
        results.sort(key=lambda x: (x['similarity'], x['quality_score']), reverse=True)
        
        return results[:limit]
    
    async def get_enhanced_context_for_prompt(
        self, 
        prompt: str,
        include_successful: bool = True
    ) -> Dict:
        """Get context for enhancing a prompt based on similar successful prompts"""
        
        similar_prompts = await self.find_similar_prompts(prompt, limit=10)
        
        if include_successful:
            # Filter for higher quality/rated prompts
            successful_prompts = [
                p for p in similar_prompts 
                if p['quality_score'] > 0.5 or p['user_rating'] > 3
            ]
        else:
            successful_prompts = similar_prompts
        
        # Extract common patterns
        common_tags = {}
        categories = {}
        
        for p in successful_prompts:
            for tag in p['tags']:
                common_tags[tag] = common_tags.get(tag, 0) + 1
            
            if p['category']:
                categories[p['category']] = categories.get(p['category'], 0) + 1
        
        return {
            'similar_prompts': similar_prompts[:5],
            'successful_examples': successful_prompts[:3],
            'common_tags': sorted(common_tags.items(), key=lambda x: x[1], reverse=True)[:10],
            'suggested_categories': sorted(categories.items(), key=lambda x: x[1], reverse=True)[:3],
            'enhancement_hints': self._generate_enhancement_hints(successful_prompts)
        }
    
    def _generate_enhancement_hints(self, successful_prompts: List[Dict]) -> List[str]:
        """Generate enhancement hints based on successful similar prompts"""
        hints = []
        
        # Analyze common patterns in successful prompts
        all_prompts_text = " ".join([p['prompt'] for p in successful_prompts])
        
        # Common quality indicators
        quality_terms = [
            'detailed', 'high quality', 'masterpiece', 'professional',
            'cinematic', 'dramatic lighting', 'photorealistic'
        ]
        
        for term in quality_terms:
            if term in all_prompts_text.lower():
                hints.append(f"Consider adding '{term}' for better quality")
        
        # Style suggestions based on successful prompts
        if 'art' in all_prompts_text.lower():
            hints.append("Art style specifications often improve results")
        
        if 'lighting' in all_prompts_text.lower():
            hints.append("Lighting descriptions enhance visual impact")
        
        return hints[:3]  # Limit to top 3 hints
    
    async def store_generation_result(
        self,
        generation_id: str,
        prompt: str,
        model_used: str,
        parameters: Dict,
        output_path: str = None,
        user_rating: int = 0,
        is_favorite: bool = False
    ):
        """Store a generation result with its embedding"""
        
        embedding = await self.get_embedding(prompt)
        embedding_blob = embedding.tobytes()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO generation_embeddings
            (generation_id, prompt_text, embedding, model_used, parameters, 
             output_path, user_rating, is_favorite)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            generation_id,
            prompt,
            embedding_blob,
            model_used,
            json.dumps(parameters),
            output_path,
            user_rating,
            is_favorite
        ))
        
        conn.commit()
        conn.close()
    
    async def semantic_search_history(
        self,
        query: str,
        limit: int = 10,
        min_similarity: float = 0.2
    ) -> List[Dict]:
        """Search generation history semantically"""
        
        query_embedding = await self.get_embedding(query)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT generation_id, prompt_text, embedding, model_used, parameters,
                   output_path, user_rating, is_favorite, created_at
            FROM generation_embeddings
            ORDER BY created_at DESC
        """)
        
        results = []
        for row in cursor.fetchall():
            stored_embedding = np.frombuffer(row[2], dtype=np.float64)
            similarity = self.cosine_similarity(query_embedding, stored_embedding)
            
            if similarity >= min_similarity:
                results.append({
                    'generation_id': row[0],
                    'prompt': row[1],
                    'similarity': similarity,
                    'model_used': row[3],
                    'parameters': json.loads(row[4]) if row[4] else {},
                    'output_path': row[5],
                    'user_rating': row[6],
                    'is_favorite': bool(row[7]),
                    'created_at': row[8]
                })
        
        conn.close()
        
        # Sort by similarity
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return results[:limit]
    
    async def update_prompt_quality(
        self,
        prompt: str,
        quality_score: float = None,
        user_rating: int = None,
        increment_usage: bool = True
    ):
        """Update quality metrics for a prompt"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if quality_score is not None:
            updates.append("quality_score = ?")
            params.append(quality_score)
        
        if user_rating is not None:
            updates.append("user_rating = ?")
            params.append(user_rating)
        
        if increment_usage:
            updates.append("generation_count = generation_count + 1")
            updates.append("last_used = CURRENT_TIMESTAMP")
        
        if updates:
            query = f"""
                UPDATE prompt_embeddings 
                SET {', '.join(updates)}
                WHERE prompt_text = ?
            """
            params.append(prompt)
            
            cursor.execute(query, params)
            conn.commit()
        
        conn.close()
    
    async def get_trending_prompts(self, limit: int = 10) -> List[Dict]:
        """Get trending prompts based on recent usage and ratings"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT prompt_text, quality_score, user_rating, generation_count,
                   tags, category, last_used
            FROM prompt_embeddings
            WHERE last_used > datetime('now', '-7 days')
            ORDER BY (quality_score * 0.4 + user_rating * 0.3 + generation_count * 0.3) DESC
            LIMIT ?
        """, (limit,))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'prompt': row[0],
                'quality_score': row[1],
                'user_rating': row[2],
                'generation_count': row[3],
                'tags': json.loads(row[4]) if row[4] else [],
                'category': row[5],
                'last_used': row[6]
            })
        
        conn.close()
        return results
    
    async def auto_categorize_prompt(self, prompt: str) -> str:
        """Automatically categorize a prompt based on similar prompts"""
        
        similar_prompts = await self.find_similar_prompts(prompt, limit=5)
        
        if not similar_prompts:
            return "uncategorized"
        
        # Get most common category from similar prompts
        categories = {}
        for p in similar_prompts:
            if p['category']:
                weight = p['similarity']  # Weight by similarity
                categories[p['category']] = categories.get(p['category'], 0) + weight
        
        if categories:
            return max(categories.items(), key=lambda x: x[1])[0]
        
        return "uncategorized"
    
    async def suggest_prompt_improvements(self, prompt: str) -> Dict:
        """Suggest improvements for a prompt based on similar successful ones"""
        
        context = await self.get_enhanced_context_for_prompt(prompt)
        similar_successful = context['successful_examples']
        
        if not similar_successful:
            return {'suggestions': [], 'confidence': 0.0}
        
        suggestions = []
        
        # Analyze differences between user prompt and successful similar prompts
        prompt_lower = prompt.lower()
        
        for successful in similar_successful:
            successful_lower = successful['prompt'].lower()
            
            # Find terms in successful prompts that aren't in user prompt
            successful_words = set(successful_lower.split())
            user_words = set(prompt_lower.split())
            
            missing_terms = successful_words - user_words
            
            # Filter for meaningful terms (not common words)
            meaningful_terms = [
                term for term in missing_terms 
                if len(term) > 3 and term not in ['the', 'and', 'with', 'from', 'this', 'that']
            ]
            
            if meaningful_terms:
                suggestions.extend(meaningful_terms[:2])  # Limit per prompt
        
        # Deduplicate and rank suggestions
        suggestion_counts = {}
        for term in suggestions:
            suggestion_counts[term] = suggestion_counts.get(term, 0) + 1
        
        ranked_suggestions = sorted(
            suggestion_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:5]
        
        confidence = len(similar_successful) / 5.0  # Max confidence with 5+ similar prompts
        
        return {
            'suggestions': [term for term, count in ranked_suggestions],
            'confidence': min(confidence, 1.0),
            'similar_count': len(similar_successful),
            'enhancement_hints': context['enhancement_hints']
        }

# Global instance
embedding_service = EmbeddingService()
