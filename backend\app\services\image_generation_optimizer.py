"""
Image Generation Optimization Service
Advanced optimization system for ComfyUI image generation processes with RTX 4070 Ti SUPER specialization
"""

import asyncio
import time
import json
import psutil
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from app.utils.centralized_logger import get_logger, log_activity, log_error

logger = get_logger()

class OptimizationStrategy(Enum):
    SPEED_FOCUSED = "speed_focused"
    QUALITY_FOCUSED = "quality_focused"
    BALANCED = "balanced"
    VRAM_CONSERVATIVE = "vram_conservative"
    EXTREME_PERFORMANCE = "extreme_performance"

class ModelType(Enum):
    FLUX = "flux"
    SDXL = "sdxl"
    SD15 = "sd15"

@dataclass
class GenerationMetrics:
    generation_id: str
    model_type: str
    workflow_complexity: str
    start_time: float
    end_time: Optional[float] = None
    total_time: Optional[float] = None
    vram_usage_mb: Optional[int] = None
    gpu_utilization_percent: Optional[float] = None
    gpu_temperature_celsius: Optional[float] = None
    cpu_utilization_percent: Optional[float] = None
    memory_usage_mb: Optional[int] = None
    steps: int = 25
    cfg_scale: float = 7.0
    width: int = 512
    height: int = 512
    batch_size: int = 1
    sampler: str = "euler"
    scheduler: str = "normal"
    quality_score: Optional[float] = None
    user_satisfaction: Optional[float] = None
    errors_count: int = 0
    retries_count: int = 0
    optimization_strategy: str = "balanced"
    
    def to_dict(self) -> dict:
        return asdict(self)

@dataclass 
class OptimizationProfile:
    model_type: ModelType
    complexity_level: str  # simple, medium, complex, extreme
    strategy: OptimizationStrategy
    
    # Hardware settings
    vram_allocation_percent: float = 0.95
    enable_mixed_precision: bool = True
    enable_cuda_graphs: bool = True
    memory_efficient: bool = False
    
    # Generation parameters
    optimal_batch_size: int = 1
    recommended_steps_range: Tuple[int, int] = (15, 30)
    optimal_cfg_range: Tuple[float, float] = (3.0, 12.0)
    preferred_samplers: List[str] = None
    preferred_schedulers: List[str] = None
    
    # Workflow optimizations
    enable_model_caching: bool = True
    enable_vae_caching: bool = True
    enable_clip_caching: bool = True
    async_preprocessing: bool = True
    pipeline_parallelization: bool = False
    
    # Quality vs Performance balance
    quality_threshold: float = 0.85
    performance_priority: float = 0.5  # 0=quality, 1=speed
    
    def __post_init__(self):
        if self.preferred_samplers is None:
            self.preferred_samplers = ["euler", "heun", "dpm_2"]
        if self.preferred_schedulers is None:
            self.preferred_schedulers = ["normal", "karras", "simple"]

class ImageGenerationOptimizer:
    """Advanced image generation optimizer with RTX 4070 Ti SUPER specialization"""
    
    def __init__(self, database_path: str = "data/generation_optimization.db"):
        self.database_path = database_path
        self.active_optimizations: Dict[str, Dict[str, Any]] = {}
        self.performance_history: List[GenerationMetrics] = []
        self.optimization_profiles = self._initialize_optimization_profiles()
        self._initialize_database()
        
        # Hardware monitoring
        self.gpu_monitoring_enabled = self._check_gpu_monitoring()
        
        # Performance baselines (RTX 4070 Ti SUPER specific)
        self.rtx_4070ti_super_baselines = {
            ModelType.FLUX: {
                "vram_usage_mb": 12288,  # 12GB typical
                "generation_time_seconds": 25.0,
                "optimal_batch_size": 1,
                "max_resolution": (1536, 1536)
            },
            ModelType.SDXL: {
                "vram_usage_mb": 8192,   # 8GB typical
                "generation_time_seconds": 15.0,
                "optimal_batch_size": 2,
                "max_resolution": (1536, 1536)
            },
            ModelType.SD15: {
                "vram_usage_mb": 4096,   # 4GB typical
                "generation_time_seconds": 8.0,
                "optimal_batch_size": 4,
                "max_resolution": (1024, 1024)
            }
        }
        
        log_activity("GENERATION_OPTIMIZER", "Image generation optimizer initialized", {
            "gpu_monitoring": self.gpu_monitoring_enabled,
            "database_path": database_path
        })

    def _initialize_optimization_profiles(self) -> Dict[str, OptimizationProfile]:
        """Initialize optimization profiles for different model types and strategies"""
        profiles = {}
        
        # Flux model profiles
        profiles["flux_speed"] = OptimizationProfile(
            model_type=ModelType.FLUX,
            complexity_level="medium",
            strategy=OptimizationStrategy.SPEED_FOCUSED,
            vram_allocation_percent=0.95,
            optimal_batch_size=1,
            recommended_steps_range=(12, 20),
            optimal_cfg_range=(2.0, 4.0),
            preferred_samplers=["euler", "dpm_2"],
            preferred_schedulers=["simple"],
            performance_priority=0.8
        )
        
        profiles["flux_quality"] = OptimizationProfile(
            model_type=ModelType.FLUX,
            complexity_level="complex",
            strategy=OptimizationStrategy.QUALITY_FOCUSED,
            vram_allocation_percent=0.98,
            optimal_batch_size=1,
            recommended_steps_range=(25, 40),
            optimal_cfg_range=(3.0, 5.0),
            preferred_samplers=["dpm_2", "heun"],
            preferred_schedulers=["karras"],
            performance_priority=0.2
        )
        
        profiles["flux_balanced"] = OptimizationProfile(
            model_type=ModelType.FLUX,
            complexity_level="medium",
            strategy=OptimizationStrategy.BALANCED,
            vram_allocation_percent=0.95,
            optimal_batch_size=1,
            recommended_steps_range=(18, 28),
            optimal_cfg_range=(3.0, 4.5),
            preferred_samplers=["euler", "dpm_2"],
            preferred_schedulers=["normal", "simple"],
            performance_priority=0.5
        )
        
        # SDXL model profiles
        profiles["sdxl_speed"] = OptimizationProfile(
            model_type=ModelType.SDXL,
            complexity_level="medium",
            strategy=OptimizationStrategy.SPEED_FOCUSED,
            vram_allocation_percent=0.90,
            optimal_batch_size=2,
            recommended_steps_range=(15, 25),
            optimal_cfg_range=(5.0, 8.0),
            preferred_samplers=["euler", "dpm_2"],
            preferred_schedulers=["simple"],
            performance_priority=0.8
        )
        
        profiles["sdxl_quality"] = OptimizationProfile(
            model_type=ModelType.SDXL,
            complexity_level="complex",
            strategy=OptimizationStrategy.QUALITY_FOCUSED,
            vram_allocation_percent=0.95,
            optimal_batch_size=1,
            recommended_steps_range=(25, 40),
            optimal_cfg_range=(6.0, 10.0),
            preferred_samplers=["dpm_2", "heun"],
            preferred_schedulers=["karras"],
            performance_priority=0.2
        )
        
        # SD1.5 model profiles  
        profiles["sd15_speed"] = OptimizationProfile(
            model_type=ModelType.SD15,
            complexity_level="simple",
            strategy=OptimizationStrategy.SPEED_FOCUSED,
            vram_allocation_percent=0.85,
            optimal_batch_size=4,
            recommended_steps_range=(15, 25),
            optimal_cfg_range=(5.0, 9.0),
            preferred_samplers=["euler", "dpm_2"],
            preferred_schedulers=["simple"],
            performance_priority=0.8
        )
        
        return profiles
    
    def _check_gpu_monitoring(self) -> bool:
        """Check if GPU monitoring is available"""
        try:
            import pynvml
            pynvml.nvmlInit()
            return True
        except ImportError:
            log_activity("GENERATION_OPTIMIZER", "GPU monitoring not available (pynvml not installed)")
            return False
        except Exception as e:
            log_activity("GENERATION_OPTIMIZER", f"GPU monitoring not available: {str(e)}")
            return False
    
    def _initialize_database(self):
        """Initialize the optimization database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Generation metrics table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS generation_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT UNIQUE NOT NULL,
                        model_type TEXT NOT NULL,
                        workflow_complexity TEXT NOT NULL,
                        start_time REAL NOT NULL,
                        end_time REAL,
                        total_time REAL,
                        vram_usage_mb INTEGER,
                        gpu_utilization_percent REAL,
                        gpu_temperature_celsius REAL,
                        cpu_utilization_percent REAL,
                        memory_usage_mb INTEGER,
                        steps INTEGER,
                        cfg_scale REAL,
                        width INTEGER,
                        height INTEGER,
                        batch_size INTEGER,
                        sampler TEXT,
                        scheduler TEXT,
                        quality_score REAL,
                        user_satisfaction REAL,
                        errors_count INTEGER,
                        retries_count INTEGER,
                        optimization_strategy TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Optimization recommendations table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS optimization_recommendations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT NOT NULL,
                        recommendation_type TEXT NOT NULL,
                        original_value TEXT,
                        recommended_value TEXT,
                        expected_improvement_percent REAL,
                        confidence_score REAL,
                        applied BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Performance baselines table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_baselines (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_type TEXT NOT NULL,
                        hardware_profile TEXT NOT NULL,
                        complexity_level TEXT NOT NULL,
                        baseline_time_seconds REAL NOT NULL,
                        baseline_vram_mb INTEGER NOT NULL,
                        baseline_quality_score REAL,
                        sample_count INTEGER DEFAULT 1,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "database_init_failed", f"Failed to initialize database: {str(e)}", {}, e)
            raise

    async def optimize_generation_parameters(self, generation_request: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize generation parameters based on model type and performance goals"""
        
        generation_id = generation_request.get("generation_id", "unknown")
        model_type = self._detect_model_type(generation_request.get("model", ""))
        complexity = self._analyze_workflow_complexity(generation_request)
        
        log_activity("GENERATION_OPTIMIZER", f"Starting optimization for {generation_id}", {
            "model_type": model_type.value,
            "complexity": complexity,
            "original_params": generation_request
        })
        
        # Select optimization profile
        profile_key = self._select_optimization_profile(model_type, complexity, generation_request)
        profile = self.optimization_profiles.get(profile_key)
        
        if not profile:
            log_activity("GENERATION_OPTIMIZER", f"No optimization profile found for {profile_key}, using defaults")
            return generation_request
        
        # Apply optimizations
        optimized_request = generation_request.copy()
        optimizations_applied = []
        
        # Optimize steps
        current_steps = generation_request.get("steps", 25)
        optimal_steps = self._optimize_steps(current_steps, profile, complexity)
        if optimal_steps != current_steps:
            optimized_request["steps"] = optimal_steps
            optimizations_applied.append(f"steps: {current_steps} → {optimal_steps}")
        
        # Optimize CFG scale
        current_cfg = generation_request.get("cfg_scale", 7.0)
        optimal_cfg = self._optimize_cfg_scale(current_cfg, profile, model_type)
        if abs(optimal_cfg - current_cfg) > 0.1:
            optimized_request["cfg_scale"] = optimal_cfg
            optimizations_applied.append(f"cfg_scale: {current_cfg} → {optimal_cfg}")
        
        # Optimize batch size
        current_batch = generation_request.get("batch_size", 1)
        optimal_batch = self._optimize_batch_size(current_batch, profile, generation_request)
        if optimal_batch != current_batch:
            optimized_request["batch_size"] = optimal_batch
            optimizations_applied.append(f"batch_size: {current_batch} → {optimal_batch}")
        
        # Optimize sampler
        current_sampler = generation_request.get("sampler", "euler")
        optimal_sampler = self._optimize_sampler(current_sampler, profile)
        if optimal_sampler != current_sampler:
            optimized_request["sampler"] = optimal_sampler
            optimizations_applied.append(f"sampler: {current_sampler} → {optimal_sampler}")
        
        # Optimize scheduler
        current_scheduler = generation_request.get("scheduler", "normal")
        optimal_scheduler = self._optimize_scheduler(current_scheduler, profile)
        if optimal_scheduler != current_scheduler:
            optimized_request["scheduler"] = optimal_scheduler
            optimizations_applied.append(f"scheduler: {current_scheduler} → {optimal_scheduler}")
        
        # Add RTX 4070 Ti SUPER specific optimizations
        rtx_optimizations = self._apply_rtx_4070ti_super_optimizations(optimized_request, model_type, profile)
        optimized_request.update(rtx_optimizations["parameters"])
        optimizations_applied.extend(rtx_optimizations["applied"])
        
        # Store optimization record
        await self._record_optimization(generation_id, generation_request, optimized_request, optimizations_applied)
        
        log_activity("GENERATION_OPTIMIZER", f"Optimization completed for {generation_id}", {
            "optimizations_applied": optimizations_applied,
            "profile_used": profile_key
        })
        
        return optimized_request

    def _detect_model_type(self, model_name: str) -> ModelType:
        """Detect model type from model name"""
        model_lower = model_name.lower()
        
        if "flux" in model_lower:
            return ModelType.FLUX
        elif "sdxl" in model_lower or "xl" in model_lower:
            return ModelType.SDXL
        elif "sd1" in model_lower or "sd_1" in model_lower or "v1-" in model_lower:
            return ModelType.SD15
        else:
            # Default to SDXL for unknown models
            return ModelType.SDXL
    
    def _analyze_workflow_complexity(self, request: Dict[str, Any]) -> str:
        """Analyze workflow complexity based on parameters"""
        complexity_score = 0
        
        # Resolution complexity
        width = request.get("width", 512)
        height = request.get("height", 512)
        total_pixels = width * height
        
        if total_pixels > 1536 * 1536:
            complexity_score += 3
        elif total_pixels > 1024 * 1024:
            complexity_score += 2
        elif total_pixels > 768 * 768:
            complexity_score += 1
        
        # Steps complexity
        steps = request.get("steps", 25)
        if steps > 40:
            complexity_score += 3
        elif steps > 30:
            complexity_score += 2
        elif steps > 20:
            complexity_score += 1
        
        # Batch size complexity
        batch_size = request.get("batch_size", 1)
        if batch_size > 4:
            complexity_score += 3
        elif batch_size > 2:
            complexity_score += 2
        elif batch_size > 1:
            complexity_score += 1
        
        # CFG scale complexity (higher CFG = more compute)
        cfg_scale = request.get("cfg_scale", 7.0)
        if cfg_scale > 12:
            complexity_score += 2
        elif cfg_scale > 9:
            complexity_score += 1
        
        # Categorize complexity
        if complexity_score >= 8:
            return "extreme"
        elif complexity_score >= 5:
            return "complex"
        elif complexity_score >= 2:
            return "medium"
        else:
            return "simple"

    def _select_optimization_profile(self, model_type: ModelType, complexity: str, request: Dict[str, Any]) -> str:
        """Select the best optimization profile for the request"""
        
        # Check if user has specified a preference
        strategy_hint = request.get("optimization_strategy", "balanced")
        
        if strategy_hint == "speed":
            return f"{model_type.value}_speed"
        elif strategy_hint == "quality":
            return f"{model_type.value}_quality"
        else:
            return f"{model_type.value}_balanced"
    
    def _optimize_steps(self, current_steps: int, profile: OptimizationProfile, complexity: str) -> int:
        """Optimize the number of sampling steps"""
        min_steps, max_steps = profile.recommended_steps_range
        
        if complexity == "simple":
            optimal = min_steps
        elif complexity == "extreme":
            optimal = max_steps
        else:
            # Interpolate based on complexity
            complexity_factor = {"medium": 0.5, "complex": 0.75}.get(complexity, 0.5)
            optimal = int(min_steps + (max_steps - min_steps) * complexity_factor)
        
        # Clamp to reasonable range
        return max(10, min(50, optimal))
    
    def _optimize_cfg_scale(self, current_cfg: float, profile: OptimizationProfile, model_type: ModelType) -> float:
        """Optimize CFG scale based on model and profile"""
        min_cfg, max_cfg = profile.optimal_cfg_range
        
        if profile.strategy == OptimizationStrategy.SPEED_FOCUSED:
            optimal = min_cfg
        elif profile.strategy == OptimizationStrategy.QUALITY_FOCUSED:
            optimal = max_cfg
        else:
            optimal = (min_cfg + max_cfg) / 2
        
        # Model-specific adjustments
        if model_type == ModelType.FLUX:
            optimal = min(4.5, optimal)  # Flux works better with lower CFG
        
        return round(optimal, 1)
    
    def _optimize_batch_size(self, current_batch: int, profile: OptimizationProfile, request: Dict[str, Any]) -> int:
        """Optimize batch size based on VRAM and resolution"""
        optimal_batch = profile.optimal_batch_size
        
        # Adjust based on resolution
        width = request.get("width", 512)
        height = request.get("height", 512)
        total_pixels = width * height
        
        if total_pixels > 1536 * 1536:
            optimal_batch = max(1, optimal_batch // 2)
        elif total_pixels > 1024 * 1024:
            optimal_batch = max(1, optimal_batch // 1.5)
        
        return int(optimal_batch)
    
    def _optimize_sampler(self, current_sampler: str, profile: OptimizationProfile) -> str:
        """Optimize sampler selection"""
        if current_sampler in profile.preferred_samplers:
            return current_sampler
        
        # Return the first preferred sampler
        return profile.preferred_samplers[0] if profile.preferred_samplers else current_sampler
    
    def _optimize_scheduler(self, current_scheduler: str, profile: OptimizationProfile) -> str:
        """Optimize scheduler selection"""
        if current_scheduler in profile.preferred_schedulers:
            return current_scheduler
            
        return profile.preferred_schedulers[0] if profile.preferred_schedulers else current_scheduler

    def _apply_rtx_4070ti_super_optimizations(self, request: Dict[str, Any], model_type: ModelType, profile: OptimizationProfile) -> Dict[str, Any]:
        """Apply RTX 4070 Ti SUPER specific optimizations"""
        
        optimizations = {
            "parameters": {},
            "applied": []
        }
        
        baseline = self.rtx_4070ti_super_baselines.get(model_type, {})
        
        # Memory management optimizations
        memory_opts = {
            "enable_gpu_acceleration": True,
            "memory_management": {
                "vram_allocation_percent": profile.vram_allocation_percent,
                "enable_mixed_precision": profile.enable_mixed_precision,
                "enable_cuda_graphs": profile.enable_cuda_graphs,
                "memory_efficient": profile.memory_efficient
            }
        }
        
        optimizations["parameters"]["hardware_config"] = memory_opts
        optimizations["applied"].append("RTX 4070 Ti SUPER memory optimization")
        
        # Resolution optimization based on model capacity
        width = request.get("width", 512)
        height = request.get("height", 512)
        max_res = baseline.get("max_resolution", (1024, 1024))
        
        if width > max_res[0] or height > max_res[1]:
            new_width = min(width, max_res[0])
            new_height = min(height, max_res[1])
            
            optimizations["parameters"]["width"] = new_width
            optimizations["parameters"]["height"] = new_height
            optimizations["applied"].append(f"resolution: {width}x{height} → {new_width}x{new_height}")
        
        # Batch size optimization for VRAM
        batch_size = request.get("batch_size", 1)
        optimal_batch = baseline.get("optimal_batch_size", 1)
        
        if batch_size > optimal_batch:
            optimizations["parameters"]["batch_size"] = optimal_batch
            optimizations["applied"].append(f"batch_size optimized for VRAM: {batch_size} → {optimal_batch}")
        
        return optimizations

    async def start_monitoring_generation(self, generation_id: str, request: Dict[str, Any]) -> GenerationMetrics:
        """Start monitoring a generation process"""
        
        model_type = self._detect_model_type(request.get("model", ""))
        complexity = self._analyze_workflow_complexity(request)
        
        metrics = GenerationMetrics(
            generation_id=generation_id,
            model_type=model_type.value,
            workflow_complexity=complexity,
            start_time=time.time(),
            steps=request.get("steps", 25),
            cfg_scale=request.get("cfg_scale", 7.0),
            width=request.get("width", 512),
            height=request.get("height", 512),
            batch_size=request.get("batch_size", 1),
            sampler=request.get("sampler", "euler"),
            scheduler=request.get("scheduler", "normal"),
            optimization_strategy=request.get("optimization_strategy", "balanced")
        )
        
        self.active_optimizations[generation_id] = {
            "metrics": metrics,
            "monitoring_task": None
        }
        
        # Start background monitoring
        monitoring_task = asyncio.create_task(self._monitor_generation_performance(generation_id))
        self.active_optimizations[generation_id]["monitoring_task"] = monitoring_task
        
        log_activity("GENERATION_OPTIMIZER", f"Started monitoring generation {generation_id}", {
            "model_type": model_type.value,
            "complexity": complexity
        })
        
        return metrics

    async def _monitor_generation_performance(self, generation_id: str):
        """Monitor generation performance in real-time"""
        
        try:
            optimization_data = self.active_optimizations.get(generation_id)
            if not optimization_data:
                return
                
            metrics = optimization_data["metrics"]
            
            while generation_id in self.active_optimizations:
                # Collect current performance metrics
                current_metrics = await self._collect_system_metrics()
                
                # Update metrics
                if current_metrics:
                    metrics.vram_usage_mb = current_metrics.get("vram_usage_mb")
                    metrics.gpu_utilization_percent = current_metrics.get("gpu_utilization")
                    metrics.gpu_temperature_celsius = current_metrics.get("gpu_temperature")
                    metrics.cpu_utilization_percent = current_metrics.get("cpu_utilization")
                    metrics.memory_usage_mb = current_metrics.get("memory_usage_mb")
                
                # Check for performance issues and recommend real-time adjustments
                recommendations = await self._analyze_real_time_performance(generation_id, current_metrics)
                if recommendations:
                    log_activity("GENERATION_OPTIMIZER", f"Real-time recommendations for {generation_id}", {
                        "recommendations": recommendations
                    })
                
                await asyncio.sleep(2)  # Monitor every 2 seconds
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "monitoring_failed", f"Performance monitoring failed for {generation_id}: {str(e)}", {
                "generation_id": generation_id
            }, e)

    async def _collect_system_metrics(self) -> Optional[Dict[str, Any]]:
        """Collect current system performance metrics"""
        
        try:
            metrics = {
                "cpu_utilization": psutil.cpu_percent(interval=None),
                "memory_usage_mb": psutil.virtual_memory().used // (1024 * 1024)
            }
            
            # GPU metrics if available
            if self.gpu_monitoring_enabled:
                try:
                    import pynvml
                    
                    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                    
                    # GPU utilization
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    metrics["gpu_utilization"] = utilization.gpu
                    
                    # VRAM usage
                    memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    metrics["vram_usage_mb"] = memory_info.used // (1024 * 1024)
                    metrics["vram_total_mb"] = memory_info.total // (1024 * 1024)
                    
                    # GPU temperature
                    temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                    metrics["gpu_temperature"] = temperature
                    
                except Exception as e:
                    log_activity("GENERATION_OPTIMIZER", f"GPU metrics collection failed: {str(e)}")
            
            return metrics
            
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "metrics_collection_failed", f"Failed to collect system metrics: {str(e)}", {}, e)
            return None

    async def _analyze_real_time_performance(self, generation_id: str, current_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze current performance and generate real-time recommendations"""
        
        recommendations = []
        
        if not current_metrics:
            return recommendations
        
        optimization_data = self.active_optimizations.get(generation_id)
        if not optimization_data:
            return recommendations
            
        metrics = optimization_data["metrics"]
        
        # VRAM usage analysis
        vram_usage_mb = current_metrics.get("vram_usage_mb", 0)
        vram_total_mb = current_metrics.get("vram_total_mb", 16384)  # 16GB default for RTX 4070 Ti SUPER
        vram_usage_percent = (vram_usage_mb / vram_total_mb) * 100 if vram_total_mb > 0 else 0
        
        if vram_usage_percent > 95:
            recommendations.append({
                "type": "critical",
                "category": "vram_management",
                "message": "VRAM usage critical, reduce batch size or resolution",
                "suggested_action": "reduce_batch_size",
                "current_value": metrics.batch_size,
                "recommended_value": max(1, metrics.batch_size // 2)
            })
        elif vram_usage_percent > 85:
            recommendations.append({
                "type": "warning",
                "category": "vram_management",
                "message": "High VRAM usage detected",
                "suggested_action": "enable_memory_efficient_mode"
            })
        
        # GPU utilization analysis
        gpu_utilization = current_metrics.get("gpu_utilization", 0)
        if gpu_utilization < 80:
            recommendations.append({
                "type": "optimization",
                "category": "gpu_utilization",
                "message": "GPU underutilized, consider increasing batch size",
                "suggested_action": "increase_batch_size",
                "current_value": metrics.batch_size,
                "recommended_value": min(4, metrics.batch_size + 1)
            })
        
        # Temperature analysis
        gpu_temperature = current_metrics.get("gpu_temperature", 0)
        if gpu_temperature > 83:
            recommendations.append({
                "type": "critical",
                "category": "thermal_management",
                "message": "GPU temperature critical, reduce workload",
                "suggested_action": "thermal_throttle_protection"
            })
        elif gpu_temperature > 78:
            recommendations.append({
                "type": "warning",
                "category": "thermal_management",
                "message": "High GPU temperature detected",
                "suggested_action": "monitor_thermal_state"
            })
        
        return recommendations

    async def finish_generation_monitoring(self, generation_id: str, success: bool = True, error: Optional[str] = None) -> Optional[GenerationMetrics]:
        """Finish monitoring and record final metrics"""
        
        optimization_data = self.active_optimizations.get(generation_id)
        if not optimization_data:
            return None
        
        metrics = optimization_data["metrics"]
        monitoring_task = optimization_data.get("monitoring_task")
        
        # Cancel monitoring task
        if monitoring_task:
            monitoring_task.cancel()
            try:
                await monitoring_task
            except asyncio.CancelledError:
                pass
        
        # Finalize metrics
        metrics.end_time = time.time()
        metrics.total_time = metrics.end_time - metrics.start_time
        
        if not success:
            metrics.errors_count += 1
        
        # Collect final system state
        final_metrics = await self._collect_system_metrics()
        if final_metrics:
            metrics.vram_usage_mb = final_metrics.get("vram_usage_mb")
            metrics.gpu_utilization_percent = final_metrics.get("gpu_utilization")
            metrics.gpu_temperature_celsius = final_metrics.get("gpu_temperature")
            metrics.cpu_utilization_percent = final_metrics.get("cpu_utilization")
            metrics.memory_usage_mb = final_metrics.get("memory_usage_mb")
        
        # Store metrics to database
        await self._store_generation_metrics(metrics)
        
        # Update performance baselines
        await self._update_performance_baselines(metrics)
        
        # Generate post-generation recommendations
        recommendations = await self._generate_post_generation_recommendations(metrics)
        
        # Cleanup
        del self.active_optimizations[generation_id]
        
        log_activity("GENERATION_OPTIMIZER", f"Finished monitoring generation {generation_id}", {
            "total_time": metrics.total_time,
            "success": success,
            "recommendations_count": len(recommendations)
        })
        
        return metrics

    async def _store_generation_metrics(self, metrics: GenerationMetrics):
        """Store generation metrics to database"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO generation_metrics (
                        generation_id, model_type, workflow_complexity, start_time, end_time,
                        total_time, vram_usage_mb, gpu_utilization_percent, gpu_temperature_celsius,
                        cpu_utilization_percent, memory_usage_mb, steps, cfg_scale, width, height,
                        batch_size, sampler, scheduler, quality_score, user_satisfaction,
                        errors_count, retries_count, optimization_strategy
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    metrics.generation_id, metrics.model_type, metrics.workflow_complexity,
                    metrics.start_time, metrics.end_time, metrics.total_time,
                    metrics.vram_usage_mb, metrics.gpu_utilization_percent, metrics.gpu_temperature_celsius,
                    metrics.cpu_utilization_percent, metrics.memory_usage_mb,
                    metrics.steps, metrics.cfg_scale, metrics.width, metrics.height, metrics.batch_size,
                    metrics.sampler, metrics.scheduler, metrics.quality_score, metrics.user_satisfaction,
                    metrics.errors_count, metrics.retries_count, metrics.optimization_strategy
                ))
                
                conn.commit()
                
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "metrics_storage_failed", f"Failed to store metrics: {str(e)}", {
                "generation_id": metrics.generation_id
            }, e)

    async def _update_performance_baselines(self, metrics: GenerationMetrics):
        """Update performance baselines based on new metrics"""
        
        if not metrics.total_time or metrics.errors_count > 0:
            return  # Don't update baselines with failed generations
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Check if baseline exists
                cursor.execute("""
                    SELECT id, baseline_time_seconds, baseline_vram_mb, sample_count
                    FROM performance_baselines
                    WHERE model_type = ? AND hardware_profile = ? AND complexity_level = ?
                """, (metrics.model_type, "rtx_4070ti_super", metrics.workflow_complexity))
                
                result = cursor.fetchone()
                
                if result:
                    # Update existing baseline with exponential moving average
                    baseline_id, current_time, current_vram, sample_count = result
                    
                    # Exponential moving average with 0.1 weight for new data
                    alpha = 0.1
                    new_time = current_time * (1 - alpha) + metrics.total_time * alpha
                    new_vram = current_vram * (1 - alpha) + (metrics.vram_usage_mb or 0) * alpha
                    
                    cursor.execute("""
                        UPDATE performance_baselines
                        SET baseline_time_seconds = ?, baseline_vram_mb = ?, sample_count = sample_count + 1,
                            last_updated = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (new_time, new_vram, baseline_id))
                else:
                    # Create new baseline
                    cursor.execute("""
                        INSERT INTO performance_baselines (
                            model_type, hardware_profile, complexity_level, baseline_time_seconds,
                            baseline_vram_mb, sample_count
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    """, (metrics.model_type, "rtx_4070ti_super", metrics.workflow_complexity,
                         metrics.total_time, metrics.vram_usage_mb or 0, 1))
                
                conn.commit()
                
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "baseline_update_failed", f"Failed to update baselines: {str(e)}", {
                "generation_id": metrics.generation_id
            }, e)

    async def _generate_post_generation_recommendations(self, metrics: GenerationMetrics) -> List[Dict[str, Any]]:
        """Generate recommendations based on completed generation metrics"""
        
        recommendations = []
        
        if not metrics.total_time:
            return recommendations
        
        # Get baseline for comparison
        baseline_time = self.rtx_4070ti_super_baselines.get(
            ModelType(metrics.model_type), {}
        ).get("generation_time_seconds", 20.0)
        
        performance_ratio = metrics.total_time / baseline_time
        
        if performance_ratio > 1.5:
            # Generation was significantly slower than baseline
            recommendations.append({
                "type": "performance",
                "category": "speed_optimization",
                "message": f"Generation was {performance_ratio:.1f}x slower than baseline",
                "suggestions": [
                    "Consider reducing steps or resolution",
                    "Try a faster sampler like 'euler'",
                    "Reduce batch size if using multiple images"
                ]
            })
        elif performance_ratio < 0.8:
            # Generation was faster than expected - might be able to improve quality
            recommendations.append({
                "type": "optimization",
                "category": "quality_optimization", 
                "message": f"Generation was {(1/performance_ratio):.1f}x faster than baseline",
                "suggestions": [
                    "Consider increasing steps for better quality",
                    "Try a higher quality sampler",
                    "Increase resolution if VRAM allows"
                ]
            })
        
        # VRAM optimization recommendations
        if metrics.vram_usage_mb:
            vram_usage_percent = (metrics.vram_usage_mb / 16384) * 100  # 16GB RTX 4070 Ti SUPER
            
            if vram_usage_percent > 90:
                recommendations.append({
                    "type": "critical",
                    "category": "vram_optimization",
                    "message": f"High VRAM usage ({vram_usage_percent:.1f}%)",
                    "suggestions": [
                        "Enable memory efficient mode",
                        "Reduce batch size",
                        "Consider lower resolution"
                    ]
                })
            elif vram_usage_percent < 60:
                recommendations.append({
                    "type": "optimization", 
                    "category": "vram_utilization",
                    "message": f"VRAM underutilized ({vram_usage_percent:.1f}%)",
                    "suggestions": [
                        "Consider increasing batch size",
                        "Try higher resolution",
                        "Enable higher quality settings"
                    ]
                })
        
        return recommendations

    async def _record_optimization(self, generation_id: str, original_request: Dict[str, Any], 
                                 optimized_request: Dict[str, Any], optimizations_applied: List[str]):
        """Record optimization changes to database"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                for optimization in optimizations_applied:
                    # Parse optimization string to extract type and values
                    parts = optimization.split(": ")
                    if len(parts) == 2:
                        param_name = parts[0]
                        value_change = parts[1]
                        
                        original_value = str(original_request.get(param_name, ""))
                        recommended_value = str(optimized_request.get(param_name, ""))
                        
                        cursor.execute("""
                            INSERT INTO optimization_recommendations (
                                generation_id, recommendation_type, original_value, recommended_value,
                                expected_improvement_percent, confidence_score, applied
                            ) VALUES (?, ?, ?, ?, ?, ?, ?)
                        """, (generation_id, param_name, original_value, recommended_value, 10.0, 0.8, True))
                
                conn.commit()
                
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "optimization_record_failed", f"Failed to record optimization: {str(e)}", {
                "generation_id": generation_id
            }, e)

    async def get_optimization_insights(self, model_type: str = None, days: int = 7) -> Dict[str, Any]:
        """Get optimization insights and statistics"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Base query conditions
                conditions = ["created_at >= datetime('now', '-{} days')".format(days)]
                params = []
                
                if model_type:
                    conditions.append("model_type = ?")
                    params.append(model_type)
                
                where_clause = " AND ".join(conditions)
                
                # Get performance statistics
                cursor.execute(f"""
                    SELECT 
                        model_type,
                        AVG(total_time) as avg_time,
                        AVG(vram_usage_mb) as avg_vram,
                        AVG(gpu_utilization_percent) as avg_gpu_util,
                        COUNT(*) as generation_count,
                        SUM(CASE WHEN errors_count = 0 THEN 1 ELSE 0 END) as success_count
                    FROM generation_metrics 
                    WHERE {where_clause}
                    GROUP BY model_type
                """, params)
                
                performance_stats = cursor.fetchall()
                
                # Get optimization effectiveness
                cursor.execute(f"""
                    SELECT 
                        recommendation_type,
                        COUNT(*) as recommendation_count,
                        AVG(expected_improvement_percent) as avg_improvement
                    FROM optimization_recommendations o
                    JOIN generation_metrics g ON o.generation_id = g.generation_id
                    WHERE g.{where_clause}
                    GROUP BY recommendation_type
                """, params)
                
                optimization_stats = cursor.fetchall()
                
                insights = {
                    "period_days": days,
                    "performance_by_model": {},
                    "optimization_effectiveness": {},
                    "recommendations": []
                }
                
                # Process performance stats
                for row in performance_stats:
                    model, avg_time, avg_vram, avg_gpu_util, gen_count, success_count = row
                    success_rate = (success_count / gen_count) * 100 if gen_count > 0 else 0
                    
                    insights["performance_by_model"][model] = {
                        "average_generation_time": avg_time,
                        "average_vram_usage_mb": avg_vram,
                        "average_gpu_utilization": avg_gpu_util,
                        "generation_count": gen_count,
                        "success_rate": success_rate
                    }
                
                # Process optimization stats
                for row in optimization_stats:
                    rec_type, rec_count, avg_improvement = row
                    insights["optimization_effectiveness"][rec_type] = {
                        "recommendation_count": rec_count,
                        "average_improvement_percent": avg_improvement
                    }
                
                # Generate insights and recommendations
                insights["recommendations"] = await self._generate_system_recommendations(insights)
                
                return insights
                
        except Exception as e:
            log_error("GENERATION_OPTIMIZER", "insights_generation_failed", f"Failed to generate insights: {str(e)}", {}, e)
            return {"error": str(e)}

    async def _generate_system_recommendations(self, insights: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate system-wide optimization recommendations based on insights"""
        
        recommendations = []
        
        for model_type, stats in insights["performance_by_model"].items():
            if stats["success_rate"] < 95:
                recommendations.append({
                    "type": "reliability",
                    "model": model_type,
                    "message": f"Low success rate for {model_type}: {stats['success_rate']:.1f}%",
                    "suggested_actions": [
                        "Review error patterns",
                        "Adjust default parameters", 
                        "Update optimization profiles"
                    ]
                })
            
            if stats["average_gpu_utilization"] and stats["average_gpu_utilization"] < 80:
                recommendations.append({
                    "type": "efficiency",
                    "model": model_type,
                    "message": f"GPU underutilization for {model_type}: {stats['average_gpu_utilization']:.1f}%",
                    "suggested_actions": [
                        "Increase default batch sizes",
                        "Optimize workflow parallelization",
                        "Review model loading strategy"
                    ]
                })
        
        return recommendations