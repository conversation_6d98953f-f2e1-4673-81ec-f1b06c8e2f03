"""
Phase 4.3 Configuration Optimization

This module provides optimized configuration settings for Phase 4.3 components:
- WebSocket connection resilience
- Performance monitoring thresholds  
- Event streaming parameters
- Integration settings

These settings have been tuned for production deployment.
"""

from typing import Dict, Any
import os
from dataclasses import dataclass

@dataclass
class WebSocketConfig:
    """Optimized WebSocket configuration for Phase 4.3"""
    
    # Connection settings
    RECONNECT_INTERVAL_MS: int = 1000  # Start with 1 second
    MAX_RECONNECT_ATTEMPTS: int = 10   # Allow 10 attempts before giving up
    EXPONENTIAL_BACKOFF_MULTIPLIER: float = 2.0  # Double delay each attempt
    MAX_RECONNECT_DELAY_MS: int = 30000  # Cap at 30 seconds
    CONNECTION_TIMEOUT_MS: int = 10000   # 10 second connection timeout
    
    # Heartbeat settings
    HEARTBEAT_INTERVAL_MS: int = 30000   # 30 second heartbeat
    HEARTBEAT_TIMEOUT_MS: int = 5000     # 5 second heartbeat timeout
    CLIENT_TIMEOUT_SECONDS: int = 60     # Consider client dead after 60s
    
    # Message handling
    MESSAGE_QUEUE_MAX_SIZE: int = 1000   # Max queued messages per client
    MAX_MESSAGE_SIZE_BYTES: int = 1048576  # 1MB max message size
    ENABLE_MESSAGE_COMPRESSION: bool = True
    
    # Performance settings
    ENABLE_AUTO_RECONNECT: bool = True
    ENABLE_SERVICE_DISCOVERY: bool = True
    DEBUG_MODE: bool = False  # Set to True for development

@dataclass
class PerformanceMonitoringConfig:
    """Performance monitoring configuration"""
    
    # Collection intervals
    METRICS_COLLECTION_INTERVAL_SECONDS: float = 5.0
    CLEANUP_INTERVAL_SECONDS: float = 300.0  # 5 minutes
    HISTORY_RETENTION_HOURS: int = 168  # 1 week
    
    # Alert thresholds
    HIGH_LATENCY_THRESHOLD_MS: int = 1000
    HIGH_ERROR_RATE_THRESHOLD: float = 0.05  # 5%
    LOW_CONNECTION_SUCCESS_RATE_THRESHOLD: float = 0.95  # 95%
    HIGH_MEMORY_USAGE_THRESHOLD_MB: int = 500
    MAX_CONNECTION_DROPS_PER_HOUR: int = 10
    
    # Alert settings
    ALERT_COOLDOWN_MINUTES: int = 15  # Prevent alert spam
    ENABLE_ALERT_NOTIFICATIONS: bool = True
    ALERT_SEVERITY_LEVELS: Dict[str, int] = None  # Will be set in __post_init__
    
    def __post_init__(self):
        if self.ALERT_SEVERITY_LEVELS is None:
            self.ALERT_SEVERITY_LEVELS = {
                'low': 1,
                'medium': 2, 
                'high': 3,
                'critical': 4
            }

@dataclass
class EventStreamingConfig:
    """Event streaming configuration"""
    
    # Progress throttling
    PROGRESS_THROTTLE_INTERVAL_SECONDS: float = 1.0  # 1 second between progress updates
    ENABLE_PROGRESS_THROTTLING: bool = True
    
    # Event queue settings
    EVENT_QUEUE_MAX_SIZE: int = 1000
    EVENT_BATCH_SIZE: int = 10  # Process events in batches
    EVENT_PROCESSING_TIMEOUT_SECONDS: float = 5.0
    
    # Event types priority (higher number = higher priority)
    EVENT_PRIORITIES: Dict[str, int] = None
    
    def __post_init__(self):
        if self.EVENT_PRIORITIES is None:
            self.EVENT_PRIORITIES = {
                'connection_status': 10,
                'generation_error': 9,
                'generation_complete': 8,
                'generation_start': 7,
                'generation_progress': 5,
                'system_stats': 3,
                'ping': 1,
                'pong': 1
            }

@dataclass
class IntegrationConfig:
    """Integration configuration for Phase 4.3"""
    
    # Service URLs
    BACKEND_BASE_URL: str = "http://localhost:8000"
    FRONTEND_BASE_URL: str = "http://localhost:3003"
    COMFYUI_BASE_URL: str = "http://localhost:8189"
    WEBSOCKET_URL: str = "ws://localhost:8000/ws"
    
    # API settings
    API_TIMEOUT_SECONDS: int = 30
    API_RETRY_ATTEMPTS: int = 3
    API_RETRY_DELAY_SECONDS: float = 1.0
    
    # Health check settings
    HEALTH_CHECK_INTERVAL_SECONDS: int = 30
    SERVICE_DISCOVERY_INTERVAL_SECONDS: int = 60
    
    # Feature flags
    ENABLE_PERFORMANCE_MONITORING: bool = True
    ENABLE_ENHANCED_WEBSOCKET_MANAGER: bool = True
    ENABLE_CONNECTION_RESILIENCE: bool = True
    ENABLE_EVENT_STREAMING: bool = True

class Phase4ConfigManager:
    """Configuration manager for Phase 4.3 components"""
    
    def __init__(self):
        self.websocket = WebSocketConfig()
        self.performance = PerformanceMonitoringConfig()
        self.streaming = EventStreamingConfig()
        self.integration = IntegrationConfig()
        self._load_environment_overrides()
    
    def _load_environment_overrides(self):
        """Load configuration overrides from environment variables"""
        
        # WebSocket configuration overrides
        if os.getenv('WS_RECONNECT_INTERVAL'):
            self.websocket.RECONNECT_INTERVAL_MS = int(os.getenv('WS_RECONNECT_INTERVAL'))
        
        if os.getenv('WS_MAX_RETRIES'):
            self.websocket.MAX_RECONNECT_ATTEMPTS = int(os.getenv('WS_MAX_RETRIES'))
        
        if os.getenv('WS_HEARTBEAT_INTERVAL'):
            self.websocket.HEARTBEAT_INTERVAL_MS = int(os.getenv('WS_HEARTBEAT_INTERVAL'))
        
        if os.getenv('ENABLE_AUTO_RECONNECT'):
            self.websocket.ENABLE_AUTO_RECONNECT = os.getenv('ENABLE_AUTO_RECONNECT').lower() == 'true'
        
        if os.getenv('DEBUG_MODE'):
            self.websocket.DEBUG_MODE = os.getenv('DEBUG_MODE').lower() == 'true'
        
        # Performance monitoring overrides
        if os.getenv('PERF_METRICS_INTERVAL'):
            self.performance.METRICS_COLLECTION_INTERVAL_SECONDS = float(os.getenv('PERF_METRICS_INTERVAL'))
        
        if os.getenv('ALERT_HIGH_LATENCY_MS'):
            self.performance.HIGH_LATENCY_THRESHOLD_MS = int(os.getenv('ALERT_HIGH_LATENCY_MS'))
        
        # Event streaming overrides
        if os.getenv('PROGRESS_THROTTLE_INTERVAL'):
            self.streaming.PROGRESS_THROTTLE_INTERVAL_SECONDS = float(os.getenv('PROGRESS_THROTTLE_INTERVAL'))
        
        # Integration overrides
        if os.getenv('BACKEND_URL'):
            self.integration.BACKEND_BASE_URL = os.getenv('BACKEND_URL')
        
        if os.getenv('WEBSOCKET_URL'):
            self.integration.WEBSOCKET_URL = os.getenv('WEBSOCKET_URL')
    
    def get_websocket_config_dict(self) -> Dict[str, Any]:
        """Get WebSocket configuration as dictionary"""
        return {
            'reconnectInterval': self.websocket.RECONNECT_INTERVAL_MS,
            'maxReconnectAttempts': self.websocket.MAX_RECONNECT_ATTEMPTS,
            'exponentialBackoffMultiplier': self.websocket.EXPONENTIAL_BACKOFF_MULTIPLIER,
            'maxReconnectDelay': self.websocket.MAX_RECONNECT_DELAY_MS,
            'connectionTimeout': self.websocket.CONNECTION_TIMEOUT_MS,
            'heartbeatInterval': self.websocket.HEARTBEAT_INTERVAL_MS,
            'heartbeatTimeout': self.websocket.HEARTBEAT_TIMEOUT_MS,
            'clientTimeout': self.websocket.CLIENT_TIMEOUT_SECONDS,
            'messageQueueMaxSize': self.websocket.MESSAGE_QUEUE_MAX_SIZE,
            'enableAutoReconnect': self.websocket.ENABLE_AUTO_RECONNECT,
            'enableServiceDiscovery': self.websocket.ENABLE_SERVICE_DISCOVERY,
            'debugMode': self.websocket.DEBUG_MODE
        }
    
    def get_performance_config_dict(self) -> Dict[str, Any]:
        """Get performance monitoring configuration as dictionary"""
        return {
            'metricsCollectionInterval': self.performance.METRICS_COLLECTION_INTERVAL_SECONDS,
            'historyRetentionHours': self.performance.HISTORY_RETENTION_HOURS,
            'alertThresholds': {
                'highLatencyMs': self.performance.HIGH_LATENCY_THRESHOLD_MS,
                'highErrorRate': self.performance.HIGH_ERROR_RATE_THRESHOLD,
                'lowConnectionSuccessRate': self.performance.LOW_CONNECTION_SUCCESS_RATE_THRESHOLD,
                'highMemoryUsageMb': self.performance.HIGH_MEMORY_USAGE_THRESHOLD_MB,
                'maxConnectionDropsPerHour': self.performance.MAX_CONNECTION_DROPS_PER_HOUR
            },
            'alertCooldownMinutes': self.performance.ALERT_COOLDOWN_MINUTES,
            'enableAlertNotifications': self.performance.ENABLE_ALERT_NOTIFICATIONS
        }
    
    def get_streaming_config_dict(self) -> Dict[str, Any]:
        """Get event streaming configuration as dictionary"""
        return {
            'progressThrottleInterval': self.streaming.PROGRESS_THROTTLE_INTERVAL_SECONDS,
            'enableProgressThrottling': self.streaming.ENABLE_PROGRESS_THROTTLING,
            'eventQueueMaxSize': self.streaming.EVENT_QUEUE_MAX_SIZE,
            'eventBatchSize': self.streaming.EVENT_BATCH_SIZE,
            'eventProcessingTimeout': self.streaming.EVENT_PROCESSING_TIMEOUT_SECONDS,
            'eventPriorities': self.streaming.EVENT_PRIORITIES
        }
    
    def get_integration_config_dict(self) -> Dict[str, Any]:
        """Get integration configuration as dictionary"""
        return {
            'backendBaseUrl': self.integration.BACKEND_BASE_URL,
            'frontendBaseUrl': self.integration.FRONTEND_BASE_URL,
            'comfyuiBaseUrl': self.integration.COMFYUI_BASE_URL,
            'websocketUrl': self.integration.WEBSOCKET_URL,
            'apiTimeout': self.integration.API_TIMEOUT_SECONDS,
            'apiRetryAttempts': self.integration.API_RETRY_ATTEMPTS,
            'healthCheckInterval': self.integration.HEALTH_CHECK_INTERVAL_SECONDS,
            'serviceDiscoveryInterval': self.integration.SERVICE_DISCOVERY_INTERVAL_SECONDS,
            'featureFlags': {
                'enablePerformanceMonitoring': self.integration.ENABLE_PERFORMANCE_MONITORING,
                'enableEnhancedWebsocketManager': self.integration.ENABLE_ENHANCED_WEBSOCKET_MANAGER,
                'enableConnectionResilience': self.integration.ENABLE_CONNECTION_RESILIENCE,
                'enableEventStreaming': self.integration.ENABLE_EVENT_STREAMING
            }
        }
    
    def get_complete_config(self) -> Dict[str, Any]:
        """Get complete Phase 4.3 configuration"""
        return {
            'phase': '4.3',
            'version': '1.0.0',
            'timestamp': None,  # Will be set when used
            'websocket': self.get_websocket_config_dict(),
            'performance': self.get_performance_config_dict(),
            'streaming': self.get_streaming_config_dict(),
            'integration': self.get_integration_config_dict()
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration settings"""
        issues = []
        warnings = []
        
        # Validate WebSocket settings
        if self.websocket.RECONNECT_INTERVAL_MS < 500:
            warnings.append("WebSocket reconnect interval is very low (< 500ms)")
        
        if self.websocket.MAX_RECONNECT_ATTEMPTS > 20:
            warnings.append("Max reconnect attempts is very high (> 20)")
        
        if self.websocket.HEARTBEAT_INTERVAL_MS < 10000:
            warnings.append("Heartbeat interval is very low (< 10s)")
        
        # Validate performance settings
        if self.performance.METRICS_COLLECTION_INTERVAL_SECONDS < 1.0:
            warnings.append("Metrics collection interval is very low (< 1s)")
        
        if self.performance.HIGH_LATENCY_THRESHOLD_MS > 5000:
            warnings.append("High latency threshold is very high (> 5s)")
        
        # Validate streaming settings
        if self.streaming.PROGRESS_THROTTLE_INTERVAL_SECONDS < 0.1:
            issues.append("Progress throttle interval is too low (< 0.1s)")
        
        if self.streaming.EVENT_QUEUE_MAX_SIZE < 100:
            warnings.append("Event queue size is very low (< 100)")
        
        # Validate integration settings
        if not self.integration.BACKEND_BASE_URL.startswith(('http://', 'https://')):
            issues.append("Backend base URL must start with http:// or https://")
        
        if not self.integration.WEBSOCKET_URL.startswith(('ws://', 'wss://')):
            issues.append("WebSocket URL must start with ws:// or wss://")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'total_checks': 8,
            'passed_checks': 8 - len(issues) - len(warnings)
        }
    
    def export_frontend_config(self) -> str:
        """Export configuration for frontend consumption (TypeScript format)"""
        config = self.get_complete_config()
        
        # Convert to TypeScript interface format
        ts_config = f"""
// Phase 4.3 Configuration - Auto-generated
// DO NOT MODIFY - This file is generated by Phase4ConfigManager

export interface Phase4Config {{
  websocket: {{
    reconnectInterval: number;
    maxReconnectAttempts: number;
    exponentialBackoffMultiplier: number;
    maxReconnectDelay: number;
    connectionTimeout: number;
    heartbeatInterval: number;
    enableAutoReconnect: boolean;
    enableServiceDiscovery: boolean;
    debugMode: boolean;
  }};
  performance: {{
    metricsCollectionInterval: number;
    alertThresholds: {{
      highLatencyMs: number;
      highErrorRate: number;
      lowConnectionSuccessRate: number;
    }};
  }};
  streaming: {{
    progressThrottleInterval: number;
    enableProgressThrottling: boolean;
    eventQueueMaxSize: number;
  }};
  integration: {{
    backendBaseUrl: string;
    websocketUrl: string;
    apiTimeout: number;
    featureFlags: {{
      enablePerformanceMonitoring: boolean;
      enableEnhancedWebsocketManager: boolean;
      enableConnectionResilience: boolean;
      enableEventStreaming: boolean;
    }};
  }};
}}

export const phase4Config: Phase4Config = {{
  websocket: {{
    reconnectInterval: {config['websocket']['reconnectInterval']},
    maxReconnectAttempts: {config['websocket']['maxReconnectAttempts']},
    exponentialBackoffMultiplier: {config['websocket']['exponentialBackoffMultiplier']},
    maxReconnectDelay: {config['websocket']['maxReconnectDelay']},
    connectionTimeout: {config['websocket']['connectionTimeout']},
    heartbeatInterval: {config['websocket']['heartbeatInterval']},
    enableAutoReconnect: {str(config['websocket']['enableAutoReconnect']).lower()},
    enableServiceDiscovery: {str(config['websocket']['enableServiceDiscovery']).lower()},
    debugMode: {str(config['websocket']['debugMode']).lower()}
  }},
  performance: {{
    metricsCollectionInterval: {config['performance']['metricsCollectionInterval']},
    alertThresholds: {{
      highLatencyMs: {config['performance']['alertThresholds']['highLatencyMs']},
      highErrorRate: {config['performance']['alertThresholds']['highErrorRate']},
      lowConnectionSuccessRate: {config['performance']['alertThresholds']['lowConnectionSuccessRate']}
    }}
  }},
  streaming: {{
    progressThrottleInterval: {config['streaming']['progressThrottleInterval']},
    enableProgressThrottling: {str(config['streaming']['enableProgressThrottling']).lower()},
    eventQueueMaxSize: {config['streaming']['eventQueueMaxSize']}
  }},
  integration: {{
    backendBaseUrl: '{config['integration']['backendBaseUrl']}',
    websocketUrl: '{config['integration']['websocketUrl']}',
    apiTimeout: {config['integration']['apiTimeout']},
    featureFlags: {{
      enablePerformanceMonitoring: {str(config['integration']['featureFlags']['enablePerformanceMonitoring']).lower()},
      enableEnhancedWebsocketManager: {str(config['integration']['featureFlags']['enableEnhancedWebsocketManager']).lower()},
      enableConnectionResilience: {str(config['integration']['featureFlags']['enableConnectionResilience']).lower()},
      enableEventStreaming: {str(config['integration']['featureFlags']['enableEventStreaming']).lower()}
    }}
  }}
}};

export default phase4Config;
"""
        return ts_config

# Global configuration instance
phase4_config = Phase4ConfigManager()

# Export commonly used configurations
WEBSOCKET_CONFIG = phase4_config.get_websocket_config_dict()
PERFORMANCE_CONFIG = phase4_config.get_performance_config_dict()
STREAMING_CONFIG = phase4_config.get_streaming_config_dict()
INTEGRATION_CONFIG = phase4_config.get_integration_config_dict()

# Utility functions
def get_optimized_websocket_settings() -> Dict[str, Any]:
    """Get production-optimized WebSocket settings"""
    return WEBSOCKET_CONFIG

def get_performance_thresholds() -> Dict[str, Any]:
    """Get performance monitoring thresholds"""
    return PERFORMANCE_CONFIG['alertThresholds']

def get_event_streaming_settings() -> Dict[str, Any]:
    """Get event streaming configuration"""
    return STREAMING_CONFIG

def validate_phase4_config() -> bool:
    """Quick validation of Phase 4.3 configuration"""
    validation = phase4_config.validate_config()
    return validation['valid']
