"""
Generation Quality vs Performance Optimizer
Advanced system for balancing image generation quality against performance metrics
"""

import asyncio
import time
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import math

from app.utils.centralized_logger import get_logger, log_activity, log_error

logger = get_logger()

class QualityLevel(Enum):
    DRAFT = "draft"           # Fast, lower quality
    STANDARD = "standard"     # Balanced quality/speed
    HIGH = "high"            # Higher quality, slower
    PREMIUM = "premium"       # Maximum quality, slow
    CUSTOM = "custom"        # User-defined balance

class QualityMetric(Enum):
    VISUAL_FIDELITY = "visual_fidelity"
    DETAIL_PRESERVATION = "detail_preservation"
    COLOR_ACCURACY = "color_accuracy"
    ARTIFACT_REDUCTION = "artifact_reduction"
    CONSISTENCY = "consistency"
    OVERALL_QUALITY = "overall_quality"

class PerformanceMetric(Enum):
    GENERATION_TIME = "generation_time"
    VRAM_USAGE = "vram_usage"
    GPU_UTILIZATION = "gpu_utilization"
    POWER_CONSUMPTION = "power_consumption"
    THERMAL_EFFICIENCY = "thermal_efficiency"

@dataclass
class QualityProfile:
    level: QualityLevel
    model_type: str
    target_quality_score: float      # 0.0 - 1.0
    acceptable_quality_range: Tuple[float, float]  # Min, Max acceptable
    
    # Generation parameters
    steps_range: Tuple[int, int]
    cfg_scale_range: Tuple[float, float]
    sampler_preferences: List[str]
    scheduler_preferences: List[str]
    
    # Quality vs Performance weights
    quality_weight: float             # 0.0 - 1.0 (higher = prioritize quality)
    performance_weight: float         # 0.0 - 1.0 (higher = prioritize speed)
    
    # Thresholds
    max_acceptable_time_seconds: float
    max_acceptable_vram_mb: float
    
    def __post_init__(self):
        # Ensure weights sum to 1.0
        total_weight = self.quality_weight + self.performance_weight
        if total_weight > 0:
            self.quality_weight /= total_weight
            self.performance_weight /= total_weight

@dataclass
class QualityAssessment:
    generation_id: str
    quality_scores: Dict[QualityMetric, float]
    performance_scores: Dict[PerformanceMetric, float]
    overall_quality_score: float
    overall_performance_score: float
    quality_performance_balance: float   # Combined score
    recommendations: List[Dict[str, Any]]
    assessment_time: float

@dataclass
class QualityOptimizationPlan:
    generation_id: str
    current_params: Dict[str, Any]
    optimized_params: Dict[str, Any]
    quality_profile: QualityProfile
    predicted_quality_score: float
    predicted_performance_score: float
    expected_improvement: Dict[str, float]
    optimization_notes: List[str]

class GenerationQualityOptimizer:
    """Advanced quality vs performance optimizer with RTX 4070 Ti SUPER specialization"""
    
    def __init__(self, database_path: str = "data/quality_optimization.db"):
        self.database_path = database_path
        self.quality_profiles = self._initialize_quality_profiles()
        self.assessment_cache: Dict[str, QualityAssessment] = {}
        self._initialize_database()
        
        # RTX 4070 Ti SUPER optimized quality profiles
        self.rtx_4070ti_super_profiles = self._initialize_rtx_profiles()
        
        log_activity("QUALITY_OPTIMIZER", "Quality optimizer initialized")

    def _initialize_database(self):
        """Initialize quality optimization database"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Quality assessments table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS quality_assessments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_id TEXT UNIQUE NOT NULL,
                        model_type TEXT,
                        quality_level TEXT,
                        steps INTEGER,
                        cfg_scale REAL,
                        sampler TEXT,
                        scheduler TEXT,
                        width INTEGER,
                        height INTEGER,
                        visual_fidelity_score REAL,
                        detail_preservation_score REAL,
                        color_accuracy_score REAL,
                        artifact_reduction_score REAL,
                        consistency_score REAL,
                        overall_quality_score REAL,
                        generation_time_seconds REAL,
                        vram_usage_mb REAL,
                        gpu_utilization_percent REAL,
                        overall_performance_score REAL,
                        quality_performance_balance REAL,
                        user_satisfaction_score REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Quality baselines table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS quality_baselines (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_type TEXT NOT NULL,
                        quality_level TEXT NOT NULL,
                        hardware_profile TEXT NOT NULL,
                        baseline_quality_score REAL NOT NULL,
                        baseline_performance_score REAL NOT NULL,
                        optimal_steps INTEGER,
                        optimal_cfg_scale REAL,
                        optimal_sampler TEXT,
                        sample_count INTEGER DEFAULT 1,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(model_type, quality_level, hardware_profile)
                    )
                """)
                
                # Quality preferences table (user preferences)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS quality_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT,
                        model_type TEXT,
                        preferred_quality_level TEXT,
                        quality_weight REAL,
                        performance_weight REAL,
                        max_acceptable_time_seconds REAL,
                        quality_priorities TEXT,  -- JSON array of prioritized metrics
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                
        except Exception as e:
            log_error("QUALITY_OPTIMIZER", "database_init_failed", f"Failed to initialize database: {str(e)}", {}, e)
            raise

    def _initialize_quality_profiles(self) -> Dict[str, QualityProfile]:
        """Initialize standard quality profiles"""
        profiles = {}
        
        # Draft profile - speed focused
        profiles["draft"] = QualityProfile(
            level=QualityLevel.DRAFT,
            model_type="generic",
            target_quality_score=0.65,
            acceptable_quality_range=(0.50, 0.75),
            steps_range=(12, 20),
            cfg_scale_range=(3.0, 6.0),
            sampler_preferences=["euler", "dpm_2"],
            scheduler_preferences=["simple"],
            quality_weight=0.3,
            performance_weight=0.7,
            max_acceptable_time_seconds=15.0,
            max_acceptable_vram_mb=8192
        )
        
        # Standard profile - balanced
        profiles["standard"] = QualityProfile(
            level=QualityLevel.STANDARD,
            model_type="generic",
            target_quality_score=0.80,
            acceptable_quality_range=(0.70, 0.85),
            steps_range=(20, 30),
            cfg_scale_range=(5.0, 9.0),
            sampler_preferences=["euler", "dpm_2", "heun"],
            scheduler_preferences=["normal", "karras"],
            quality_weight=0.5,
            performance_weight=0.5,
            max_acceptable_time_seconds=30.0,
            max_acceptable_vram_mb=12288
        )
        
        # High profile - quality focused
        profiles["high"] = QualityProfile(
            level=QualityLevel.HIGH,
            model_type="generic",
            target_quality_score=0.90,
            acceptable_quality_range=(0.85, 0.95),
            steps_range=(30, 50),
            cfg_scale_range=(6.0, 12.0),
            sampler_preferences=["dpm_2", "heun", "dpmpp_2m"],
            scheduler_preferences=["karras", "exponential"],
            quality_weight=0.7,
            performance_weight=0.3,
            max_acceptable_time_seconds=60.0,
            max_acceptable_vram_mb=16384
        )
        
        # Premium profile - maximum quality
        profiles["premium"] = QualityProfile(
            level=QualityLevel.PREMIUM,
            model_type="generic", 
            target_quality_score=0.95,
            acceptable_quality_range=(0.90, 1.0),
            steps_range=(50, 80),
            cfg_scale_range=(7.0, 15.0),
            sampler_preferences=["dpmpp_2m", "heun", "ddim"],
            scheduler_preferences=["karras", "exponential"],
            quality_weight=0.8,
            performance_weight=0.2,
            max_acceptable_time_seconds=120.0,
            max_acceptable_vram_mb=16384
        )
        
        return profiles

    def _initialize_rtx_profiles(self) -> Dict[str, Dict[str, QualityProfile]]:
        """Initialize RTX 4070 Ti SUPER specific quality profiles"""
        rtx_profiles = {}
        
        # Flux model profiles for RTX 4070 Ti SUPER
        rtx_profiles["flux"] = {
            "draft": QualityProfile(
                level=QualityLevel.DRAFT,
                model_type="flux",
                target_quality_score=0.70,
                acceptable_quality_range=(0.60, 0.80),
                steps_range=(12, 16),
                cfg_scale_range=(2.5, 3.5),
                sampler_preferences=["euler"],
                scheduler_preferences=["simple"],
                quality_weight=0.3,
                performance_weight=0.7,
                max_acceptable_time_seconds=20.0,
                max_acceptable_vram_mb=12288
            ),
            "standard": QualityProfile(
                level=QualityLevel.STANDARD,
                model_type="flux",
                target_quality_score=0.85,
                acceptable_quality_range=(0.75, 0.90),
                steps_range=(18, 25),
                cfg_scale_range=(3.0, 4.0),
                sampler_preferences=["euler", "dpm_2"],
                scheduler_preferences=["simple", "normal"],
                quality_weight=0.5,
                performance_weight=0.5,
                max_acceptable_time_seconds=35.0,
                max_acceptable_vram_mb=14336
            ),
            "high": QualityProfile(
                level=QualityLevel.HIGH,
                model_type="flux",
                target_quality_score=0.92,
                acceptable_quality_range=(0.88, 0.96),
                steps_range=(25, 35),
                cfg_scale_range=(3.5, 4.5),
                sampler_preferences=["dpm_2", "heun"],
                scheduler_preferences=["normal", "karras"],
                quality_weight=0.7,
                performance_weight=0.3,
                max_acceptable_time_seconds=60.0,
                max_acceptable_vram_mb=15360
            )
        }
        
        # SDXL model profiles for RTX 4070 Ti SUPER
        rtx_profiles["sdxl"] = {
            "draft": QualityProfile(
                level=QualityLevel.DRAFT,
                model_type="sdxl",
                target_quality_score=0.72,
                acceptable_quality_range=(0.65, 0.80),
                steps_range=(15, 20),
                cfg_scale_range=(5.0, 7.0),
                sampler_preferences=["euler", "dpm_2"],
                scheduler_preferences=["simple"],
                quality_weight=0.3,
                performance_weight=0.7,
                max_acceptable_time_seconds=15.0,
                max_acceptable_vram_mb=8192
            ),
            "standard": QualityProfile(
                level=QualityLevel.STANDARD,
                model_type="sdxl",
                target_quality_score=0.83,
                acceptable_quality_range=(0.75, 0.88),
                steps_range=(20, 30),
                cfg_scale_range=(6.0, 9.0),
                sampler_preferences=["euler", "dpm_2", "heun"],
                scheduler_preferences=["normal", "karras"],
                quality_weight=0.5,
                performance_weight=0.5,
                max_acceptable_time_seconds=25.0,
                max_acceptable_vram_mb=10240
            ),
            "high": QualityProfile(
                level=QualityLevel.HIGH,
                model_type="sdxl",
                target_quality_score=0.91,
                acceptable_quality_range=(0.85, 0.95),
                steps_range=(30, 45),
                cfg_scale_range=(7.0, 11.0),
                sampler_preferences=["dpm_2", "heun", "dpmpp_2m"],
                scheduler_preferences=["karras", "exponential"],
                quality_weight=0.7,
                performance_weight=0.3,
                max_acceptable_time_seconds=45.0,
                max_acceptable_vram_mb=12288
            )
        }
        
        # SD1.5 model profiles for RTX 4070 Ti SUPER
        rtx_profiles["sd15"] = {
            "draft": QualityProfile(
                level=QualityLevel.DRAFT,
                model_type="sd15",
                target_quality_score=0.68,
                acceptable_quality_range=(0.60, 0.75),
                steps_range=(15, 20),
                cfg_scale_range=(6.0, 8.0),
                sampler_preferences=["euler", "dpm_2"],
                scheduler_preferences=["simple"],
                quality_weight=0.3,
                performance_weight=0.7,
                max_acceptable_time_seconds=10.0,
                max_acceptable_vram_mb=4096
            ),
            "standard": QualityProfile(
                level=QualityLevel.STANDARD,
                model_type="sd15",
                target_quality_score=0.80,
                acceptable_quality_range=(0.72, 0.85),
                steps_range=(20, 30),
                cfg_scale_range=(7.0, 10.0),
                sampler_preferences=["euler", "dpm_2", "heun"],
                scheduler_preferences=["normal", "karras"],
                quality_weight=0.5,
                performance_weight=0.5,
                max_acceptable_time_seconds=15.0,
                max_acceptable_vram_mb=6144
            ),
            "high": QualityProfile(
                level=QualityLevel.HIGH,
                model_type="sd15",
                target_quality_score=0.88,
                acceptable_quality_range=(0.82, 0.92),
                steps_range=(30, 50),
                cfg_scale_range=(8.0, 12.0),
                sampler_preferences=["dpm_2", "heun", "dpmpp_2m"],
                scheduler_preferences=["karras", "exponential"],
                quality_weight=0.7,
                performance_weight=0.3,
                max_acceptable_time_seconds=25.0,
                max_acceptable_vram_mb=8192
            )
        }
        
        return rtx_profiles

    async def optimize_for_quality_balance(self, generation_request: Dict[str, Any],
                                          target_quality_level: Union[QualityLevel, str] = QualityLevel.STANDARD,
                                          user_preferences: Optional[Dict[str, Any]] = None) -> QualityOptimizationPlan:
        """Optimize generation parameters for quality vs performance balance"""
        
        generation_id = generation_request.get("generation_id", "unknown")
        model_type = self._detect_model_type(generation_request.get("model", ""))
        
        log_activity("QUALITY_OPTIMIZER", f"Optimizing quality balance for {generation_id}", {
            "model_type": model_type,
            "target_quality": target_quality_level.value if isinstance(target_quality_level, QualityLevel) else target_quality_level
        })
        
        # Get quality profile
        quality_profile = self._get_quality_profile(model_type, target_quality_level)
        
        # Apply user preferences if provided
        if user_preferences:
            quality_profile = self._apply_user_preferences(quality_profile, user_preferences)
        
        # Get historical quality data
        historical_data = await self._get_historical_quality_data(model_type, target_quality_level)
        
        # Optimize parameters
        optimized_params = await self._optimize_parameters_for_quality(
            generation_request, quality_profile, historical_data
        )
        
        # Predict quality and performance scores
        predicted_quality = await self._predict_quality_score(optimized_params, quality_profile, historical_data)
        predicted_performance = await self._predict_performance_score(optimized_params, quality_profile)
        
        # Calculate expected improvements
        expected_improvement = self._calculate_expected_improvement(
            generation_request, optimized_params, quality_profile
        )
        
        # Generate optimization notes
        optimization_notes = self._generate_quality_optimization_notes(
            generation_request, optimized_params, quality_profile, predicted_quality, predicted_performance
        )
        
        plan = QualityOptimizationPlan(
            generation_id=generation_id,
            current_params=generation_request.copy(),
            optimized_params=optimized_params,
            quality_profile=quality_profile,
            predicted_quality_score=predicted_quality,
            predicted_performance_score=predicted_performance,
            expected_improvement=expected_improvement,
            optimization_notes=optimization_notes
        )
        
        log_activity("QUALITY_OPTIMIZER", f"Quality optimization completed for {generation_id}", {
            "predicted_quality": predicted_quality,
            "predicted_performance": predicted_performance,
            "optimization_count": len([k for k, v in optimized_params.items() if v != generation_request.get(k)])
        })
        
        return plan

    def _detect_model_type(self, model_name: str) -> str:
        """Detect model type from model name"""
        model_lower = model_name.lower()
        
        if "flux" in model_lower:
            return "flux"
        elif "sdxl" in model_lower or "xl" in model_lower:
            return "sdxl"
        elif "sd1" in model_lower or "sd_1" in model_lower or "v1-" in model_lower:
            return "sd15"
        else:
            return "sdxl"  # Default

    def _get_quality_profile(self, model_type: str, quality_level: Union[QualityLevel, str]) -> QualityProfile:
        """Get quality profile for model type and quality level"""
        
        if isinstance(quality_level, str):
            quality_level = QualityLevel(quality_level)
        
        # First try RTX 4070 Ti SUPER specific profiles
        rtx_model_profiles = self.rtx_4070ti_super_profiles.get(model_type, {})
        if quality_level.value in rtx_model_profiles:
            return rtx_model_profiles[quality_level.value]
        
        # Fall back to generic profiles
        return self.quality_profiles.get(quality_level.value, self.quality_profiles["standard"])

    def _apply_user_preferences(self, profile: QualityProfile, user_preferences: Dict[str, Any]) -> QualityProfile:
        """Apply user preferences to quality profile"""
        
        # Create a copy to avoid modifying the original
        modified_profile = QualityProfile(
            level=profile.level,
            model_type=profile.model_type,
            target_quality_score=profile.target_quality_score,
            acceptable_quality_range=profile.acceptable_quality_range,
            steps_range=profile.steps_range,
            cfg_scale_range=profile.cfg_scale_range,
            sampler_preferences=profile.sampler_preferences.copy(),
            scheduler_preferences=profile.scheduler_preferences.copy(),
            quality_weight=user_preferences.get("quality_weight", profile.quality_weight),
            performance_weight=user_preferences.get("performance_weight", profile.performance_weight),
            max_acceptable_time_seconds=user_preferences.get("max_time", profile.max_acceptable_time_seconds),
            max_acceptable_vram_mb=user_preferences.get("max_vram", profile.max_acceptable_vram_mb)
        )
        
        return modified_profile

    async def _optimize_parameters_for_quality(self, generation_request: Dict[str, Any],
                                             quality_profile: QualityProfile,
                                             historical_data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize parameters based on quality profile"""
        
        optimized_params = generation_request.copy()
        
        # Optimize steps
        current_steps = generation_request.get("steps", 25)
        optimal_steps = self._optimize_steps_for_quality(current_steps, quality_profile, historical_data)
        if optimal_steps != current_steps:
            optimized_params["steps"] = optimal_steps
        
        # Optimize CFG scale
        current_cfg = generation_request.get("cfg_scale", 7.0)
        optimal_cfg = self._optimize_cfg_for_quality(current_cfg, quality_profile, historical_data)
        if abs(optimal_cfg - current_cfg) > 0.1:
            optimized_params["cfg_scale"] = optimal_cfg
        
        # Optimize sampler
        current_sampler = generation_request.get("sampler", "euler")
        optimal_sampler = self._optimize_sampler_for_quality(current_sampler, quality_profile, historical_data)
        if optimal_sampler != current_sampler:
            optimized_params["sampler"] = optimal_sampler
        
        # Optimize scheduler
        current_scheduler = generation_request.get("scheduler", "normal")
        optimal_scheduler = self._optimize_scheduler_for_quality(current_scheduler, quality_profile)
        if optimal_scheduler != current_scheduler:
            optimized_params["scheduler"] = optimal_scheduler
        
        # Optimize batch size for quality (usually 1 for highest quality)
        if quality_profile.level in [QualityLevel.HIGH, QualityLevel.PREMIUM]:
            optimized_params["batch_size"] = 1
        
        return optimized_params

    def _optimize_steps_for_quality(self, current_steps: int, profile: QualityProfile, 
                                   historical_data: Dict[str, Any]) -> int:
        """Optimize sampling steps for quality target"""
        
        min_steps, max_steps = profile.steps_range
        
        # Use historical data if available
        if historical_data.get("optimal_steps"):
            historical_optimal = historical_data["optimal_steps"]
            # Bias towards historical data but stay within profile range
            optimal_steps = max(min_steps, min(max_steps, historical_optimal))
        else:
            # Calculate based on quality target
            quality_factor = profile.target_quality_score
            optimal_steps = int(min_steps + (max_steps - min_steps) * quality_factor)
        
        # Apply quality vs performance weighting
        if profile.performance_weight > profile.quality_weight:
            # Bias towards lower steps for performance
            optimal_steps = int(optimal_steps * 0.9)
        elif profile.quality_weight > profile.performance_weight:
            # Bias towards higher steps for quality
            optimal_steps = int(optimal_steps * 1.1)
        
        return max(min_steps, min(max_steps, optimal_steps))

    def _optimize_cfg_for_quality(self, current_cfg: float, profile: QualityProfile,
                                 historical_data: Dict[str, Any]) -> float:
        """Optimize CFG scale for quality target"""
        
        min_cfg, max_cfg = profile.cfg_scale_range
        
        # Use historical data if available
        if historical_data.get("optimal_cfg_scale"):
            return historical_data["optimal_cfg_scale"]
        
        # Calculate based on quality target and model type
        if profile.model_type == "flux":
            # Flux works better with lower CFG
            optimal_cfg = min_cfg + (max_cfg - min_cfg) * 0.3
        else:
            # SDXL and SD1.5 can use higher CFG for quality
            quality_factor = profile.target_quality_score
            optimal_cfg = min_cfg + (max_cfg - min_cfg) * quality_factor
        
        return round(max(min_cfg, min(max_cfg, optimal_cfg)), 1)

    def _optimize_sampler_for_quality(self, current_sampler: str, profile: QualityProfile,
                                     historical_data: Dict[str, Any]) -> str:
        """Optimize sampler for quality target"""
        
        # Use historical data if available
        if historical_data.get("optimal_sampler") and historical_data["optimal_sampler"] in profile.sampler_preferences:
            return historical_data["optimal_sampler"]
        
        # If current sampler is in preferences and quality-focused, keep it
        if current_sampler in profile.sampler_preferences and profile.quality_weight > 0.6:
            return current_sampler
        
        # Return the first preferred sampler (usually the best for this quality level)
        return profile.sampler_preferences[0] if profile.sampler_preferences else current_sampler

    def _optimize_scheduler_for_quality(self, current_scheduler: str, profile: QualityProfile) -> str:
        """Optimize scheduler for quality target"""
        
        if current_scheduler in profile.scheduler_preferences:
            return current_scheduler
        
        return profile.scheduler_preferences[0] if profile.scheduler_preferences else current_scheduler

    async def _predict_quality_score(self, params: Dict[str, Any], profile: QualityProfile,
                                    historical_data: Dict[str, Any]) -> float:
        """Predict quality score for given parameters"""
        
        # Base prediction on profile target
        base_score = profile.target_quality_score
        
        # Adjust based on parameters vs profile optimums
        steps = params.get("steps", 25)
        min_steps, max_steps = profile.steps_range
        steps_factor = min(1.0, steps / max_steps)  # Higher steps = higher quality
        
        cfg_scale = params.get("cfg_scale", 7.0)
        min_cfg, max_cfg = profile.cfg_scale_range
        cfg_factor = min(1.0, (cfg_scale - min_cfg) / (max_cfg - min_cfg))
        
        # Sampler quality factor
        sampler = params.get("sampler", "euler")
        sampler_factor = 1.0
        if sampler in profile.sampler_preferences:
            # Earlier in list = better for this quality level
            sampler_index = profile.sampler_preferences.index(sampler)
            sampler_factor = 1.0 - (sampler_index * 0.05)  # 5% reduction per position
        
        # Combine factors
        predicted_score = base_score * (steps_factor * 0.4 + cfg_factor * 0.3 + sampler_factor * 0.3)
        
        # Use historical data if available for calibration
        if historical_data.get("baseline_quality_score"):
            historical_baseline = historical_data["baseline_quality_score"]
            # Blend prediction with historical data
            predicted_score = predicted_score * 0.7 + historical_baseline * 0.3
        
        return max(0.0, min(1.0, predicted_score))

    async def _predict_performance_score(self, params: Dict[str, Any], profile: QualityProfile) -> float:
        """Predict performance score for given parameters"""
        
        # Base performance score (inverse of quality - higher quality = lower performance)
        base_performance = 1.0 - profile.target_quality_score * 0.8
        
        # Adjust based on parameters
        steps = params.get("steps", 25)
        # More steps = lower performance
        steps_penalty = min(0.5, (steps - 15) * 0.01)  # 1% penalty per step above 15
        
        batch_size = params.get("batch_size", 1)
        batch_bonus = min(0.2, (batch_size - 1) * 0.05)  # 5% bonus per additional batch item
        
        width = params.get("width", 512)
        height = params.get("height", 512)
        resolution_penalty = ((width * height) / (512 * 512) - 1) * 0.1  # 10% penalty per resolution doubling
        
        performance_score = base_performance - steps_penalty + batch_bonus - resolution_penalty
        
        return max(0.0, min(1.0, performance_score))

    def _calculate_expected_improvement(self, original_params: Dict[str, Any], optimized_params: Dict[str, Any],
                                       profile: QualityProfile) -> Dict[str, float]:
        """Calculate expected improvement from optimization"""
        
        improvements = {}
        
        # Steps improvement
        original_steps = original_params.get("steps", 25)
        optimized_steps = optimized_params.get("steps", 25)
        if optimized_steps != original_steps:
            step_improvement = (optimized_steps - original_steps) / original_steps * 100
            improvements["steps_change_percent"] = step_improvement
        
        # CFG improvement  
        original_cfg = original_params.get("cfg_scale", 7.0)
        optimized_cfg = optimized_params.get("cfg_scale", 7.0)
        if abs(optimized_cfg - original_cfg) > 0.1:
            cfg_improvement = (optimized_cfg - original_cfg) / original_cfg * 100
            improvements["cfg_change_percent"] = cfg_improvement
        
        # Expected quality improvement
        quality_improvement = (profile.target_quality_score - 0.7) * 100  # Assume 70% baseline
        improvements["expected_quality_improvement_percent"] = max(0, quality_improvement)
        
        # Expected performance impact
        performance_impact = -1 * quality_improvement * 0.5  # Quality gains cost performance
        improvements["expected_performance_impact_percent"] = performance_impact
        
        return improvements

    def _generate_quality_optimization_notes(self, original_params: Dict[str, Any], optimized_params: Dict[str, Any],
                                           profile: QualityProfile, predicted_quality: float,
                                           predicted_performance: float) -> List[str]:
        """Generate human-readable optimization notes"""
        
        notes = []
        
        # Quality target note
        notes.append(f"Optimized for {profile.level.value} quality (target: {profile.target_quality_score:.1%})")
        
        # Predicted scores
        notes.append(f"Predicted quality score: {predicted_quality:.1%}")
        notes.append(f"Predicted performance score: {predicted_performance:.1%}")
        
        # Parameter changes
        changes = []
        for key in ["steps", "cfg_scale", "sampler", "scheduler"]:
            original_value = original_params.get(key)
            optimized_value = optimized_params.get(key)
            if original_value != optimized_value:
                changes.append(f"{key}: {original_value} → {optimized_value}")
        
        if changes:
            notes.append(f"Parameter changes: {', '.join(changes)}")
        else:
            notes.append("No parameter changes needed - already optimal")
        
        # Quality vs performance balance note
        if profile.quality_weight > profile.performance_weight:
            notes.append("Quality-focused optimization - expect longer generation times")
        elif profile.performance_weight > profile.quality_weight:
            notes.append("Performance-focused optimization - prioritizing speed over quality")
        else:
            notes.append("Balanced optimization - equal weight to quality and performance")
        
        # RTX 4070 Ti SUPER specific notes
        if profile.model_type in ["flux", "sdxl", "sd15"]:
            notes.append("RTX 4070 Ti SUPER optimizations applied")
        
        return notes

    async def _get_historical_quality_data(self, model_type: str, quality_level: Union[QualityLevel, str]) -> Dict[str, Any]:
        """Get historical quality data for calibration"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                quality_level_str = quality_level.value if isinstance(quality_level, QualityLevel) else quality_level
                
                cursor.execute("""
                    SELECT baseline_quality_score, baseline_performance_score, optimal_steps,
                           optimal_cfg_scale, optimal_sampler
                    FROM quality_baselines
                    WHERE model_type = ? AND quality_level = ? AND hardware_profile = ?
                """, (model_type, quality_level_str, "rtx_4070ti_super"))
                
                result = cursor.fetchone()
                
                if result:
                    return {
                        "baseline_quality_score": result[0],
                        "baseline_performance_score": result[1],
                        "optimal_steps": result[2],
                        "optimal_cfg_scale": result[3],
                        "optimal_sampler": result[4]
                    }
                    
        except Exception as e:
            log_error("QUALITY_OPTIMIZER", "historical_data_failed", f"Failed to get historical data: {str(e)}", {}, e)
        
        return {}

    async def assess_generation_quality(self, generation_id: str, generation_params: Dict[str, Any],
                                       actual_performance_metrics: Dict[str, Any],
                                       user_feedback: Optional[Dict[str, Any]] = None) -> QualityAssessment:
        """Assess the quality of a completed generation"""
        
        start_time = time.time()
        
        log_activity("QUALITY_OPTIMIZER", f"Assessing quality for {generation_id}")
        
        # Calculate quality scores (these would ideally use image analysis)
        quality_scores = await self._calculate_quality_scores(generation_params, user_feedback)
        
        # Calculate performance scores
        performance_scores = self._calculate_performance_scores(actual_performance_metrics)
        
        # Calculate overall scores
        overall_quality = sum(quality_scores.values()) / len(quality_scores)
        overall_performance = sum(performance_scores.values()) / len(performance_scores)
        
        # Calculate combined quality-performance balance
        quality_weight = 0.6  # Default weighting
        performance_weight = 0.4
        balance_score = overall_quality * quality_weight + overall_performance * performance_weight
        
        # Generate recommendations
        recommendations = await self._generate_quality_recommendations(
            generation_params, quality_scores, performance_scores, user_feedback
        )
        
        assessment = QualityAssessment(
            generation_id=generation_id,
            quality_scores=quality_scores,
            performance_scores=performance_scores,
            overall_quality_score=overall_quality,
            overall_performance_score=overall_performance,
            quality_performance_balance=balance_score,
            recommendations=recommendations,
            assessment_time=time.time() - start_time
        )
        
        # Cache assessment
        self.assessment_cache[generation_id] = assessment
        
        # Store in database
        await self._store_quality_assessment(assessment, generation_params, actual_performance_metrics)
        
        log_activity("QUALITY_OPTIMIZER", f"Quality assessment completed for {generation_id}", {
            "overall_quality": overall_quality,
            "overall_performance": overall_performance,
            "balance_score": balance_score
        })
        
        return assessment

    async def _calculate_quality_scores(self, generation_params: Dict[str, Any],
                                       user_feedback: Optional[Dict[str, Any]] = None) -> Dict[QualityMetric, float]:
        """Calculate quality scores (would ideally use image analysis)"""
        
        # Placeholder implementation - in reality would analyze generated images
        scores = {}
        
        # Base scores on generation parameters as proxy for quality
        steps = generation_params.get("steps", 25)
        cfg_scale = generation_params.get("cfg_scale", 7.0)
        
        # Visual fidelity (based on steps and CFG)
        scores[QualityMetric.VISUAL_FIDELITY] = min(1.0, (steps / 50) * 0.7 + (cfg_scale / 15) * 0.3)
        
        # Detail preservation (higher steps = better detail)
        scores[QualityMetric.DETAIL_PRESERVATION] = min(1.0, steps / 40)
        
        # Color accuracy (moderate CFG is best)
        optimal_cfg = 8.0
        cfg_accuracy = 1.0 - abs(cfg_scale - optimal_cfg) / optimal_cfg * 0.5
        scores[QualityMetric.COLOR_ACCURACY] = max(0.5, cfg_accuracy)
        
        # Artifact reduction (higher steps = fewer artifacts)
        scores[QualityMetric.ARTIFACT_REDUCTION] = min(1.0, steps / 35)
        
        # Consistency (based on sampler choice)
        sampler = generation_params.get("sampler", "euler")
        consistency_scores = {"euler": 0.85, "dpm_2": 0.80, "heun": 0.90, "dpmpp_2m": 0.88}
        scores[QualityMetric.CONSISTENCY] = consistency_scores.get(sampler, 0.75)
        
        # Use user feedback if available
        if user_feedback:
            user_satisfaction = user_feedback.get("satisfaction_score", 0.5) / 5.0  # Convert 1-5 to 0-1
            # Blend with calculated scores
            for metric in scores:
                scores[metric] = scores[metric] * 0.7 + user_satisfaction * 0.3
        
        # Overall quality
        scores[QualityMetric.OVERALL_QUALITY] = sum(scores.values()) / len(scores)
        
        return scores

    def _calculate_performance_scores(self, performance_metrics: Dict[str, Any]) -> Dict[PerformanceMetric, float]:
        """Calculate performance scores from actual metrics"""
        
        scores = {}
        
        # Generation time (lower is better, normalize to 0-1 scale)
        generation_time = performance_metrics.get("generation_time_seconds", 30.0)
        time_score = max(0.0, 1.0 - (generation_time - 5) / 120)  # 5-125 second range
        scores[PerformanceMetric.GENERATION_TIME] = time_score
        
        # VRAM usage (efficient usage is better)
        vram_usage = performance_metrics.get("vram_usage_mb", 8192)
        max_vram = 16384  # RTX 4070 Ti SUPER
        vram_efficiency = 1.0 - (vram_usage / max_vram) * 0.5  # 50% penalty for full VRAM usage
        scores[PerformanceMetric.VRAM_USAGE] = max(0.0, vram_efficiency)
        
        # GPU utilization (higher is better up to a point)
        gpu_util = performance_metrics.get("gpu_utilization_percent", 80.0) / 100.0
        scores[PerformanceMetric.GPU_UTILIZATION] = min(1.0, gpu_util * 1.2)  # Bonus for >83% utilization
        
        # Power consumption (placeholder - would need actual power data)
        scores[PerformanceMetric.POWER_CONSUMPTION] = 0.8  # Assume reasonable power usage
        
        # Thermal efficiency (lower temps are better)
        gpu_temp = performance_metrics.get("gpu_temperature_celsius", 75.0)
        thermal_score = max(0.0, 1.0 - (gpu_temp - 60) / 40)  # 60-100°C range
        scores[PerformanceMetric.THERMAL_EFFICIENCY] = thermal_score
        
        return scores

    async def _generate_quality_recommendations(self, generation_params: Dict[str, Any],
                                              quality_scores: Dict[QualityMetric, float],
                                              performance_scores: Dict[PerformanceMetric, float],
                                              user_feedback: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Generate recommendations for quality improvement"""
        
        recommendations = []
        
        # Check individual quality metrics
        for metric, score in quality_scores.items():
            if score < 0.7:  # Below acceptable threshold
                if metric == QualityMetric.VISUAL_FIDELITY:
                    recommendations.append({
                        "type": "quality_improvement",
                        "metric": metric.value,
                        "current_score": score,
                        "suggestion": "Increase sampling steps or adjust CFG scale",
                        "priority": "high" if score < 0.5 else "medium"
                    })
                elif metric == QualityMetric.DETAIL_PRESERVATION:
                    recommendations.append({
                        "type": "quality_improvement",
                        "metric": metric.value,
                        "current_score": score,
                        "suggestion": "Increase sampling steps for better detail preservation",
                        "priority": "high" if score < 0.5 else "medium"
                    })
                elif metric == QualityMetric.ARTIFACT_REDUCTION:
                    recommendations.append({
                        "type": "quality_improvement",
                        "metric": metric.value,
                        "current_score": score,
                        "suggestion": "Try a higher quality sampler like 'dpm_2' or 'heun'",
                        "priority": "medium"
                    })
        
        # Check performance metrics
        if performance_scores[PerformanceMetric.GENERATION_TIME] < 0.5:
            recommendations.append({
                "type": "performance_improvement",
                "metric": "generation_time",
                "suggestion": "Consider reducing steps or using a faster sampler for better performance",
                "priority": "medium"
            })
        
        if performance_scores[PerformanceMetric.VRAM_USAGE] < 0.3:
            recommendations.append({
                "type": "performance_warning",
                "metric": "vram_usage",
                "suggestion": "High VRAM usage detected - consider reducing batch size or resolution",
                "priority": "high"
            })
        
        # User feedback based recommendations
        if user_feedback:
            satisfaction = user_feedback.get("satisfaction_score", 3.0)
            if satisfaction < 3.0:  # Below average satisfaction
                recommendations.append({
                    "type": "user_satisfaction",
                    "suggestion": "Consider switching to a higher quality profile for better results",
                    "priority": "high"
                })
        
        return recommendations

    async def _store_quality_assessment(self, assessment: QualityAssessment, 
                                       generation_params: Dict[str, Any],
                                       performance_metrics: Dict[str, Any]):
        """Store quality assessment in database"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                model_type = self._detect_model_type(generation_params.get("model", ""))
                
                cursor.execute("""
                    INSERT OR REPLACE INTO quality_assessments (
                        generation_id, model_type, quality_level, steps, cfg_scale, sampler, scheduler,
                        width, height, visual_fidelity_score, detail_preservation_score, color_accuracy_score,
                        artifact_reduction_score, consistency_score, overall_quality_score,
                        generation_time_seconds, vram_usage_mb, gpu_utilization_percent,
                        overall_performance_score, quality_performance_balance
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    assessment.generation_id, model_type, "unknown",  # quality_level would need to be tracked
                    generation_params.get("steps", 25), generation_params.get("cfg_scale", 7.0),
                    generation_params.get("sampler", "euler"), generation_params.get("scheduler", "normal"),
                    generation_params.get("width", 512), generation_params.get("height", 512),
                    assessment.quality_scores.get(QualityMetric.VISUAL_FIDELITY, 0.0),
                    assessment.quality_scores.get(QualityMetric.DETAIL_PRESERVATION, 0.0),
                    assessment.quality_scores.get(QualityMetric.COLOR_ACCURACY, 0.0),
                    assessment.quality_scores.get(QualityMetric.ARTIFACT_REDUCTION, 0.0),
                    assessment.quality_scores.get(QualityMetric.CONSISTENCY, 0.0),
                    assessment.overall_quality_score,
                    performance_metrics.get("generation_time_seconds", 0.0),
                    performance_metrics.get("vram_usage_mb", 0),
                    performance_metrics.get("gpu_utilization_percent", 0.0),
                    assessment.overall_performance_score,
                    assessment.quality_performance_balance
                ))
                
                conn.commit()
                
        except Exception as e:
            log_error("QUALITY_OPTIMIZER", "assessment_storage_failed", f"Failed to store assessment: {str(e)}", {
                "generation_id": assessment.generation_id
            }, e)

    async def get_quality_optimization_insights(self, days: int = 7) -> Dict[str, Any]:
        """Get quality optimization insights and statistics"""
        
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Get quality statistics
                cursor.execute("""
                    SELECT 
                        model_type,
                        AVG(overall_quality_score) as avg_quality,
                        AVG(overall_performance_score) as avg_performance,
                        AVG(quality_performance_balance) as avg_balance,
                        COUNT(*) as generation_count
                    FROM quality_assessments
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY model_type
                """.format(days))
                
                quality_stats = cursor.fetchall()
                
                # Get quality trends by parameters
                cursor.execute("""
                    SELECT 
                        steps,
                        AVG(overall_quality_score) as avg_quality,
                        COUNT(*) as count
                    FROM quality_assessments
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY steps
                    HAVING count >= 3
                    ORDER BY steps
                """.format(days))
                
                steps_quality = cursor.fetchall()
                
                insights = {
                    "period_days": days,
                    "quality_by_model": {},
                    "quality_trends": {
                        "steps_vs_quality": {}
                    },
                    "recommendations": []
                }
                
                # Process quality by model
                for row in quality_stats:
                    model_type, avg_quality, avg_performance, avg_balance, count = row
                    insights["quality_by_model"][model_type] = {
                        "average_quality_score": avg_quality,
                        "average_performance_score": avg_performance,
                        "average_balance_score": avg_balance,
                        "generation_count": count
                    }
                
                # Process steps vs quality trends
                for row in steps_quality:
                    steps, avg_quality, count = row
                    insights["quality_trends"]["steps_vs_quality"][steps] = {
                        "average_quality": avg_quality,
                        "sample_count": count
                    }
                
                # Generate insights-based recommendations
                insights["recommendations"] = await self._generate_system_quality_recommendations(insights)
                
                return insights
                
        except Exception as e:
            log_error("QUALITY_OPTIMIZER", "insights_failed", f"Failed to generate insights: {str(e)}", {}, e)
            return {"error": str(e)}

    async def _generate_system_quality_recommendations(self, insights: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate system-wide quality recommendations from insights"""
        
        recommendations = []
        
        # Check for models with low quality scores
        for model_type, stats in insights["quality_by_model"].items():
            if stats["average_quality_score"] < 0.75:
                recommendations.append({
                    "type": "model_optimization",
                    "model": model_type,
                    "message": f"Low average quality score ({stats['average_quality_score']:.2f}) for {model_type}",
                    "suggestions": [
                        "Review default parameters for this model",
                        "Consider higher quality profiles by default",
                        "Update quality baselines"
                    ]
                })
        
        # Check for performance vs quality imbalance
        for model_type, stats in insights["quality_by_model"].items():
            if stats["average_performance_score"] < 0.4 and stats["average_quality_score"] > 0.85:
                recommendations.append({
                    "type": "balance_optimization",
                    "model": model_type,
                    "message": f"Quality is high but performance is low for {model_type}",
                    "suggestions": [
                        "Consider more balanced quality profiles",
                        "Optimize for better performance without sacrificing too much quality"
                    ]
                })
        
        return recommendations