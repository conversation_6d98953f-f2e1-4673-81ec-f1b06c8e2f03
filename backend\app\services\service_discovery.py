"""
Enhanced Service Discovery for Backend Middleware
Monitors service health, handles failover, and provides intelligent routing
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from app.core.config import settings, get_service_url, get_websocket_url

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded" 
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ServiceMetrics:
    status: ServiceStatus
    response_time: float = 0.0
    success_rate: float = 0.0
    consecutive_failures: int = 0
    last_check: datetime = field(default_factory=datetime.now)
    error_message: Optional[str] = None
    features: List[str] = field(default_factory=list)

@dataclass
class ServiceDiscoveryResult:
    services: Dict[str, ServiceMetrics]
    recommended_mode: str
    total_healthy: int
    last_update: datetime

class ServiceDiscovery:
    """
    Advanced service discovery with health monitoring, failover, and load balancing
    Integrated with centralized configuration system
    """
    
    def __init__(self):
        self.services: Dict[str, ServiceMetrics] = {}
        self.health_check_interval = 30  # seconds
        self.timeout = 5  # seconds
        self.max_retries = 3
        self.failure_threshold = 3
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        
        # Service definitions with health check endpoints
        self.service_definitions = {
            'comfyui': {
                'url': get_service_url('comfyui'),
                'health_endpoint': '/system_stats',
                'websocket_url': get_websocket_url('comfyui'),
                'required_features': ['object_info', 'queue', 'prompt'],
                'optional_features': ['upload/image', 'view', 'history']
            },
            'ollama': {
                'url': get_service_url('ollama'),
                'health_endpoint': '/api/tags',
                'websocket_url': None,
                'required_features': ['generate', 'chat'],
                'optional_features': ['pull', 'create', 'show']
            }
        }
        
        # Initialize service metrics
        for service_name in self.service_definitions:
            self.services[service_name] = ServiceMetrics(
                status=ServiceStatus.UNKNOWN,
                last_check=datetime.now()
            )
    
    async def start_monitoring(self):
        """Start continuous service monitoring"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        logger.info("Starting service discovery monitoring")
        
        # Initial health check
        await self.check_all_services()
        
        # Start periodic monitoring
        self.monitoring_task = asyncio.create_task(self._monitor_loop())
    
    async def stop_monitoring(self):
        """Stop service monitoring"""
        self.is_monitoring = False
        
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped service discovery monitoring")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self.check_all_services()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Short delay on error
    
    async def check_all_services(self) -> ServiceDiscoveryResult:
        """Check health of all services"""
        tasks = []
        for service_name in self.service_definitions:
            task = asyncio.create_task(self._check_service_health(service_name))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Calculate results
        healthy_count = sum(1 for metrics in self.services.values() 
                          if metrics.status == ServiceStatus.HEALTHY)
        
        recommended_mode = self._determine_recommended_mode()
        
        result = ServiceDiscoveryResult(
            services=self.services.copy(),
            recommended_mode=recommended_mode,
            total_healthy=healthy_count,
            last_update=datetime.now()
        )
        
        logger.info(f"Service health check completed: {healthy_count}/{len(self.services)} healthy")
        return result
    
    async def _check_service_health(self, service_name: str) -> ServiceMetrics:
        """Check health of a specific service"""
        service_def = self.service_definitions[service_name]
        metrics = self.services[service_name]
        
        start_time = datetime.now()
        
        try:
            # Perform health check
            health_url = f"{service_def['url']}{service_def['health_endpoint']}"
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(health_url) as response:
                    response_time = (datetime.now() - start_time).total_seconds()
                    
                    if response.status == 200:
                        # Check service features
                        features = await self._check_service_features(session, service_def)
                        
                        # Update metrics
                        metrics.status = ServiceStatus.HEALTHY
                        metrics.response_time = response_time
                        metrics.consecutive_failures = 0
                        metrics.last_check = datetime.now()
                        metrics.error_message = None
                        metrics.features = features
                        
                        # Update success rate
                        metrics.success_rate = min(100.0, metrics.success_rate + 5.0)
                        
                    else:
                        self._mark_service_failed(metrics, f"HTTP {response.status}", response_time)
        
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            self._mark_service_failed(metrics, str(e), response_time)
        
        return metrics
    
    async def _check_service_features(self, session: aiohttp.ClientSession, service_def: Dict) -> List[str]:
        """Check available features for a service"""
        available_features = []
        base_url = service_def['url']
        
        # Check required features
        for feature in service_def.get('required_features', []):
            try:
                test_url = f"{base_url}/{feature}"
                async with session.get(test_url) as response:
                    if response.status in [200, 404]:  # 404 is acceptable for some endpoints
                        available_features.append(feature)
            except:
                pass
        
        # Check optional features
        for feature in service_def.get('optional_features', []):
            try:
                test_url = f"{base_url}/{feature}"
                async with session.get(test_url) as response:
                    if response.status in [200, 404]:
                        available_features.append(f"{feature} (optional)")
            except:
                pass
        
        return available_features
    
    def _mark_service_failed(self, metrics: ServiceMetrics, error: str, response_time: float):
        """Mark service as failed and update metrics"""
        metrics.consecutive_failures += 1
        metrics.response_time = response_time
        metrics.last_check = datetime.now()
        metrics.error_message = error
        metrics.success_rate = max(0.0, metrics.success_rate - 10.0)
        
        # Determine status based on failure count
        if metrics.consecutive_failures >= self.failure_threshold:
            metrics.status = ServiceStatus.UNHEALTHY
        else:
            metrics.status = ServiceStatus.DEGRADED
    
    def _determine_recommended_mode(self) -> str:
        """Determine the recommended service mode based on health"""
        comfyui_healthy = self.services['comfyui'].status == ServiceStatus.HEALTHY
        ollama_healthy = self.services['ollama'].status == ServiceStatus.HEALTHY
        
        if comfyui_healthy and ollama_healthy:
            return "full_middleware"
        elif comfyui_healthy:
            return "comfyui_only"
        elif ollama_healthy:
            return "ollama_only"
        else:
            return "degraded"
    
    async def get_service_status(self, service_name: str) -> Optional[ServiceMetrics]:
        """Get current status of a specific service"""
        return self.services.get(service_name)
    
    async def is_service_healthy(self, service_name: str) -> bool:
        """Check if a service is currently healthy"""
        metrics = self.services.get(service_name)
        return metrics is not None and metrics.status == ServiceStatus.HEALTHY
    
    async def get_healthy_services(self) -> List[str]:
        """Get list of currently healthy services"""
        return [
            name for name, metrics in self.services.items()
            if metrics.status == ServiceStatus.HEALTHY
        ]
    
    async def get_service_recommendations(self) -> Dict[str, Any]:
        """Get recommendations for service usage"""
        recommendations = {
            "recommended_mode": self._determine_recommended_mode(),
            "service_status": {},
            "routing_recommendations": {},
            "performance_tips": []
        }
        
        for service_name, metrics in self.services.items():
            recommendations["service_status"][service_name] = {
                "status": metrics.status.value,
                "response_time": metrics.response_time,
                "success_rate": metrics.success_rate,
                "last_check": metrics.last_check.isoformat(),
                "available_features": metrics.features
            }
        
        # Generate routing recommendations
        if self.services['comfyui'].status == ServiceStatus.HEALTHY:
            recommendations["routing_recommendations"]["image_generation"] = "route_to_comfyui"
        else:
            recommendations["routing_recommendations"]["image_generation"] = "service_unavailable"
            
        if self.services['ollama'].status == ServiceStatus.HEALTHY:
            recommendations["routing_recommendations"]["llm_requests"] = "route_to_ollama"
        else:
            recommendations["routing_recommendations"]["llm_requests"] = "service_unavailable"
        
        # Performance tips
        if self.services['comfyui'].response_time > 2.0:
            recommendations["performance_tips"].append("ComfyUI response time is high - consider checking GPU utilization")
            
        if any(m.consecutive_failures > 0 for m in self.services.values()):
            recommendations["performance_tips"].append("Some services have recent failures - monitor error logs")
        
        return recommendations
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary"""
        total_services = len(self.services)
        healthy_services = sum(1 for m in self.services.values() if m.status == ServiceStatus.HEALTHY)
        
        avg_response_time = sum(m.response_time for m in self.services.values()) / total_services if total_services > 0 else 0
        avg_success_rate = sum(m.success_rate for m in self.services.values()) / total_services if total_services > 0 else 0
        
        return {
            "overall_health": "healthy" if healthy_services == total_services else "degraded",
            "healthy_services": healthy_services,
            "total_services": total_services,
            "average_response_time": round(avg_response_time, 3),
            "average_success_rate": round(avg_success_rate, 2),
            "recommended_mode": self._determine_recommended_mode(),
            "last_update": max(m.last_check for m in self.services.values()).isoformat()
        }

# Global service discovery instance
service_discovery = ServiceDiscovery()