"""
WebSocket Relay Service
Provides bidirectional WebSocket communication between frontend and ComfyUI
Enhanced with centralized configuration and service discovery
"""

import asyncio
import websockets
import json
import logging
from typing import Dict, Set, Optional, Any, List
from fastapi import WebSocket
import uuid
from datetime import datetime
from app.core.config import settings, get_websocket_url

logger = logging.getLogger(__name__)

class WebSocketRelay:
    """
    WebSocket relay that connects frontend clients to ComfyUI backend
    Handles message routing, connection management, and error recovery
    """
    
    def __init__(self, comfyui_ws_url: Optional[str] = None):
        # Use centralized configuration with fallback
        self.comfyui_ws_url = comfyui_ws_url or get_websocket_url('comfyui')
        self.frontend_clients: Set[WebSocket] = set()
        self.comfyui_ws: Optional[websockets.WebSocketServerProtocol] = None
        self.client_id = str(uuid.uuid4())
        self.is_connecting_to_comfyui = False
        self.reconnect_task: Optional[asyncio.Task] = None
        self.last_progress_broadcast = {}  # Track last broadcast time per generation
        self.progress_throttle_seconds = 2.0  # Minimum seconds between progress updates
        
    async def start(self):
        """Start the relay service"""
        logger.info("Starting WebSocket relay service")
        await self.connect_to_comfyui()
        
    async def stop(self):
        """Stop the relay service"""
        logger.info("Stopping WebSocket relay service")
        
        # Cancel reconnection task
        if self.reconnect_task and not self.reconnect_task.done():
            self.reconnect_task.cancel()
            
        # Close ComfyUI connection
        if self.comfyui_ws:
            await self.comfyui_ws.close()
            
        # Close all frontend connections
        for client in self.frontend_clients.copy():
            await client.close()
            
    async def connect_to_comfyui(self) -> bool:
        """Establish connection to ComfyUI WebSocket"""
        if self.is_connecting_to_comfyui:
            return False
            
        self.is_connecting_to_comfyui = True
        
        try:
            logger.info(f"Connecting to ComfyUI at {self.comfyui_ws_url}")
            
            # Add client_id to ComfyUI WebSocket URL
            ws_url_with_client = f"{self.comfyui_ws_url}?clientId={self.client_id}"
            
            self.comfyui_ws = await websockets.connect(
                ws_url_with_client,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10
            )
            
            logger.info("Successfully connected to ComfyUI WebSocket")
            
            # Start listening to ComfyUI messages
            asyncio.create_task(self.listen_to_comfyui())
            
            # Notify frontend clients about connection status
            await self.broadcast_to_frontend({
                "type": "connection_status",
                "data": {
                    "comfyui": "connected",
                    "timestamp": asyncio.get_event_loop().time()
                }
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to ComfyUI: {e}")
            
            # Notify frontend clients about connection failure
            await self.broadcast_to_frontend({
                "type": "connection_status",
                "data": {
                    "comfyui": "disconnected",
                    "error": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                }
            })
            
            # Schedule reconnection
            self.schedule_reconnection()
            return False
            
        finally:
            self.is_connecting_to_comfyui = False
    
    def schedule_reconnection(self, delay: float = 5.0):
        """Schedule automatic reconnection to ComfyUI"""
        if self.reconnect_task and not self.reconnect_task.done():
            return  # Already scheduled
            
        async def reconnect():
            await asyncio.sleep(delay)
            await self.connect_to_comfyui()
            
        self.reconnect_task = asyncio.create_task(reconnect())
        
    async def listen_to_comfyui(self):
        """Listen for messages from ComfyUI and relay to frontend clients"""
        try:
            async for message in self.comfyui_ws:
                try:
                    # Parse ComfyUI message
                    if isinstance(message, str):
                        data = json.loads(message)
                    else:
                        # Handle binary messages (images, etc.)
                        data = {
                            "type": "binary_data",
                            "data": message.hex(),  # Convert binary to hex string
                            "size": len(message)
                        }
                    
                    # Process ComfyUI progress messages
                    await self.process_comfyui_message(data)
                    
                    # Add relay metadata
                    relay_message = {
                        "type": "comfyui_message", 
                        "source": "comfyui",
                        "data": data,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                    
                    # Relay to all frontend clients
                    await self.broadcast_to_frontend(relay_message)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse ComfyUI message: {e}")
                except Exception as e:
                    logger.error(f"Error processing ComfyUI message: {e}")
                    
        except websockets.ConnectionClosed:
            logger.warning("ComfyUI WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error listening to ComfyUI: {e}")
        finally:
            # Connection lost, schedule reconnection
            logger.info("ComfyUI connection lost, scheduling reconnection")
            await self.broadcast_to_frontend({
                "type": "connection_status",
                "data": {
                    "comfyui": "disconnected",
                    "timestamp": asyncio.get_event_loop().time()
                }
            })
            self.schedule_reconnection()
    
    async def add_frontend_client(self, websocket: WebSocket):
        """Add frontend client to relay"""
        # WebSocket should already be accepted by the main endpoint
        # Don't call websocket.accept() here as it's already been accepted
        self.frontend_clients.add(websocket)
        
        logger.info(f"Frontend client connected. Total clients: {len(self.frontend_clients)}")
        
        # Send current connection status to new client
        comfyui_status = "connected" if (self.comfyui_ws and not self.comfyui_ws.closed) else "disconnected"
        await websocket.send_text(json.dumps({
            "type": "connection_status",
            "data": {
                "comfyui": comfyui_status,
                "client_id": self.client_id,
                "timestamp": asyncio.get_event_loop().time()
            }
        }))
        
    async def remove_frontend_client(self, websocket: WebSocket):
        """Remove frontend client from relay"""
        self.frontend_clients.discard(websocket)
        logger.info(f"Frontend client disconnected. Total clients: {len(self.frontend_clients)}")
        
    async def handle_frontend_message(self, websocket: WebSocket, message: str):
        """Handle message from frontend client"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "ping":
                # Respond to ping with pong
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": data.get("timestamp", asyncio.get_event_loop().time())
                }))
                
            elif message_type == "comfyui_command":
                # Forward command to ComfyUI
                await self.send_to_comfyui(data.get("data", {}))
                
            elif message_type == "request_status":
                # Send current status
                comfyui_status = "connected" if (self.comfyui_ws and not self.comfyui_ws.closed) else "disconnected"
                await websocket.send_text(json.dumps({
                    "type": "connection_status",
                    "data": {
                        "comfyui": comfyui_status,
                        "client_id": self.client_id,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                }))
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse frontend message: {e}")
        except Exception as e:
            logger.error(f"Error handling frontend message: {e}")
    
    async def send_to_comfyui(self, data: Any):
        """Send message to ComfyUI"""
        if not self.comfyui_ws or self.comfyui_ws.closed:
            logger.warning("Cannot send to ComfyUI: connection not available")
            return False
            
        try:
            message = json.dumps(data)
            await self.comfyui_ws.send(message)
            return True
        except Exception as e:
            logger.error(f"Failed to send message to ComfyUI: {e}")
            return False
    
    async def broadcast_to_frontend(self, message: Dict[str, Any]):
        """Send message to all frontend clients"""
        if not self.frontend_clients:
            return

        message_str = json.dumps(message)
        disconnected_clients = set()

        for client in self.frontend_clients:
            try:
                # Check WebSocket connection state before sending
                from starlette.websockets import WebSocketState
                if hasattr(client, 'client_state') and client.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"Frontend client not connected (state: {client.client_state})")
                    disconnected_clients.add(client)
                    continue
                elif hasattr(client, 'application_state') and client.application_state != WebSocketState.CONNECTED:
                    logger.warning(f"Frontend client not connected (app_state: {client.application_state})")
                    disconnected_clients.add(client)
                    continue

                await client.send_text(message_str)
            except Exception as e:
                logger.error(f"Failed to send message to frontend client: {e}")
                disconnected_clients.add(client)

        # Remove disconnected clients
        for client in disconnected_clients:
            self.frontend_clients.discard(client)

        if disconnected_clients:
            logger.info(f"Removed {len(disconnected_clients)} disconnected clients")
    
    async def broadcast_generation_progress(self, generation_id: str, state: str, progress: float = 0.0, 
                                          substage: str = None, message: str = None, error: str = None,
                                          results: Dict[str, Any] = None, metadata: Dict[str, Any] = None):
        """Broadcast generation progress to all frontend clients with throttling"""
        
        # Throttle progress updates to reduce spam
        now = datetime.now()
        last_broadcast = self.last_progress_broadcast.get(generation_id)
        
        # Always broadcast state changes and completion, throttle progress updates
        should_broadcast = (
            state in ['completed', 'failed', 'cancelled'] or
            progress == 0.0 or
            progress >= 100.0 or
            last_broadcast is None or
            (now - last_broadcast).total_seconds() >= self.progress_throttle_seconds
        )
        
        if not should_broadcast:
            return
            
        self.last_progress_broadcast[generation_id] = now
        
        progress_message = {
            "type": "generation_progress",
            "data": {
                "generation_id": generation_id,
                "state": state,
                "progress": progress,
                "substage": substage,
                "message": message,
                "error": error,
                "results": results,
                "metadata": metadata or {},
                "timestamp": now.isoformat()
            }
        }
        
        # Add optimization data if available
        try:
            from app.services.image_generation_optimizer import ImageGenerationOptimizer
            image_optimizer = ImageGenerationOptimizer()
            
            optimization_data = image_optimizer.active_optimizations.get(generation_id)
            if optimization_data:
                metrics = optimization_data["metrics"]
                progress_message["data"]["performance_metrics"] = {
                    "vram_usage_mb": metrics.vram_usage_mb,
                    "gpu_utilization_percent": metrics.gpu_utilization_percent,
                    "gpu_temperature_celsius": metrics.gpu_temperature_celsius,
                    "cpu_utilization_percent": metrics.cpu_utilization_percent,
                    "optimization_strategy": metrics.optimization_strategy,
                    "elapsed_time": now.timestamp() - metrics.start_time
                }
                
                # Add real-time recommendations
                current_metrics = await image_optimizer._collect_system_metrics()
                if current_metrics:
                    recommendations = await image_optimizer._analyze_real_time_performance(generation_id, current_metrics)
                    if recommendations:
                        progress_message["data"]["optimization_recommendations"] = recommendations
                        
        except Exception as e:
            # Don't fail the broadcast if optimization data is unavailable
            logger.debug(f"Could not add optimization data to progress: {str(e)}")
        
        # Only log significant state changes
        if state in ['completed', 'failed', 'cancelled'] or progress == 0.0 or progress >= 100.0:
            logger.info(f"Generation progress: {generation_id} - {state} ({progress:.1f}%)")
        
        await self.broadcast_to_frontend(progress_message)
    
    async def process_comfyui_message(self, data: Dict[str, Any]):
        """Process ComfyUI messages for progress and state updates"""
        try:
            message_type = data.get("type")
            
            if message_type == "progress":
                # ComfyUI progress update
                value = data.get("value", 0)
                max_val = data.get("max", 1)
                node = data.get("node")
                
                if max_val > 0:
                    progress_percent = min(95, max(5, int((value / max_val) * 100)))
                    
                    # Try to find which generation this belongs to
                    # For now, broadcast to all active generations
                    progress_message = {
                        "type": "comfyui_progress",
                        "data": {
                            "progress": progress_percent,
                            "value": value,
                            "max": max_val,
                            "node": node,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    
                    # Add real-time system metrics to ComfyUI progress
                    try:
                        from app.services.image_generation_optimizer import ImageGenerationOptimizer
                        image_optimizer = ImageGenerationOptimizer()
                        
                        current_metrics = await image_optimizer._collect_system_metrics()
                        if current_metrics:
                            progress_message["data"]["system_metrics"] = {
                                "gpu_utilization": current_metrics.get("gpu_utilization"),
                                "vram_usage_mb": current_metrics.get("vram_usage_mb"),
                                "gpu_temperature": current_metrics.get("gpu_temperature"),
                                "cpu_utilization": current_metrics.get("cpu_utilization")
                            }
                    except Exception as e:
                        logger.debug(f"Could not add system metrics to ComfyUI progress: {str(e)}")
                    await self.broadcast_to_frontend(progress_message)
                    
            elif message_type == "execution_start":
                # Execution started
                prompt_id = data.get("prompt_id")
                if prompt_id:
                    start_message = {
                        "type": "comfyui_execution_start",
                        "data": {
                            "prompt_id": prompt_id,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await self.broadcast_to_frontend(start_message)
                    
            elif message_type == "execution_success":
                # Execution completed
                prompt_id = data.get("prompt_id")
                if prompt_id:
                    complete_message = {
                        "type": "comfyui_execution_complete",
                        "data": {
                            "prompt_id": prompt_id,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await self.broadcast_to_frontend(complete_message)
                    
            elif message_type == "execution_error":
                # Execution failed
                prompt_id = data.get("prompt_id")
                error = data.get("exception_message", "Unknown error")
                if prompt_id:
                    error_message = {
                        "type": "comfyui_execution_error",
                        "data": {
                            "prompt_id": prompt_id,
                            "error": error,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await self.broadcast_to_frontend(error_message)
                    
        except Exception as e:
            logger.error(f"Error processing ComfyUI message: {e}")

    async def broadcast_generation_state(self, generation_id: str, state: str, data: Dict[str, Any] = None):
        """Broadcast generation state change to all frontend clients"""
        state_message = {
            "type": "generation_state",
            "data": {
                "generation_id": generation_id,
                "state": state,
                "data": data or {},
                "timestamp": datetime.now().isoformat()
            }
        }
        
        logger.info(f"Broadcasting generation state: {generation_id} - {state}")
        await self.broadcast_to_frontend(state_message)
    
    async def broadcast_generation_error(self, generation_id: str, error: str, error_code: str = None,
                                       details: Dict[str, Any] = None):
        """Broadcast generation error to all frontend clients"""
        error_message = {
            "type": "generation_error",
            "data": {
                "generation_id": generation_id,
                "error": error,
                "error_code": error_code,
                "details": details or {},
                "timestamp": datetime.now().isoformat()
            }
        }
        
        logger.error(f"Broadcasting generation error: {generation_id} - {error}")
        await self.broadcast_to_frontend(error_message)
    
    async def broadcast_generation_complete(self, generation_id: str, results: Dict[str, Any],
                                          metadata: Dict[str, Any] = None):
        """Broadcast generation completion to all frontend clients"""
        complete_message = {
            "type": "generation_complete",
            "data": {
                "generation_id": generation_id,
                "results": results,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat()
            }
        }
        
        logger.info(f"Broadcasting generation complete: {generation_id}")
        await self.broadcast_to_frontend(complete_message)
    
    async def broadcast_system_status(self, status: str, data: Dict[str, Any] = None):
        """Broadcast system status updates to all frontend clients"""
        status_message = {
            "type": "system_status",
            "data": {
                "status": status,
                "data": data or {},
                "timestamp": datetime.now().isoformat()
            }
        }
        
        logger.info(f"Broadcasting system status: {status}")
        await self.broadcast_to_frontend(status_message)
    
    def get_client_count(self) -> int:
        """Get number of connected frontend clients"""
        return len(self.frontend_clients)
    
    def is_comfyui_connected(self) -> bool:
        """Check if ComfyUI is connected"""
        return self.comfyui_ws is not None and not self.comfyui_ws.closed

# Global relay instance
websocket_relay = WebSocketRelay()
