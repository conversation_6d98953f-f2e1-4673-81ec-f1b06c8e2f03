#!/usr/bin/env python3
"""
Personal Style Analysis for Handpicked Images
Creates a personalized style config based on user's curated image collection

This script analyzes the user's handpicked images to:
1. Identify their personal aesthetic preferences  
2. Extract common visual patterns and themes
3. Generate a personalized style guide/config
4. Create replication instructions for their preferred style
5. Build a comprehensive personal style database
"""

import asyncio
import json
import numpy as np
from datetime import datetime
from pathlib import Path
from PIL import Image, ImageStat
from collections import Counter, defaultdict
from typing import Dict, List, Any, Tuple
import colorsys
import statistics
import hashlib

class PersonalStyleAnalyzer:
    """Enhanced analyzer focused on personal style preference extraction."""
    
    def __init__(self, target_folder: str, verified_workflows_folder: str):
        self.target_folder = Path(target_folder)
        self.verified_workflows_folder = Path(verified_workflows_folder)
        
        # Personal style tracking
        self.personal_style_data = {
            "color_preferences": [],
            "composition_patterns": [],
            "subject_preferences": [],
            "lighting_preferences": [],
            "texture_preferences": [],
            "workflow_patterns": []
        }
        
        # Analysis results
        self.image_analyses = []
        self.style_patterns = {}
        self.personal_config = {}
        
        print("[PERSONAL STYLE] Analyzing handpicked images for personal style preferences...")
        print(f"[NOTE] User Context: These images represent the user's preferred aesthetic style")
    
    async def analyze_personal_style(self):
        """Comprehensive personal style analysis of handpicked images."""
        
        # Get all images
        image_files = list(self.target_folder.glob("*.png")) + list(self.target_folder.glob("*.jpg")) + list(self.target_folder.glob("*.jpeg"))
        print(f"[ANALYSIS] Found {len(image_files)} handpicked images to analyze")
        
        # Analyze each image for personal preferences
        for i, image_path in enumerate(image_files, 1):
            print(f"[STYLE ANALYSIS] Processing image {i}/{len(image_files)}: {image_path.name}")
            
            analysis = await self.analyze_image_for_personal_style(image_path)
            self.image_analyses.append(analysis)
            
            # Update personal style data
            self.update_personal_style_tracking(analysis)
        
        # Extract overall style patterns
        await self.extract_personal_style_patterns()
        
        # Generate personal style config
        await self.generate_personal_style_config()
        
        # Create personal style guide
        await self.create_personal_style_guide()
        
        print(f"[COMPLETE] Personal style analysis finished!")
        return True
    
    async def analyze_image_for_personal_style(self, image_path: Path) -> Dict[str, Any]:
        """Analyze single image for personal style characteristics."""
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB for analysis
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Get basic info
                width, height = img.size
                aspect_ratio = width / height
                
                # Color analysis - this is key for personal style
                color_analysis = await self.analyze_color_preferences(img)
                
                # Composition analysis
                composition_analysis = await self.analyze_composition_style(img, aspect_ratio)
                
                # Lighting analysis
                lighting_analysis = await self.analyze_lighting_preferences(img)
                
                # Extract workflow if available
                workflow_info = await self.extract_workflow_data(image_path)
                
                return {
                    "filename": image_path.name,
                    "dimensions": f"{width}x{height}",
                    "aspect_ratio": round(aspect_ratio, 2),
                    "color_preferences": color_analysis,
                    "composition_style": composition_analysis,
                    "lighting_preferences": lighting_analysis,
                    "workflow_info": workflow_info,
                    "personal_style_score": await self.calculate_style_consistency(color_analysis, composition_analysis, lighting_analysis)
                }
        
        except Exception as e:
            return {"filename": image_path.name, "error": str(e)}
    
    async def analyze_color_preferences(self, img: Image.Image) -> Dict[str, Any]:
        """Deep color preference analysis."""
        
        # Get all colors and their frequency
        colors = img.getcolors(maxcolors=256*256*256)
        if not colors:
            return {"error": "Could not analyze colors"}
        
        # Sort by frequency
        colors.sort(key=lambda x: x[0], reverse=True)
        
        # Extract dominant colors (top 10)
        dominant_colors = []
        for count, (r, g, b) in colors[:10]:
            hex_color = f"#{r:02x}{g:02x}{b:02x}"
            
            # Convert to HSV for better analysis
            h, s, v = colorsys.rgb_to_hsv(r/255, g/255, b/255)
            
            dominant_colors.append({
                "hex": hex_color,
                "rgb": [r, g, b],
                "hsv": [round(h*360), round(s*100), round(v*100)],
                "frequency": count,
                "percentage": round(count / sum(c[0] for c in colors) * 100, 2)
            })
        
        # Analyze color temperature
        avg_temp = self.calculate_color_temperature(dominant_colors[:5])
        
        # Analyze saturation preferences
        avg_saturation = statistics.mean([c["hsv"][1] for c in dominant_colors[:5]])
        
        # Analyze brightness preferences  
        avg_brightness = statistics.mean([c["hsv"][2] for c in dominant_colors[:5]])
        
        # Color harmony analysis
        harmony_type = self.analyze_color_harmony([c["hsv"] for c in dominant_colors[:5]])
        
        return {
            "dominant_colors": dominant_colors,
            "color_temperature": avg_temp,
            "saturation_preference": round(avg_saturation, 1),
            "brightness_preference": round(avg_brightness, 1),
            "harmony_type": harmony_type,
            "palette_complexity": len([c for c in dominant_colors if c["percentage"] > 5])
        }
    
    def calculate_color_temperature(self, colors: List[Dict]) -> str:
        """Calculate if user prefers warm/cool colors."""
        warm_count = 0
        cool_count = 0
        
        for color in colors:
            hue = color["hsv"][0]
            if 0 <= hue <= 60 or 300 <= hue <= 360:  # Reds, oranges, magentas
                warm_count += color["percentage"]
            elif 120 <= hue <= 240:  # Blues, cyans, greens
                cool_count += color["percentage"]
        
        if warm_count > cool_count * 1.2:
            return "warm"
        elif cool_count > warm_count * 1.2:
            return "cool"
        else:
            return "neutral"
    
    def analyze_color_harmony(self, hsv_colors: List[List]) -> str:
        """Analyze what type of color harmony the user prefers."""
        if len(hsv_colors) < 3:
            return "monochromatic"
        
        hues = [c[0] for c in hsv_colors]
        hue_spread = max(hues) - min(hues)
        
        if hue_spread < 30:
            return "monochromatic"
        elif hue_spread < 90:
            return "analogous"  
        elif any(abs(h1 - h2) > 150 for h1 in hues for h2 in hues if h1 != h2):
            return "complementary"
        else:
            return "triadic"
    
    async def analyze_composition_style(self, img: Image.Image, aspect_ratio: float) -> Dict[str, Any]:
        """Analyze user's composition preferences."""
        
        width, height = img.size
        
        # Convert to grayscale for composition analysis
        gray = img.convert('L')
        pixels = np.array(gray)
        
        # Rule of thirds analysis
        third_w, third_h = width // 3, height // 3
        
        # Check for focal points near rule of thirds intersections
        intersections = [
            (third_w, third_h), (2*third_w, third_h),
            (third_w, 2*third_h), (2*third_w, 2*third_h)
        ]
        
        # Simple center vs off-center analysis
        center_region = pixels[height//3:2*height//3, width//3:2*width//3]
        center_brightness = np.mean(center_region)
        overall_brightness = np.mean(pixels)
        
        # Analyze if composition is center-focused or follows rule of thirds
        composition_type = "centered" if center_brightness > overall_brightness * 1.1 else "rule_of_thirds"
        
        # Aspect ratio preference
        aspect_category = "square" if 0.9 <= aspect_ratio <= 1.1 else \
                         "portrait" if aspect_ratio < 0.9 else "landscape"
        
        return {
            "aspect_ratio": aspect_ratio,
            "aspect_preference": aspect_category,
            "composition_type": composition_type,
            "center_focus_strength": round(center_brightness / overall_brightness, 2),
            "overall_brightness": round(overall_brightness, 1)
        }
    
    async def analyze_lighting_preferences(self, img: Image.Image) -> Dict[str, Any]:
        """Analyze lighting style preferences."""
        
        # Convert to grayscale for lighting analysis
        gray = img.convert('L')
        pixels = np.array(gray)
        
        # Overall brightness
        avg_brightness = np.mean(pixels)
        
        # Contrast analysis  
        contrast = np.std(pixels)
        
        # Highlight/shadow distribution
        highlights = np.sum(pixels > 200) / pixels.size
        shadows = np.sum(pixels < 55) / pixels.size
        
        # Lighting style classification
        if avg_brightness > 180:
            lighting_style = "high_key"
        elif avg_brightness < 75:
            lighting_style = "low_key"
        else:
            lighting_style = "balanced"
        
        # Contrast preference
        if contrast > 80:
            contrast_style = "high_contrast"
        elif contrast < 40:
            contrast_style = "low_contrast"
        else:
            contrast_style = "moderate_contrast"
        
        return {
            "lighting_style": lighting_style,
            "contrast_style": contrast_style,
            "avg_brightness": round(avg_brightness, 1),
            "contrast_value": round(contrast, 1),
            "highlight_percentage": round(highlights * 100, 1),
            "shadow_percentage": round(shadows * 100, 1)
        }
    
    async def extract_workflow_data(self, image_path: Path) -> Dict[str, Any]:
        """Extract workflow information if available."""
        
        if image_path.suffix.lower() != '.png':
            return {"workflow_available": False}
        
        try:
            with Image.open(image_path) as img:
                if hasattr(img, 'text') and img.text:
                    workflow_fields = ['workflow', 'Workflow', 'ComfyUI_workflow']
                    for field in workflow_fields:
                        if field in img.text:
                            try:
                                workflow_json = json.loads(img.text[field])
                                
                                # Analyze workflow for style preferences
                                model_info = self.extract_model_info(workflow_json)
                                settings_info = self.extract_settings_info(workflow_json)
                                
                                return {
                                    "workflow_available": True,
                                    "model_info": model_info,
                                    "settings_info": settings_info,
                                    "node_count": len(workflow_json.get('nodes', []))
                                }
                            except json.JSONDecodeError:
                                continue
        
        except Exception:
            pass
        
        return {"workflow_available": False}
    
    def extract_model_info(self, workflow: Dict) -> Dict[str, Any]:
        """Extract model information from workflow."""
        
        models = {"checkpoint": None, "lora": [], "controlnet": [], "other": []}
        
        if isinstance(workflow.get('nodes'), list):
            for node in workflow['nodes']:
                if isinstance(node, dict) and 'type' in node:
                    node_type = node['type'].lower()
                    
                    if 'checkpoint' in node_type or 'load_checkpoint' in node_type:
                        if 'inputs' in node and 'ckpt_name' in node['inputs']:
                            models["checkpoint"] = node['inputs']['ckpt_name']
                    elif 'lora' in node_type:
                        if 'inputs' in node and 'lora_name' in node['inputs']:
                            models["lora"].append(node['inputs']['lora_name'])
                    elif 'controlnet' in node_type:
                        models["controlnet"].append(node_type)
        
        return models
    
    def extract_settings_info(self, workflow: Dict) -> Dict[str, Any]:
        """Extract generation settings from workflow."""
        
        settings = {"steps": None, "cfg": None, "sampler": None, "scheduler": None}
        
        if isinstance(workflow.get('nodes'), list):
            for node in workflow['nodes']:
                if isinstance(node, dict) and 'inputs' in node:
                    inputs = node['inputs']
                    if 'steps' in inputs:
                        settings["steps"] = inputs['steps']
                    if 'cfg' in inputs:
                        settings["cfg"] = inputs['cfg']  
                    if 'sampler_name' in inputs:
                        settings["sampler"] = inputs['sampler_name']
                    if 'scheduler' in inputs:
                        settings["scheduler"] = inputs['scheduler']
        
        return settings
    
    async def calculate_style_consistency(self, color_analysis: Dict, composition_analysis: Dict, lighting_analysis: Dict) -> float:
        """Calculate how well this image fits with the overall style."""
        
        # This would compare against accumulated preferences
        # For now, return a base score
        return 85.0
    
    def update_personal_style_tracking(self, analysis: Dict):
        """Update personal style preferences based on image analysis."""
        
        if "error" in analysis:
            return
        
        # Track color preferences
        if "color_preferences" in analysis:
            color_prefs = analysis["color_preferences"]
            self.personal_style_data["color_preferences"].append({
                "temperature": color_prefs.get("color_temperature"),
                "saturation": color_prefs.get("saturation_preference"),
                "brightness": color_prefs.get("brightness_preference"),
                "harmony": color_prefs.get("harmony_type"),
                "dominant_colors": color_prefs.get("dominant_colors", [])[:3]
            })
        
        # Track composition preferences
        if "composition_style" in analysis:
            comp = analysis["composition_style"]
            self.personal_style_data["composition_patterns"].append({
                "aspect_preference": comp.get("aspect_preference"),
                "composition_type": comp.get("composition_type"),
                "center_focus": comp.get("center_focus_strength")
            })
        
        # Track lighting preferences
        if "lighting_preferences" in analysis:
            lighting = analysis["lighting_preferences"]
            self.personal_style_data["lighting_preferences"].append({
                "style": lighting.get("lighting_style"),
                "contrast": lighting.get("contrast_style"),
                "brightness": lighting.get("avg_brightness")
            })
        
        # Track workflow patterns
        if analysis.get("workflow_info", {}).get("workflow_available"):
            workflow_info = analysis["workflow_info"]
            self.personal_style_data["workflow_patterns"].append({
                "model": workflow_info.get("model_info", {}).get("checkpoint"),
                "settings": workflow_info.get("settings_info", {}),
                "node_count": workflow_info.get("node_count")
            })
    
    async def extract_personal_style_patterns(self):
        """Extract overall patterns from all analyzed images."""
        
        # Color pattern analysis
        color_temps = [p["temperature"] for p in self.personal_style_data["color_preferences"] if p["temperature"]]
        preferred_temp = Counter(color_temps).most_common(1)[0][0] if color_temps else "neutral"
        
        saturations = [p["saturation"] for p in self.personal_style_data["color_preferences"] if p["saturation"]]
        avg_saturation = statistics.mean(saturations) if saturations else 50
        
        harmonies = [p["harmony"] for p in self.personal_style_data["color_preferences"] if p["harmony"]]
        preferred_harmony = Counter(harmonies).most_common(1)[0][0] if harmonies else "balanced"
        
        # Composition patterns
        aspects = [p["aspect_preference"] for p in self.personal_style_data["composition_patterns"] if p["aspect_preference"]]
        preferred_aspect = Counter(aspects).most_common(1)[0][0] if aspects else "landscape"
        
        compositions = [p["composition_type"] for p in self.personal_style_data["composition_patterns"] if p["composition_type"]]
        preferred_composition = Counter(compositions).most_common(1)[0][0] if compositions else "balanced"
        
        # Lighting patterns
        lighting_styles = [p["style"] for p in self.personal_style_data["lighting_preferences"] if p["style"]]
        preferred_lighting = Counter(lighting_styles).most_common(1)[0][0] if lighting_styles else "balanced"
        
        # Workflow patterns
        models = [p["model"] for p in self.personal_style_data["workflow_patterns"] if p["model"]]
        preferred_model = Counter(models).most_common(1)[0][0] if models else "unknown"
        
        self.style_patterns = {
            "color_patterns": {
                "preferred_temperature": preferred_temp,
                "average_saturation": round(avg_saturation, 1),
                "preferred_harmony": preferred_harmony,
                "temperature_distribution": dict(Counter(color_temps))
            },
            "composition_patterns": {
                "preferred_aspect_ratio": preferred_aspect,
                "preferred_composition": preferred_composition,
                "aspect_distribution": dict(Counter(aspects))
            },
            "lighting_patterns": {
                "preferred_lighting_style": preferred_lighting,
                "lighting_distribution": dict(Counter(lighting_styles))
            },
            "workflow_patterns": {
                "preferred_model": preferred_model,
                "model_distribution": dict(Counter(models))
            }
        }
    
    async def generate_personal_style_config(self):
        """Generate a comprehensive personal style configuration."""
        
        patterns = self.style_patterns
        
        # Create style-based prompt templates
        base_style_prompt = await self.create_style_prompt_template()
        
        # Create model-specific configurations
        model_configs = await self.create_model_specific_configs()
        
        # Create negative prompt based on preferences
        personal_negative_prompt = await self.create_personal_negative_prompt()
        
        self.personal_config = {
            "personal_style_profile": {
                "style_name": "Personal Curated Style",
                "description": "Style configuration based on user's handpicked image preferences",
                "creation_date": datetime.now().isoformat(),
                "image_count_analyzed": len(self.image_analyses),
                "confidence_score": await self.calculate_overall_confidence()
            },
            
            "visual_preferences": {
                "color_profile": {
                    "temperature": patterns["color_patterns"]["preferred_temperature"],
                    "saturation_level": patterns["color_patterns"]["average_saturation"],
                    "harmony_type": patterns["color_patterns"]["preferred_harmony"],
                    "recommended_palette": await self.generate_recommended_palette()
                },
                "composition_profile": {
                    "aspect_ratio": patterns["composition_patterns"]["preferred_aspect_ratio"],
                    "composition_style": patterns["composition_patterns"]["preferred_composition"],
                    "focus_type": "center" if patterns["composition_patterns"]["preferred_composition"] == "centered" else "rule_of_thirds"
                },
                "lighting_profile": {
                    "lighting_style": patterns["lighting_patterns"]["preferred_lighting_style"],
                    "contrast_preference": await self.determine_contrast_preference(),
                    "mood": await self.determine_mood_preference()
                }
            },
            
            "generation_config": {
                "recommended_models": await self.get_recommended_models(),
                "base_prompt_template": base_style_prompt,
                "negative_prompt_template": personal_negative_prompt,
                "model_specific_configs": model_configs,
                "recommended_settings": await self.get_recommended_settings()
            },
            
            "style_keywords": await self.generate_style_keywords(),
            "usage_instructions": await self.create_usage_instructions()
        }
    
    async def create_style_prompt_template(self) -> str:
        """Create a prompt template based on style analysis."""
        
        patterns = self.style_patterns
        color_temp = patterns["color_patterns"]["preferred_temperature"]
        lighting = patterns["lighting_patterns"]["preferred_lighting_style"]
        harmony = patterns["color_patterns"]["preferred_harmony"]
        
        # Build style-specific prompt elements
        prompt_elements = []
        
        # Color temperature
        if color_temp == "warm":
            prompt_elements.append("warm color palette, golden tones, cozy atmosphere")
        elif color_temp == "cool":
            prompt_elements.append("cool color palette, blue and cyan tones, crisp atmosphere")
        else:
            prompt_elements.append("balanced color palette, natural tones")
        
        # Lighting style
        if lighting == "high_key":
            prompt_elements.append("bright lighting, high key, well-lit, luminous")
        elif lighting == "low_key":
            prompt_elements.append("dramatic lighting, low key, moody shadows, atmospheric")
        else:
            prompt_elements.append("balanced lighting, natural illumination")
        
        # Color harmony
        if harmony == "monochromatic":
            prompt_elements.append("monochromatic color scheme, tonal variation")
        elif harmony == "complementary":
            prompt_elements.append("complementary colors, color contrast")
        
        base_template = f"high quality, detailed, {', '.join(prompt_elements)}, professional composition"
        
        return base_template
    
    async def create_personal_negative_prompt(self) -> str:
        """Create negative prompt based on what user doesn't prefer."""
        
        patterns = self.style_patterns
        
        negative_elements = ["low quality", "blurry", "distorted", "artifacts"]
        
        # Add style-specific negatives
        color_temp = patterns["color_patterns"]["preferred_temperature"]
        if color_temp == "warm":
            negative_elements.append("cold lighting, blue tint, harsh fluorescent lighting")
        elif color_temp == "cool":
            negative_elements.append("overly warm, orange tint, yellow cast")
        
        lighting = patterns["lighting_patterns"]["preferred_lighting_style"]
        if lighting == "high_key":
            negative_elements.append("dark shadows, underexposed, gloomy")
        elif lighting == "low_key":
            negative_elements.append("overexposed, blown out highlights, flat lighting")
        
        return ", ".join(negative_elements)
    
    async def create_model_specific_configs(self) -> Dict[str, Any]:
        """Create configurations optimized for different models."""
        
        return {
            "flux_dev": {
                "strength": 0.8,
                "guidance_scale": 7.5,
                "steps": 30,
                "prompt_enhancement": "Add 'flux style' prefix for consistency"
            },
            "sdxl_base": {
                "strength": 0.9,
                "guidance_scale": 8.0,
                "steps": 35,
                "prompt_enhancement": "Use detailed descriptive prompts"
            },
            "sd_15": {
                "strength": 1.0,
                "guidance_scale": 7.0,
                "steps": 40,
                "prompt_enhancement": "Keep prompts concise but descriptive"
            }
        }
    
    async def generate_recommended_palette(self) -> List[str]:
        """Generate recommended color palette based on preferences."""
        
        # Extract most common colors from all analyses
        all_colors = []
        for pref in self.personal_style_data["color_preferences"]:
            if "dominant_colors" in pref:
                all_colors.extend([c["hex"] for c in pref["dominant_colors"]])
        
        # Get most frequent colors
        color_counts = Counter(all_colors)
        recommended_palette = [color for color, count in color_counts.most_common(8)]
        
        return recommended_palette
    
    async def determine_contrast_preference(self) -> str:
        """Determine user's contrast preference."""
        contrasts = [p["contrast"] for p in self.personal_style_data["lighting_preferences"] if p.get("contrast")]
        if contrasts:
            return Counter(contrasts).most_common(1)[0][0]
        return "moderate_contrast"
    
    async def determine_mood_preference(self) -> str:
        """Determine overall mood preference."""
        lightings = [p["style"] for p in self.personal_style_data["lighting_preferences"] if p.get("style")]
        
        if not lightings:
            return "balanced"
        
        most_common = Counter(lightings).most_common(1)[0][0]
        
        mood_map = {
            "high_key": "bright_cheerful",
            "low_key": "dramatic_moody", 
            "balanced": "natural_versatile"
        }
        
        return mood_map.get(most_common, "balanced")
    
    async def get_recommended_models(self) -> List[str]:
        """Get recommended models based on workflow analysis."""
        
        models = [p["model"] for p in self.personal_style_data["workflow_patterns"] if p.get("model")]
        
        if not models:
            return ["Flux Dev", "SDXL Base", "SD 1.5"]
        
        model_counts = Counter(models)
        recommended = [model for model, count in model_counts.most_common(3)]
        
        # Ensure we have at least 3 recommendations
        defaults = ["Flux Dev", "SDXL Base", "SD 1.5"]
        for default in defaults:
            if default not in recommended:
                recommended.append(default)
        
        return recommended[:3]
    
    async def get_recommended_settings(self) -> Dict[str, Any]:
        """Get recommended generation settings."""
        
        settings_list = [p["settings"] for p in self.personal_style_data["workflow_patterns"] if p.get("settings")]
        
        if not settings_list:
            return {
                "steps": 30,
                "cfg_scale": 7.5,
                "sampler": "euler_ancestral",
                "scheduler": "normal"
            }
        
        # Average the settings where available
        steps = [s.get("steps") for s in settings_list if s.get("steps")]
        cfgs = [s.get("cfg") for s in settings_list if s.get("cfg")]
        samplers = [s.get("sampler") for s in settings_list if s.get("sampler")]
        
        return {
            "steps": int(statistics.mean(steps)) if steps else 30,
            "cfg_scale": round(statistics.mean(cfgs), 1) if cfgs else 7.5,
            "sampler": Counter(samplers).most_common(1)[0][0] if samplers else "euler_ancestral",
            "scheduler": "normal"
        }
    
    async def generate_style_keywords(self) -> List[str]:
        """Generate keywords that capture the personal style."""
        
        patterns = self.style_patterns
        keywords = []
        
        # Add color-based keywords
        temp = patterns["color_patterns"]["preferred_temperature"]
        if temp == "warm":
            keywords.extend(["warm tones", "golden", "cozy", "inviting"])
        elif temp == "cool":
            keywords.extend(["cool tones", "crisp", "clean", "modern"])
        
        # Add lighting-based keywords
        lighting = patterns["lighting_patterns"]["preferred_lighting_style"]
        if lighting == "high_key":
            keywords.extend(["bright", "luminous", "airy", "light"])
        elif lighting == "low_key":
            keywords.extend(["dramatic", "moody", "atmospheric", "cinematic"])
        
        # Add composition keywords
        comp = patterns["composition_patterns"]["preferred_composition"]
        if comp == "centered":
            keywords.extend(["centered", "focal", "balanced"])
        else:
            keywords.extend(["dynamic", "rule of thirds", "compositional"])
        
        return list(set(keywords))
    
    async def create_usage_instructions(self) -> Dict[str, str]:
        """Create instructions for using the personal style config."""
        
        return {
            "prompt_usage": "Use the base_prompt_template as a starting point and add your specific subject/scene description",
            "negative_prompt": "Always use the negative_prompt_template to avoid unwanted elements",
            "model_selection": "Choose from recommended_models based on your specific needs - Flux for photorealism, SDXL for versatility, SD1.5 for speed",
            "settings": "Use the recommended_settings as a baseline and adjust based on your specific requirements",
            "color_palette": "Reference the recommended_palette for color-accurate reproductions of your preferred style"
        }
    
    async def calculate_overall_confidence(self) -> float:
        """Calculate confidence in the style analysis."""
        
        # Base confidence on number of images and consistency
        image_count = len(self.image_analyses)
        
        # More images = higher confidence
        count_confidence = min(image_count * 10, 90)  # Cap at 90%
        
        # Consistency in preferences increases confidence
        consistency_bonus = 5  # Default bonus for finding patterns
        
        return min(count_confidence + consistency_bonus, 95)  # Cap at 95%
    
    async def create_personal_style_guide(self):
        """Create comprehensive style guide document."""
        
        # Save personal config as JSON
        config_file = self.target_folder / "Personal_Style_Config.json"
        with open(config_file, 'w') as f:
            json.dump(self.personal_config, f, indent=2)
        
        # Save detailed analysis
        detailed_analysis = {
            "handpicked_images_analysis": {
                "total_images": len(self.image_analyses),
                "analysis_date": datetime.now().isoformat(),
                "user_note": "These images were handpicked by the user as representative of their preferred style"
            },
            "individual_image_analyses": self.image_analyses,
            "extracted_style_patterns": self.style_patterns,
            "personal_style_data": self.personal_style_data
        }
        
        analysis_file = self.target_folder / "Detailed_Personal_Style_Analysis.json"
        with open(analysis_file, 'w') as f:
            json.dump(detailed_analysis, f, indent=2)
        
        # Create human-readable style guide
        readme_content = await self.create_style_guide_readme()
        readme_file = self.target_folder / "Personal_Style_Guide.md"
        with open(readme_file, 'w') as f:
            f.write(readme_content)
        
        print(f"[GENERATED] Personal style files created:")
        print(f"  - {config_file}")
        print(f"  - {analysis_file}")
        print(f"  - {readme_file}")
    
    async def create_style_guide_readme(self) -> str:
        """Create human-readable style guide."""
        
        config = self.personal_config
        patterns = self.style_patterns
        
        return f"""# Personal Style Guide

## Style Profile
- **Style Name**: {config['personal_style_profile']['style_name']}
- **Based on**: {config['personal_style_profile']['image_count_analyzed']} handpicked images
- **Confidence Score**: {config['personal_style_profile']['confidence_score']}%
- **Created**: {config['personal_style_profile']['creation_date']}

## Your Visual Preferences

### Color Profile
- **Temperature**: {config['visual_preferences']['color_profile']['temperature']} tones
- **Saturation Level**: {config['visual_preferences']['color_profile']['saturation_level']}%
- **Color Harmony**: {config['visual_preferences']['color_profile']['harmony_type']}

### Composition Style  
- **Preferred Aspect Ratio**: {config['visual_preferences']['composition_profile']['aspect_ratio']}
- **Composition Type**: {config['visual_preferences']['composition_profile']['composition_style']}
- **Focus Style**: {config['visual_preferences']['composition_profile']['focus_type']}

### Lighting Preferences
- **Lighting Style**: {config['visual_preferences']['lighting_profile']['lighting_style']}
- **Contrast**: {config['visual_preferences']['lighting_profile']['contrast_preference']}
- **Mood**: {config['visual_preferences']['lighting_profile']['mood']}

## Generation Configuration

### Recommended Models
1. **Primary**: {config['generation_config']['recommended_models'][0]}
2. **Secondary**: {config['generation_config']['recommended_models'][1]}  
3. **Fallback**: {config['generation_config']['recommended_models'][2]}

### Base Prompt Template
```
{config['generation_config']['base_prompt_template']}
```

### Negative Prompt Template
```
{config['generation_config']['negative_prompt_template']}
```

### Recommended Settings
- **Steps**: {config['generation_config']['recommended_settings']['steps']}
- **CFG Scale**: {config['generation_config']['recommended_settings']['cfg_scale']}
- **Sampler**: {config['generation_config']['recommended_settings']['sampler']}

## Style Keywords
{', '.join(config['style_keywords'])}

## Usage Instructions
{chr(10).join([f"- **{k.replace('_', ' ').title()}**: {v}" for k, v in config['usage_instructions'].items()])}

## Color Palette
Your preferred colors: {', '.join(config['visual_preferences']['color_profile']['recommended_palette'][:6])}

---
*This style guide was generated by analyzing your handpicked image collection to identify your personal aesthetic preferences.*
"""


async def main():
    """Execute personal style analysis."""
    print("Personal Style Analysis - Handpicked Image Collection")
    print("=" * 60)
    print("Analyzing your curated images to create a personalized style config...")
    print("=" * 60)
    
    analyzer = PersonalStyleAnalyzer(
        target_folder="G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis",
        verified_workflows_folder="G:/ZComfyUI/comfyui-custom-frontend/verified_workflows"
    )
    
    try:
        await analyzer.analyze_personal_style()
        
        print(f"\n[SUCCESS] Personal Style Analysis Complete!")
        print(f"[RESULTS] Your personalized style config has been generated")
        print(f"[FILES] Check the Image_Analysis folder for:")
        print(f"  - Personal_Style_Config.json (machine-readable config)")
        print(f"  - Personal_Style_Guide.md (human-readable guide)")
        print(f"  - Detailed_Personal_Style_Analysis.json (full analysis)")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Personal style analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)