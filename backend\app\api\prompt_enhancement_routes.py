"""
API routes for prompt enhancement functionality
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Literal
from enum import Enum
import logging

from ..services.prompt_enhancement_service import (
    PromptEnhancementService,
    EnhancementRequest,
    EnhancementResult,
    ImageModel,
    EnhancementMode,
    LLMModel
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/prompt-enhancement", tags=["prompt-enhancement"])

# Request/Response Models
class EnhancementRequestModel(BaseModel):
    user_prompt: str = Field(..., description="The original user prompt to enhance")
    target_model: Literal["flux_dev", "flux_kontext", "sd15", "sdxl"] = Field(..., description="Target image generation model")
    enhancement_mode: Literal["creative", "technical", "balanced"] = Field(default="balanced", description="Enhancement approach")
    style_preference: Optional[str] = Field(None, description="Preferred artistic style")
    quality_level: str = Field(default="high", description="Quality level for enhancement")
    reference_image_context: Optional[str] = Field(None, description="Context about reference image if applicable")
    preservation_elements: Optional[List[str]] = Field(None, description="Elements to preserve in editing mode")

class EnhancementResponseModel(BaseModel):
    original_prompt: str
    enhanced_prompt: str
    negative_prompt: Optional[str] = None
    explanation: str = ""
    technical_settings: Optional[Dict] = None
    confidence_score: float
    llm_model_used: str
    processing_time: float
    success: bool = True
    error_message: Optional[str] = None

class ModelStatusModel(BaseModel):
    ollama_available: bool
    available_models: List[str]
    recommended_models: Dict[str, str]
    service_healthy: bool

class QuickEnhanceRequest(BaseModel):
    prompt: str
    model: Literal["flux_dev", "flux_kontext", "sd15"] = "flux_dev"
    mode: Literal["creative", "technical", "balanced"] = "balanced"

# Global service instance
enhancement_service = PromptEnhancementService()

@router.get("/status", response_model=ModelStatusModel)
async def get_service_status():
    """
    Get the current status of the prompt enhancement service
    """
    try:
        ollama_healthy = await enhancement_service.check_ollama_health()
        available_models = await enhancement_service.list_available_models() if ollama_healthy else []
        
        recommended_models = {
            "primary": "mistral:7b-instruct",
            "creative": "llama3.2:latest", 
            "premium": "nous-hermes2-mixtral:latest",
            "technical": "codellama:latest"
        }
        
        return ModelStatusModel(
            ollama_available=ollama_healthy,
            available_models=available_models,
            recommended_models=recommended_models,
            service_healthy=ollama_healthy and len(available_models) > 0
        )
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return ModelStatusModel(
            ollama_available=False,
            available_models=[],
            recommended_models={},
            service_healthy=False
        )

@router.post("/enhance", response_model=EnhancementResponseModel)
async def enhance_prompt(request: EnhancementRequestModel):
    """
    Enhance a user prompt for a specific image generation model
    """
    try:
        # Convert request to internal format
        enhancement_request = EnhancementRequest(
            user_prompt=request.user_prompt,
            target_model=ImageModel(request.target_model),
            enhancement_mode=EnhancementMode(request.enhancement_mode),
            style_preference=request.style_preference,
            quality_level=request.quality_level,
            reference_image_context=request.reference_image_context,
            preservation_elements=request.preservation_elements
        )
        
        # Perform enhancement
        result = await enhancement_service.enhance_prompt(enhancement_request)
        
        # Convert result to response format
        return EnhancementResponseModel(
            original_prompt=result.original_prompt,
            enhanced_prompt=result.enhanced_prompt,
            negative_prompt=result.negative_prompt,
            explanation=result.explanation,
            technical_settings=result.technical_settings,
            confidence_score=result.confidence_score,
            llm_model_used=result.llm_model_used,
            processing_time=result.processing_time,
            success=True
        )
        
    except Exception as e:
        logger.error(f"Enhancement failed: {e}")
        return EnhancementResponseModel(
            original_prompt=request.user_prompt,
            enhanced_prompt=request.user_prompt,
            explanation=f"Enhancement failed: {str(e)}",
            confidence_score=0.0,
            llm_model_used="",
            processing_time=0.0,
            success=False,
            error_message=str(e)
        )

@router.post("/quick-enhance", response_model=EnhancementResponseModel)
async def quick_enhance(request: QuickEnhanceRequest):
    """
    Quick enhancement with simplified parameters
    """
    try:
        enhancement_request = EnhancementRequest(
            user_prompt=request.prompt,
            target_model=ImageModel(request.model),
            enhancement_mode=EnhancementMode(request.mode)
        )
        
        result = await enhancement_service.enhance_prompt(enhancement_request)
        
        return EnhancementResponseModel(
            original_prompt=result.original_prompt,
            enhanced_prompt=result.enhanced_prompt,
            negative_prompt=result.negative_prompt,
            explanation=result.explanation,
            technical_settings=result.technical_settings,
            confidence_score=result.confidence_score,
            llm_model_used=result.llm_model_used,
            processing_time=result.processing_time,
            success=True
        )
        
    except Exception as e:
        logger.error(f"Quick enhancement failed: {e}")
        return EnhancementResponseModel(
            original_prompt=request.prompt,
            enhanced_prompt=request.prompt,
            explanation=f"Enhancement failed: {str(e)}",
            confidence_score=0.0,
            llm_model_used="",
            processing_time=0.0,
            success=False,
            error_message=str(e)
        )

@router.get("/models/available")
async def get_available_models():
    """
    Get list of available Ollama models
    """
    try:
        models = await enhancement_service.list_available_models()
        return {"available_models": models, "count": len(models)}
    except Exception as e:
        logger.error(f"Failed to get available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/recommended")
async def get_recommended_models():
    """
    Get recommended models for different use cases
    """
    return {
        "recommendations": {
            "primary": {
                "model": "mistral:7b-instruct",
                "description": "Best overall choice for prompt enhancement",
                "size": "4.1 GB",
                "use_case": "Primary enhancement engine"
            },
            "creative": {
                "model": "llama3.2:latest", 
                "description": "Best for creative and conversational enhancement",
                "size": "2.0 GB",
                "use_case": "Creative brainstorming and natural language enhancement"
            },
            "premium": {
                "model": "nous-hermes2-mixtral:latest",
                "description": "Highest quality enhancement for advanced users",
                "size": "26 GB", 
                "use_case": "Premium enhancement with advanced reasoning"
            },
            "technical": {
                "model": "codellama:latest",
                "description": "Good for technical and structured prompts",
                "size": "3.8 GB",
                "use_case": "Technical workflow generation"
            }
        }
    }

@router.get("/templates/{model_name}")
async def get_enhancement_template(model_name: str):
    """
    Get the enhancement template for a specific image model
    """
    try:
        templates = enhancement_service.templates
        if model_name in templates:
            return {"template": templates[model_name]}
        else:
            raise HTTPException(status_code=404, detail=f"Template for {model_name} not found")
    except Exception as e:
        logger.error(f"Failed to get template: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-enhance")
async def batch_enhance_prompts(prompts: List[QuickEnhanceRequest]):
    """
    Enhance multiple prompts in batch
    """
    results = []
    
    for prompt_request in prompts:
        try:
            enhancement_request = EnhancementRequest(
                user_prompt=prompt_request.prompt,
                target_model=ImageModel(prompt_request.model),
                enhancement_mode=EnhancementMode(prompt_request.mode)
            )
            
            result = await enhancement_service.enhance_prompt(enhancement_request)
            
            results.append(EnhancementResponseModel(
                original_prompt=result.original_prompt,
                enhanced_prompt=result.enhanced_prompt,
                negative_prompt=result.negative_prompt,
                explanation=result.explanation,
                technical_settings=result.technical_settings,
                confidence_score=result.confidence_score,
                llm_model_used=result.llm_model_used,
                processing_time=result.processing_time,
                success=True
            ))
            
        except Exception as e:
            logger.error(f"Batch enhancement failed for prompt: {prompt_request.prompt}: {e}")
            results.append(EnhancementResponseModel(
                original_prompt=prompt_request.prompt,
                enhanced_prompt=prompt_request.prompt,
                explanation=f"Enhancement failed: {str(e)}",
                confidence_score=0.0,
                llm_model_used="",
                processing_time=0.0,
                success=False,
                error_message=str(e)
            ))
    
    return {"results": results, "total": len(results)}

@router.get("/health")
async def health_check():
    """
    Simple health check endpoint
    """
    try:
        ollama_healthy = await enhancement_service.check_ollama_health()
        return {
            "status": "healthy" if ollama_healthy else "degraded",
            "ollama_available": ollama_healthy,
            "service": "prompt-enhancement"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "ollama_available": False,
            "service": "prompt-enhancement",
            "error": str(e)
        }
