#!/usr/bin/env python3
"""
Execute Image Expert Analysis on Image_Analysis Folder
Following the comprehensive instructions in 2A-README.md
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add framework and scripts to path
sys.path.insert(0, str(Path(__file__).parent))
sys.path.insert(0, str(Path(__file__).parent / 'scripts'))

from scripts.image_expert import EnhancedImageExpertAgent

async def main():
    """Execute comprehensive image analysis following README instructions."""
    print("Enhanced Image Expert Agent - Image Analysis Execution")
    print("=" * 60)
    print(f"Target Folder: G:\\ZComfyUI\\comfyui-custom-frontend\\frontend\\Image_Analysis\\")
    print(f"Verified Workflows: G:\\ZComfyUI\\comfyui-custom-frontend\\verified_workflows\\")
    print(f"Execution Time: {datetime.now().isoformat()}")
    print("=" * 60)
    
    # Initialize the Image Expert agent
    agent_context = {
        "agent": {
            "name": "enhanced-image-expert",
            "version": "2.2",
            "capabilities": ["image_analysis", "workflow_extraction", "style_guide_generation"]
        },
        "target_folder": "G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis/",
        "verified_workflows_folder": "G:/ZComfyUI/comfyui-custom-frontend/verified_workflows/",
        "hardware": "RTX_4070_Ti_SUPER",
        "primary_models": ["Flux Dev", "SDXL Base", "SD 1.5"],
        "workflow_extraction_enabled": True,
        "comprehensive_analysis": True,
        "style_guide_generation": True,
        "file_organization": True
    }
    
    try:
        # Create Image Expert instance
        print("[INIT] Initializing Enhanced Image Expert Agent...")
        image_expert = EnhancedImageExpertAgent(agent_context)
        
        # Execute the comprehensive analysis task following README specifications
        print("[EXECUTE] Starting comprehensive image analysis with workflow extraction...")
        
        # Create the task parameters based on README requirements
        analysis_task = {
            "task": "comprehensive_image_analysis_with_workflow_extraction",
            "parameters": {
                "target_folder": agent_context["target_folder"],
                "verified_workflows_folder": agent_context["verified_workflows_folder"],
                "analysis_components": [
                    "folder_scan",
                    "workflow_extraction", 
                    "individual_analysis",
                    "style_categorization",
                    "batch_insights",
                    "file_organization"
                ],
                "workflow_extraction_settings": {
                    "extract_from_png_metadata": True,
                    "validate_workflow_structure": True,
                    "create_workflow_index": True,
                    "avoid_duplicates": True,
                    "generate_descriptive_filenames": True
                },
                "analysis_requirements": {
                    "style_analysis": True,
                    "technical_analysis": True,
                    "replication_blueprint": True,
                    "quality_assessment": True,
                    "hardware_optimization": True
                },
                "output_specifications": {
                    "generate_1a_results": True,
                    "generate_1b_style_guides": True,
                    "create_extraction_log": True,
                    "update_workflow_index": True
                },
                "file_management": {
                    "move_to_processed": True,
                    "organize_by_workflow_status": True,
                    "preserve_metadata": True,
                    "create_movement_log": True
                }
            }
        }
        
        # Execute the analysis
        result = await image_expert.execute(analysis_task)
        
        # Display results
        if result.status == "success":
            print(f"\n[SUCCESS] Image analysis completed successfully!")
            
            # Display summary statistics
            if "summary_statistics" in result.data:
                stats = result.data["summary_statistics"]
                print(f"\n[STATS] Analysis Summary:")
                print(f"  - Total images processed: {stats.get('total_images', 0)}")
                print(f"  - Workflows extracted: {stats.get('workflows_extracted', 0)}")
                print(f"  - Style categories identified: {stats.get('style_categories', 0)}")
                print(f"  - Files moved to processed: {stats.get('files_moved', 0)}")
                print(f"  - Analysis confidence: {stats.get('avg_confidence', 0):.1f}%")
            
            # Display file outputs
            print(f"\n[OUTPUT] Generated Files:")
            if "output_files" in result.data:
                for file_path in result.data["output_files"]:
                    print(f"  - {file_path}")
            
            # Display workflow extraction summary
            if "workflow_extraction_summary" in result.data:
                extraction = result.data["workflow_extraction_summary"]
                print(f"\n[WORKFLOWS] Extraction Summary:")
                print(f"  - Extraction attempts: {extraction.get('attempts', 0)}")
                print(f"  - Successful extractions: {extraction.get('successful', 0)}")
                print(f"  - Workflows saved: {extraction.get('saved', 0)}")
                print(f"  - Success rate: {extraction.get('success_rate', 0):.1f}%")
            
        else:
            print(f"[ERROR] Analysis failed: {result.error}")
            if hasattr(result, 'data') and result.data:
                print(f"Error details: {result.data}")
        
        print(f"\n[COMPLETE] Image Expert analysis execution finished")
        print("=" * 60)
        
    except Exception as e:
        print(f"[CRITICAL ERROR] {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)