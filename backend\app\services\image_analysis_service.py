import requests
import base64
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any
from PIL import Image
import hashlib
import os

from ..core.config import settings

class OllamaImageAnalyzer:
    def __init__(self, model: str = "llava:latest"):
        self.model = model
        self.ollama_url = settings.OLLAMA_API_URL
        self.db_path = Path("data/agents/image_expert.db")
        self._ensure_database()
    
    def _ensure_database(self):
        """Initialize database if it doesn't exist"""
        if not self.db_path.exists():
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            # Execute schema from the SQL file
            schema_path = Path("data/agents/image_expert_schema.sql")
            if schema_path.exists():
                with open(schema_path, 'r') as f:
                    schema = f.read()
                
                with sqlite3.connect(self.db_path) as conn:
                    conn.executescript(schema)
    
    def _image_to_base64(self, image_path: str) -> str:
        """Convert image to base64 string"""
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    
    def _get_image_metadata(self, image_path: str) -> Dict[str, Any]:
        """Extract basic image metadata"""
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode,
                    "file_size": os.path.getsize(image_path)
                }
        except Exception as e:
            return {"error": str(e)}
    
    def _call_ollama(self, prompt: str, image_b64: str) -> Optional[str]:
        """Make API call to Ollama"""
        try:
            response = requests.post(f"{self.ollama_url}/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "images": [image_b64],
                "stream": False
            }, timeout=60)
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                print(f"Ollama API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return None
    
    def analyze_art_style(self, image_path: str) -> Dict[str, Any]:
        """Identify art style and movement"""
        prompt = """Analyze this image's artistic style. Provide a structured analysis covering:

1. PRIMARY ART STYLE: Main artistic movement or style (e.g., Renaissance, Impressionism, Digital Art, Concept Art)
2. SECONDARY INFLUENCES: Additional styles or influences present
3. TIME PERIOD: Historical period or modern digital category
4. KEY CHARACTERISTICS: Specific visual elements that define this style
5. COLOR PALETTE: Dominant colors and color harmony type
6. TECHNIQUE: Apparent artistic technique or medium
7. COMPOSITION: Compositional approach and structure
8. CONFIDENCE: Rate your confidence in this analysis (1-10)

Format your response as clear, structured text."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "art_style",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_composition(self, image_path: str) -> Dict[str, Any]:
        """Analyze composition and visual structure"""
        prompt = """Analyze this image's composition and visual structure:

1. RULE OF THIRDS: How well does it follow the rule of thirds? (1-10)
2. FOCAL POINT: Identify the main focal point and its strength (1-10)
3. LEADING LINES: Are there leading lines? Describe them
4. BALANCE: Visual balance assessment (symmetrical/asymmetrical/radial)
5. DEPTH: How is depth created? (perspective, overlapping, size, etc.)
6. MOVEMENT: How does the eye move through the image?
7. CONTRAST: Light/dark contrast levels and effectiveness
8. OVERALL COMPOSITION SCORE: Rate the composition strength (1-10)

Provide specific details about what makes the composition effective or ineffective."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "composition",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_technical_quality(self, image_path: str) -> Dict[str, Any]:
        """Analyze technical image quality"""
        prompt = """Evaluate this image's technical quality:

1. SHARPNESS: Overall sharpness and focus quality (1-10)
2. NOISE/GRAIN: Presence of noise or grain (low/medium/high)
3. EXPOSURE: Exposure levels (underexposed/proper/overexposed)
4. DYNAMIC RANGE: Shadow and highlight detail retention (1-10)
5. COLOR ACCURACY: Natural and accurate colors (1-10)
6. COMPRESSION: Visible compression artifacts (none/minor/moderate/severe)
7. RESOLUTION: Apparent resolution quality for the content
8. TECHNICAL ISSUES: Any technical flaws or problems
9. OVERALL TECHNICAL SCORE: Rate technical quality (1-10)

Focus on measurable technical aspects rather than artistic merit."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "technical_quality",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def comprehensive_analysis(self, image_path: str) -> Dict[str, Any]:
        """Perform comprehensive image analysis"""
        print(f"Analyzing image: {image_path}")
        
        # Get image metadata
        metadata = self._get_image_metadata(image_path)
        
        # Perform all analyses
        style_analysis = self.analyze_art_style(image_path)
        composition_analysis = self.analyze_composition(image_path)
        technical_analysis = self.analyze_technical_quality(image_path)
        
        # Create comprehensive result
        result = {
            "image_path": image_path,
            "image_filename": Path(image_path).name,
            "metadata": metadata,
            "analyses": {
                "style": style_analysis,
                "composition": composition_analysis,
                "technical": technical_analysis
            },
            "analyzed_at": datetime.now().isoformat(),
            "analyzer_model": self.model
        }
        
        # Store in database
        self._store_analysis(result)
        
        return result
    
    def _store_analysis(self, analysis_data: Dict[str, Any]):
        """Store analysis results in SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Extract metadata
                meta = analysis_data.get("metadata", {})
                
                # Insert into image_analysis table
                cursor.execute("""
                    INSERT INTO image_analysis (
                        image_path, image_filename, file_size_bytes,
                        dimensions_width, dimensions_height, file_format,
                        style_analysis_details, composition_score, technical_quality_score,
                        analyzed_at, analyzer_version, tags, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    analysis_data["image_path"],
                    analysis_data["image_filename"],
                    meta.get("file_size", 0),
                    meta.get("width", 0),
                    meta.get("height", 0),
                    meta.get("format", "unknown"),
                    json.dumps(analysis_data["analyses"]),
                    8.0,  # Placeholder composition score
                    8.0,  # Placeholder technical score
                    analysis_data["analyzed_at"],
                    analysis_data["analyzer_model"],
                    json.dumps(["ollama_analysis"]),
                    "Analyzed with Ollama LLaVA"
                ))
                
                conn.commit()
                print(f"Analysis stored for {analysis_data['image_filename']}")
                
        except Exception as e:
            print(f"Error storing analysis: {e}")

    def get_analysis_history(self, limit: int = 10) -> list:
        """Get recent analysis history"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT image_path, image_filename, analyzed_at, 
                           style_analysis_details, composition_score, technical_quality_score
                    FROM image_analysis 
                    ORDER BY analyzed_at DESC 
                    LIMIT ?
                """, (limit,))
                
                results = cursor.fetchall()
                return [
                    {
                        "image_path": row[0],
                        "filename": row[1],
                        "analyzed_at": row[2],
                        "analysis_details": json.loads(row[3]) if row[3] else {},
                        "composition_score": row[4],
                        "technical_score": row[5]
                    }
                    for row in results
                ]
        except Exception as e:
            print(f"Error getting analysis history: {e}")
            return []

# Convenience function for quick analysis
def analyze_image(image_path: str) -> Dict[str, Any]:
    """Quick function to analyze an image"""
    analyzer = OllamaImageAnalyzer()
    return analyzer.comprehensive_analysis(image_path)