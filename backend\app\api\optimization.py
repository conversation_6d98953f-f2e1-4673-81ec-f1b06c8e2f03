"""
Optimization API Routes
Provides REST API endpoints for image generation optimization services
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from datetime import datetime

from app.services.image_generation_optimizer import ImageGenerationOptimizer, OptimizationStrategy
from app.services.workflow_execution_optimizer import WorkflowExecutionOptimizer, OptimizationPriority
from app.utils.centralized_logger import get_logger, log_activity, log_error

router = APIRouter()
logger = get_logger()

# Initialize optimizers
image_optimizer = ImageGenerationOptimizer()
workflow_optimizer = WorkflowExecutionOptimizer()

class OptimizationRequest(BaseModel):
    generation_params: Dict[str, Any]
    optimization_strategy: Optional[str] = "balanced"
    hardware_profile: Optional[str] = "rtx_4070ti_super"

class OptimizationResponse(BaseModel):
    success: bool
    optimized_params: Dict[str, Any]
    optimizations_applied: List[str]
    performance_estimates: Dict[str, float]
    recommendations: List[Dict[str, Any]]

class WorkflowOptimizationRequest(BaseModel):
    workflow: Dict[str, Any]
    generation_params: Dict[str, Any]
    optimization_priority: Optional[str] = "balanced"

class WorkflowOptimizationResponse(BaseModel):
    success: bool
    workflow_hash: str
    execution_plan: Dict[str, Any]
    performance_estimates: Dict[str, float]
    optimization_notes: List[str]

class PerformanceMetricsResponse(BaseModel):
    generation_id: str
    metrics: Dict[str, Any]
    current_recommendations: List[Dict[str, Any]]

@router.post("/optimize/parameters", response_model=OptimizationResponse)
async def optimize_generation_parameters(request: OptimizationRequest):
    """Optimize generation parameters for better performance"""
    
    try:
        log_activity("OPTIMIZATION_API", "Parameter optimization requested", {
            "strategy": request.optimization_strategy,
            "hardware_profile": request.hardware_profile
        })
        
        # Optimize parameters
        optimized_params = await image_optimizer.optimize_generation_parameters(
            request.generation_params
        )
        
        # Calculate what changed
        optimizations_applied = []
        for key, value in optimized_params.items():
            original_value = request.generation_params.get(key)
            if original_value != value:
                optimizations_applied.append(f"{key}: {original_value} → {value}")
        
        # Get performance estimates
        model_type = image_optimizer._detect_model_type(request.generation_params.get("model", ""))
        complexity = image_optimizer._analyze_workflow_complexity(request.generation_params)
        
        baseline = image_optimizer.rtx_4070ti_super_baselines.get(model_type, {})
        estimated_time = baseline.get("generation_time_seconds", 20.0)
        estimated_vram = baseline.get("vram_usage_mb", 8192)
        
        # Apply optimization factors
        if request.optimization_strategy == "speed":
            estimated_time *= 0.8  # 20% faster
        elif request.optimization_strategy == "quality":
            estimated_time *= 1.2  # 20% slower but better quality
        
        performance_estimates = {
            "estimated_generation_time_seconds": estimated_time,
            "estimated_vram_usage_mb": estimated_vram,
            "estimated_gpu_utilization_percent": 85.0
        }
        
        response = OptimizationResponse(
            success=True,
            optimized_params=optimized_params,
            optimizations_applied=optimizations_applied,
            performance_estimates=performance_estimates,
            recommendations=[]
        )
        
        log_activity("OPTIMIZATION_API", "Parameter optimization completed", {
            "optimizations_count": len(optimizations_applied)
        })
        
        return response
        
    except Exception as e:
        error_msg = f"Parameter optimization failed: {str(e)}"
        log_error("OPTIMIZATION_API", "parameter_optimization_failed", error_msg, {}, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/optimize/workflow", response_model=WorkflowOptimizationResponse)
async def optimize_workflow_execution(request: WorkflowOptimizationRequest):
    """Optimize workflow execution plan for better performance"""
    
    try:
        log_activity("OPTIMIZATION_API", "Workflow optimization requested", {
            "priority": request.optimization_priority,
            "node_count": len(request.workflow.get("nodes", []))
        })
        
        # Map priority string to enum
        priority_map = {
            "speed": OptimizationPriority.SPEED,
            "memory": OptimizationPriority.MEMORY,
            "quality": OptimizationPriority.QUALITY,
            "balanced": OptimizationPriority.BALANCED
        }
        
        priority = priority_map.get(request.optimization_priority, OptimizationPriority.BALANCED)
        
        # Generate optimization plan
        optimization_plan = await workflow_optimizer.optimize_workflow_execution(
            request.workflow,
            request.generation_params,
            priority
        )
        
        execution_plan = {
            "execution_order": optimization_plan.execution_order,
            "parallel_groups": optimization_plan.parallel_groups,
            "caching_strategy": optimization_plan.caching_strategy,
            "memory_checkpoints": optimization_plan.memory_checkpoints
        }
        
        performance_estimates = {
            "estimated_total_time_ms": optimization_plan.estimated_total_time_ms,
            "estimated_peak_vram_mb": optimization_plan.estimated_peak_vram_mb
        }
        
        response = WorkflowOptimizationResponse(
            success=True,
            workflow_hash=optimization_plan.workflow_hash,
            execution_plan=execution_plan,
            performance_estimates=performance_estimates,
            optimization_notes=optimization_plan.optimization_notes
        )
        
        log_activity("OPTIMIZATION_API", "Workflow optimization completed", {
            "workflow_hash": optimization_plan.workflow_hash,
            "estimated_time_ms": optimization_plan.estimated_total_time_ms
        })
        
        return response
        
    except Exception as e:
        error_msg = f"Workflow optimization failed: {str(e)}"
        log_error("OPTIMIZATION_API", "workflow_optimization_failed", error_msg, {}, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/metrics/{generation_id}", response_model=PerformanceMetricsResponse)
async def get_generation_metrics(generation_id: str):
    """Get real-time performance metrics for active generation"""
    
    try:
        # Check if generation is being monitored
        optimization_data = image_optimizer.active_optimizations.get(generation_id)
        
        if not optimization_data:
            raise HTTPException(status_code=404, detail="Generation not found or not being monitored")
        
        metrics = optimization_data["metrics"]
        
        # Collect current system metrics
        current_metrics = await image_optimizer._collect_system_metrics()
        
        # Get real-time recommendations
        recommendations = await image_optimizer._analyze_real_time_performance(generation_id, current_metrics or {})
        
        response_metrics = {
            "generation_id": metrics.generation_id,
            "model_type": metrics.model_type,
            "workflow_complexity": metrics.workflow_complexity,
            "start_time": metrics.start_time,
            "current_time": datetime.now().timestamp(),
            "elapsed_time": datetime.now().timestamp() - metrics.start_time,
            "vram_usage_mb": metrics.vram_usage_mb,
            "gpu_utilization_percent": metrics.gpu_utilization_percent,
            "gpu_temperature_celsius": metrics.gpu_temperature_celsius,
            "cpu_utilization_percent": metrics.cpu_utilization_percent,
            "memory_usage_mb": metrics.memory_usage_mb,
            "optimization_strategy": metrics.optimization_strategy
        }
        
        response = PerformanceMetricsResponse(
            generation_id=generation_id,
            metrics=response_metrics,
            current_recommendations=recommendations
        )
        
        log_activity("OPTIMIZATION_API", f"Metrics retrieved for {generation_id}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Failed to get metrics: {str(e)}"
        log_error("OPTIMIZATION_API", "metrics_retrieval_failed", error_msg, {
            "generation_id": generation_id
        }, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/insights")
async def get_optimization_insights(
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    days: int = Query(7, description="Number of days to analyze", ge=1, le=30)
):
    """Get optimization insights and performance analytics"""
    
    try:
        log_activity("OPTIMIZATION_API", "Optimization insights requested", {
            "model_type": model_type,
            "days": days
        })
        
        # Get insights from both optimizers
        generation_insights = await image_optimizer.get_optimization_insights(model_type, days)
        workflow_insights = await workflow_optimizer.get_optimization_statistics(days)
        
        combined_insights = {
            "period_days": days,
            "model_filter": model_type,
            "generation_optimization": generation_insights,
            "workflow_optimization": workflow_insights,
            "summary": {
                "total_optimizations": len(generation_insights.get("performance_by_model", {})),
                "avg_success_rate": sum(
                    stats.get("success_rate", 0) 
                    for stats in generation_insights.get("performance_by_model", {}).values()
                ) / max(1, len(generation_insights.get("performance_by_model", {}))),
                "top_recommendations": generation_insights.get("recommendations", [])[:5]
            }
        }
        
        log_activity("OPTIMIZATION_API", "Optimization insights generated", {
            "total_models": len(generation_insights.get("performance_by_model", {}))
        })
        
        return combined_insights
        
    except Exception as e:
        error_msg = f"Failed to get optimization insights: {str(e)}"
        log_error("OPTIMIZATION_API", "insights_generation_failed", error_msg, {
            "model_type": model_type,
            "days": days
        }, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/profiles")
async def get_optimization_profiles():
    """Get available optimization profiles and their configurations"""
    
    try:
        profiles = {}
        
        # Get image optimization profiles
        for profile_key, profile in image_optimizer.optimization_profiles.items():
            profiles[profile_key] = {
                "type": "generation_parameters",
                "model_type": profile.model_type.value,
                "complexity_level": profile.complexity_level,
                "strategy": profile.strategy.value,
                "settings": {
                    "vram_allocation_percent": profile.vram_allocation_percent,
                    "optimal_batch_size": profile.optimal_batch_size,
                    "recommended_steps_range": profile.recommended_steps_range,
                    "optimal_cfg_range": profile.optimal_cfg_range,
                    "preferred_samplers": profile.preferred_samplers,
                    "performance_priority": profile.performance_priority
                }
            }
        
        # Get RTX 4070 Ti SUPER baselines
        hardware_baselines = {}
        for model_type, baseline in image_optimizer.rtx_4070ti_super_baselines.items():
            hardware_baselines[model_type.value] = baseline
        
        response = {
            "optimization_profiles": profiles,
            "hardware_baselines": hardware_baselines,
            "available_strategies": [strategy.value for strategy in OptimizationStrategy],
            "available_priorities": [priority.value for priority in OptimizationPriority]
        }
        
        log_activity("OPTIMIZATION_API", "Optimization profiles retrieved")
        
        return response
        
    except Exception as e:
        error_msg = f"Failed to get optimization profiles: {str(e)}"
        log_error("OPTIMIZATION_API", "profiles_retrieval_failed", error_msg, {}, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/profiles/rtx4070ti")
async def get_rtx_4070ti_optimized_params(request: OptimizationRequest):
    """Get RTX 4070 Ti SUPER optimized parameters for specific generation request"""
    
    try:
        log_activity("OPTIMIZATION_API", "RTX 4070 Ti SUPER optimization requested")
        
        # Detect model type
        model_type = image_optimizer._detect_model_type(request.generation_params.get("model", ""))
        
        # Get RTX 4070 Ti SUPER specific profile
        baseline = image_optimizer.rtx_4070ti_super_baselines.get(model_type, {})
        
        # Apply RTX-specific optimizations
        rtx_optimizations = image_optimizer._apply_rtx_4070ti_super_optimizations(
            request.generation_params, 
            model_type, 
            image_optimizer.optimization_profiles.get(f"{model_type.value}_balanced")
        )
        
        optimized_params = request.generation_params.copy()
        optimized_params.update(rtx_optimizations["parameters"])
        
        response = {
            "success": True,
            "model_type": model_type.value,
            "hardware_profile": "rtx_4070ti_super",
            "baseline_performance": baseline,
            "optimized_params": optimized_params,
            "rtx_optimizations_applied": rtx_optimizations["applied"],
            "performance_expectations": {
                "vram_usage_mb": baseline.get("vram_usage_mb", 8192),
                "generation_time_seconds": baseline.get("generation_time_seconds", 20.0),
                "optimal_batch_size": baseline.get("optimal_batch_size", 1),
                "max_resolution": baseline.get("max_resolution", [1024, 1024])
            }
        }
        
        log_activity("OPTIMIZATION_API", "RTX 4070 Ti SUPER optimization completed", {
            "model_type": model_type.value,
            "optimizations_count": len(rtx_optimizations["applied"])
        })
        
        return response
        
    except Exception as e:
        error_msg = f"RTX 4070 Ti SUPER optimization failed: {str(e)}"
        log_error("OPTIMIZATION_API", "rtx_optimization_failed", error_msg, {}, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/health")
async def optimization_health_check():
    """Health check for optimization services"""
    
    try:
        # Check if optimizers are working
        test_request = {
            "model": "test_model",
            "prompt": "test prompt",
            "width": 512,
            "height": 512,
            "steps": 25,
            "cfg_scale": 7.0
        }
        
        # Test parameter optimization
        try:
            await image_optimizer.optimize_generation_parameters(test_request)
            generation_optimizer_status = "healthy"
        except Exception as e:
            generation_optimizer_status = f"error: {str(e)}"
        
        # Test workflow optimization
        try:
            test_workflow = {"nodes": [], "links": []}
            await workflow_optimizer.optimize_workflow_execution(test_workflow, test_request)
            workflow_optimizer_status = "healthy"
        except Exception as e:
            workflow_optimizer_status = f"error: {str(e)}"
        
        # Check database connections
        try:
            await image_optimizer.get_optimization_insights(days=1)
            database_status = "healthy"
        except Exception as e:
            database_status = f"error: {str(e)}"
        
        health_status = {
            "status": "healthy" if all(
                status == "healthy" for status in [
                    generation_optimizer_status,
                    workflow_optimizer_status,
                    database_status
                ]
            ) else "degraded",
            "services": {
                "generation_optimizer": generation_optimizer_status,
                "workflow_optimizer": workflow_optimizer_status,
                "database": database_status
            },
            "gpu_monitoring_available": image_optimizer.gpu_monitoring_enabled,
            "active_optimizations": len(image_optimizer.active_optimizations),
            "cached_workflows": len(workflow_optimizer.workflow_cache)
        }
        
        log_activity("OPTIMIZATION_API", "Health check completed", health_status)
        
        return health_status
        
    except Exception as e:
        error_msg = f"Health check failed: {str(e)}"
        log_error("OPTIMIZATION_API", "health_check_failed", error_msg, {}, e)
        return {
            "status": "error",
            "error": error_msg
        }