import sys
import asyncio
import json
from pathlib import Path

# Add the path to your agents
sys.path.insert(0, r"G:\ZComfyUI\comfyui-custom-frontend\agent_orchestration\scripts")

# Import your UI agent
from ui_state_manager import execute

# Set up context for comprehensive UI analysis
context = {
    "agent": {"name": "ui-state-construction"},
    "task": {
        "name": "comprehensive_ui_analysis",
        "parameters": {}
    },
    "config": {
        "project_root": r"G:\ZComfyUI\comfyui-custom-frontend",
        "data_dir": r"G:\ZComfyUI\comfyui-custom-frontend\agent_orchestration\data",
        "databases": ["agent_execution.db"],
        "safety_mode": "autonomous",  # or "supervised"
        "rollback_enabled": True
    },
    "knowledge_bases": {}  # Add any relevant knowledge bases here
}

# Execute the comprehensive UI analysis
async def run_ui_analysis():
    try:
        result = await execute(context)
        print(json.dumps(result, indent=2))
        return result
    except Exception as e:
        print(f"Error running UI analysis: {e}")
        return None

# Run it
if __name__ == "__main__":
    result = asyncio.run(run_ui_analysis())
