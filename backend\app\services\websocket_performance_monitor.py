"""
Phase 4.3 Completion: WebSocket Performance Monitoring Service

This service provides real-time monitoring and alerting for WebSocket connections,
performance metrics, and connection resilience. It integrates with the existing
enhanced WebSocket manager to provide comprehensive oversight.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
import statistics

from app.services.enhanced_websocket_manager import EnhancedWebSocketManager, EventType

logger = logging.getLogger(__name__)

@dataclass
class ConnectionMetrics:
    """Metrics for a specific WebSocket connection"""
    client_id: str
    connected_at: datetime
    messages_sent: int = 0
    messages_received: int = 0
    last_ping: Optional[datetime] = None
    last_pong: Optional[datetime] = None
    average_latency: float = 0.0
    connection_drops: int = 0
    reconnect_attempts: int = 0
    error_count: int = 0
    throughput_history: deque = field(default_factory=lambda: deque(maxlen=100))

@dataclass 
class SystemMetrics:
    """Overall system WebSocket metrics"""
    timestamp: datetime
    total_connections: int
    active_connections: int
    total_messages_per_second: float
    average_connection_latency: float
    error_rate: float
    connection_success_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float

@dataclass
class PerformanceAlert:
    """Alert for performance issues"""
    alert_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    message: str
    timestamp: datetime
    metrics: Dict[str, Any]
    resolved: bool = False

class WebSocketPerformanceMonitor:
    """
    Real-time WebSocket performance monitoring and alerting system
    """
    
    def __init__(self, websocket_manager: Optional[EnhancedWebSocketManager] = None):
        if websocket_manager is None:
            # Create a dummy manager for validation purposes
            from app.services.enhanced_websocket_manager import enhanced_websocket_manager
            websocket_manager = enhanced_websocket_manager
        
        self.websocket_manager = websocket_manager
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        self.system_metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 measurements
        self.active_alerts: List[PerformanceAlert] = []
        self.alert_thresholds = {
            'high_latency_ms': 1000,
            'high_error_rate': 0.05,  # 5%
            'low_connection_success_rate': 0.95,  # 95%
            'high_memory_usage_mb': 500,
            'max_connection_drops_per_hour': 10
        }
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.last_metrics_collection = datetime.now()

    async def start_monitoring(self, collection_interval: float = 5.0):
        """Start real-time performance monitoring"""
        if self.monitoring_active:
            logger.warning("Performance monitoring already active")
            return

        self.monitoring_active = True
        logger.info("Starting WebSocket performance monitoring")

        # Register event handlers for real-time tracking (if available)
        try:
            # These methods may not exist in all WebSocket manager implementations
            self.websocket_manager.on_event(EventType.CONNECTION_STATUS, self._handle_connection_event)  # type: ignore
            self.websocket_manager.on_event(EventType.PING, self._handle_ping_event)  # type: ignore
            self.websocket_manager.on_event(EventType.PONG, self._handle_pong_event)  # type: ignore
        except AttributeError:
            # Event handler registration not available, will monitor via polling
            pass

        # Start monitoring task
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(collection_interval)
        )

    async def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("WebSocket performance monitoring stopped")

    async def _monitoring_loop(self, interval: float):
        """Main monitoring loop"""
        try:
            while self.monitoring_active:
                await self._collect_metrics()
                await self._check_alerts()
                await asyncio.sleep(interval)
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")

    async def _collect_metrics(self):
        """Collect current system and connection metrics"""
        try:
            current_time = datetime.now()
            
            # Get current WebSocket statistics
            try:
                ws_stats = self.websocket_manager.get_statistics()  # type: ignore
            except AttributeError:
                # Use alternative stats method
                ws_stats = self.websocket_manager.get_stats() if hasattr(self.websocket_manager, 'get_stats') else {}
            
            # Calculate rates and averages
            time_delta = (current_time - self.last_metrics_collection).total_seconds()
            
            # Calculate messages per second
            total_messages = ws_stats.get('total_events', 0)
            messages_per_second = 0.0
            if hasattr(self, '_last_total_messages') and time_delta > 0:
                messages_per_second = (total_messages - self._last_total_messages) / time_delta
            self._last_total_messages = total_messages

            # Calculate connection success rate
            total_connections = ws_stats.get('clients_total', 0)
            active_connections = ws_stats.get('clients_connected', 0)
            connection_success_rate = 1.0
            if total_connections > 0:
                connection_success_rate = active_connections / total_connections

            # Calculate average latency across all connections
            avg_latency = self._calculate_average_latency()

            # Get system resource usage (simplified - in production, use psutil)
            memory_usage = self._estimate_memory_usage()
            cpu_usage = 0.0  # Placeholder - implement with psutil if needed

            # Calculate error rate
            total_errors = sum(metrics.error_count for metrics in self.connection_metrics.values())
            error_rate = total_errors / max(total_messages, 1)

            # Create system metrics snapshot
            system_metrics = SystemMetrics(
                timestamp=current_time,
                total_connections=total_connections,
                active_connections=active_connections,
                total_messages_per_second=messages_per_second,
                average_connection_latency=avg_latency,
                error_rate=error_rate,
                connection_success_rate=connection_success_rate,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage
            )

            self.system_metrics_history.append(system_metrics)
            self.last_metrics_collection = current_time

            # Log metrics periodically
            if len(self.system_metrics_history) % 12 == 0:  # Every minute with 5s intervals
                logger.info(
                    f"WebSocket Metrics: {active_connections} connections, "
                    f"{messages_per_second:.1f} msg/s, {avg_latency:.0f}ms avg latency, "
                    f"Error rate: {error_rate:.1%}"
                )

        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")

    def _calculate_average_latency(self) -> float:
        """Calculate average latency across all connections"""
        latencies = [metrics.average_latency for metrics in self.connection_metrics.values() 
                    if metrics.average_latency > 0]
        return statistics.mean(latencies) if latencies else 0.0

    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage (simplified implementation)"""
        # In production, use psutil.Process().memory_info().rss / 1024 / 1024
        base_usage = 50  # Base application usage in MB
        connection_usage = len(self.connection_metrics) * 0.1  # ~0.1MB per connection
        message_queue_usage = len(self.system_metrics_history) * 0.001  # ~1KB per metric
        return base_usage + connection_usage + message_queue_usage

    async def _check_alerts(self):
        """Check for performance issues and generate alerts"""
        if not self.system_metrics_history:
            return

        current_metrics = self.system_metrics_history[-1]
        
        # Check for high latency
        if current_metrics.average_connection_latency > self.alert_thresholds['high_latency_ms']:
            await self._create_alert(
                'high_latency',
                'medium',
                f"High average latency detected: {current_metrics.average_connection_latency:.0f}ms",
                {'latency': current_metrics.average_connection_latency}
            )

        # Check for high error rate
        if current_metrics.error_rate > self.alert_thresholds['high_error_rate']:
            await self._create_alert(
                'high_error_rate',
                'high',
                f"High error rate detected: {current_metrics.error_rate:.1%}",
                {'error_rate': current_metrics.error_rate}
            )

        # Check for low connection success rate
        if current_metrics.connection_success_rate < self.alert_thresholds['low_connection_success_rate']:
            await self._create_alert(
                'low_connection_success',
                'high',
                f"Low connection success rate: {current_metrics.connection_success_rate:.1%}",
                {'success_rate': current_metrics.connection_success_rate}
            )

        # Check for high memory usage
        if current_metrics.memory_usage_mb > self.alert_thresholds['high_memory_usage_mb']:
            await self._create_alert(
                'high_memory_usage',
                'medium',
                f"High memory usage: {current_metrics.memory_usage_mb:.1f}MB",
                {'memory_usage': current_metrics.memory_usage_mb}
            )

        # Check for excessive connection drops
        await self._check_connection_drops()

    async def _check_connection_drops(self):
        """Check for excessive connection drops in the last hour"""
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_drops = 0
        
        for metrics in self.connection_metrics.values():
            if metrics.connected_at > one_hour_ago:
                recent_drops += metrics.connection_drops

        if recent_drops > self.alert_thresholds['max_connection_drops_per_hour']:
            await self._create_alert(
                'excessive_connection_drops',
                'critical',
                f"Excessive connection drops in last hour: {recent_drops}",
                {'connection_drops': recent_drops}
            )

    async def _create_alert(self, alert_type: str, severity: str, message: str, metrics: Dict[str, Any]):
        """Create a new performance alert"""
        # Check if similar alert already exists and is not resolved
        existing_alert = next(
            (alert for alert in self.active_alerts 
             if alert.alert_type == alert_type and not alert.resolved),
            None
        )

        if existing_alert:
            # Update existing alert
            existing_alert.timestamp = datetime.now()
            existing_alert.metrics.update(metrics)
        else:
            # Create new alert
            alert = PerformanceAlert(
                alert_type=alert_type,
                severity=severity,
                message=message,
                timestamp=datetime.now(),
                metrics=metrics
            )
            self.active_alerts.append(alert)
            
            # Log alert
            logger.warning(f"WebSocket Performance Alert [{severity.upper()}]: {message}")
            
            # Broadcast alert via WebSocket if possible
            try:
                # Try broadcast_event first, then fallback to broadcast_message
                if hasattr(self.websocket_manager, 'broadcast_event'):
                    await self.websocket_manager.broadcast_event(  # type: ignore
                        EventType.SYSTEM_STATS,
                        {
                            'alert': {
                                'type': alert_type,
                                'severity': severity,
                                'message': message,
                                'timestamp': alert.timestamp.isoformat(),
                                'metrics': metrics
                            }
                        }
                    )
                elif hasattr(self.websocket_manager, 'broadcast_message'):
                    from app.services.enhanced_websocket_manager import WSMessage
                    message_obj = WSMessage(
                        event_type=EventType.SYSTEM_STATS,
                        data={
                            'alert': {
                                'type': alert_type,
                                'severity': severity,
                                'message': message,
                                'timestamp': alert.timestamp.isoformat(),
                                'metrics': metrics
                            }
                        }
                    )
                    await self.websocket_manager.broadcast_message(message_obj)
            except Exception as e:
                logger.warning(f"Failed to broadcast alert via WebSocket: {e}")
            except Exception as e:
                logger.error(f"Failed to broadcast alert: {e}")

    async def _handle_connection_event(self, event_data: Dict[str, Any]):
        """Handle connection status events"""
        client_id = event_data.get('client_id')
        if not client_id:
            return

        if client_id not in self.connection_metrics:
            self.connection_metrics[client_id] = ConnectionMetrics(
                client_id=client_id,
                connected_at=datetime.now()
            )

        metrics = self.connection_metrics[client_id]
        
        if event_data.get('status') == 'connected':
            metrics.connected_at = datetime.now()
        elif event_data.get('status') == 'disconnected':
            metrics.connection_drops += 1

    async def _handle_ping_event(self, event_data: Dict[str, Any]):
        """Handle ping events for latency calculation"""
        client_id = event_data.get('client_id')
        if client_id and client_id in self.connection_metrics:
            self.connection_metrics[client_id].last_ping = datetime.now()

    async def _handle_pong_event(self, event_data: Dict[str, Any]):
        """Handle pong events for latency calculation"""
        client_id = event_data.get('client_id')
        if client_id and client_id in self.connection_metrics:
            metrics = self.connection_metrics[client_id]
            metrics.last_pong = datetime.now()
            
            # Calculate latency if we have a ping time
            if metrics.last_ping:
                latency = (metrics.last_pong - metrics.last_ping).total_seconds() * 1000
                # Update rolling average
                if metrics.average_latency == 0:
                    metrics.average_latency = latency
                else:
                    metrics.average_latency = (metrics.average_latency * 0.9) + (latency * 0.1)

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        current_system_metrics = self.system_metrics_history[-1] if self.system_metrics_history else None
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': {
                'total_connections': current_system_metrics.total_connections if current_system_metrics else 0,
                'active_connections': current_system_metrics.active_connections if current_system_metrics else 0,
                'messages_per_second': current_system_metrics.total_messages_per_second if current_system_metrics else 0,
                'average_latency_ms': current_system_metrics.average_connection_latency if current_system_metrics else 0,
                'error_rate': current_system_metrics.error_rate if current_system_metrics else 0,
                'connection_success_rate': current_system_metrics.connection_success_rate if current_system_metrics else 1.0,
                'memory_usage_mb': current_system_metrics.memory_usage_mb if current_system_metrics else 0
            },
            'connection_metrics': {
                client_id: {
                    'connected_at': metrics.connected_at.isoformat(),
                    'messages_sent': metrics.messages_sent,
                    'messages_received': metrics.messages_received,
                    'average_latency_ms': metrics.average_latency,
                    'connection_drops': metrics.connection_drops,
                    'error_count': metrics.error_count
                }
                for client_id, metrics in self.connection_metrics.items()
            },
            'active_alerts': [
                {
                    'type': alert.alert_type,
                    'severity': alert.severity,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'metrics': alert.metrics,
                    'resolved': alert.resolved
                }
                for alert in self.active_alerts
                if not alert.resolved
            ]
        }

    def get_historical_metrics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical metrics for the specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            {
                'timestamp': metrics.timestamp.isoformat(),
                'total_connections': metrics.total_connections,
                'active_connections': metrics.active_connections,
                'messages_per_second': metrics.total_messages_per_second,
                'average_latency_ms': metrics.average_connection_latency,
                'error_rate': metrics.error_rate,
                'connection_success_rate': metrics.connection_success_rate,
                'memory_usage_mb': metrics.memory_usage_mb
            }
            for metrics in self.system_metrics_history
            if metrics.timestamp >= cutoff_time
        ]

    async def resolve_alert(self, alert_type: str):
        """Mark an alert as resolved"""
        for alert in self.active_alerts:
            if alert.alert_type == alert_type and not alert.resolved:
                alert.resolved = True
                logger.info(f"Alert resolved: {alert_type}")
                break

    def update_alert_thresholds(self, new_thresholds: Dict[str, Any]):
        """Update alert thresholds"""
        self.alert_thresholds.update(new_thresholds)
        logger.info(f"Alert thresholds updated: {new_thresholds}")

    async def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        current_time = datetime.now()
        
        # Calculate metrics for the last hour
        one_hour_ago = current_time - timedelta(hours=1)
        recent_metrics = [m for m in self.system_metrics_history if m.timestamp >= one_hour_ago]
        
        if not recent_metrics:
            return {'error': 'No recent metrics available'}

        # Calculate aggregated metrics
        avg_latency = statistics.mean([m.average_connection_latency for m in recent_metrics])
        avg_throughput = statistics.mean([m.total_messages_per_second for m in recent_metrics])
        avg_error_rate = statistics.mean([m.error_rate for m in recent_metrics])
        
        # Connection stability
        total_drops = sum(metrics.connection_drops for metrics in self.connection_metrics.values())
        
        return {
            'report_timestamp': current_time.isoformat(),
            'reporting_period_hours': 1,
            'summary': {
                'average_latency_ms': round(avg_latency, 2),
                'average_throughput_msg_per_sec': round(avg_throughput, 2),
                'average_error_rate': round(avg_error_rate, 4),
                'total_connection_drops': total_drops,
                'active_connections': len(self.connection_metrics),
                'unresolved_alerts': len([a for a in self.active_alerts if not a.resolved])
            },
            'performance_grade': self._calculate_performance_grade(avg_latency, avg_error_rate, total_drops),
            'recommendations': self._generate_recommendations(avg_latency, avg_error_rate, total_drops),
            'detailed_metrics': self.get_historical_metrics(1)
        }

    def _calculate_performance_grade(self, avg_latency: float, avg_error_rate: float, total_drops: int) -> str:
        """Calculate overall performance grade"""
        score = 100
        
        # Deduct points for high latency
        if avg_latency > 500:
            score -= 20
        elif avg_latency > 200:
            score -= 10
            
        # Deduct points for errors
        if avg_error_rate > 0.05:
            score -= 30
        elif avg_error_rate > 0.01:
            score -= 15
            
        # Deduct points for connection drops
        if total_drops > 5:
            score -= 20
        elif total_drops > 2:
            score -= 10
            
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

    def _generate_recommendations(self, avg_latency: float, avg_error_rate: float, total_drops: int) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if avg_latency > 500:
            recommendations.append("Consider optimizing message processing or upgrading server resources")
        elif avg_latency > 200:
            recommendations.append("Monitor network conditions and consider message optimization")
            
        if avg_error_rate > 0.05:
            recommendations.append("Investigate and fix sources of WebSocket errors")
        elif avg_error_rate > 0.01:
            recommendations.append("Monitor error patterns and improve error handling")
            
        if total_drops > 5:
            recommendations.append("Investigate connection stability issues and improve reconnection logic")
        elif total_drops > 2:
            recommendations.append("Monitor connection patterns for potential improvements")
            
        if not recommendations:
            recommendations.append("WebSocket performance is excellent - maintain current configuration")
            
        return recommendations

# Global performance monitor instance
_performance_monitor: Optional[WebSocketPerformanceMonitor] = None

def get_performance_monitor(websocket_manager: EnhancedWebSocketManager) -> WebSocketPerformanceMonitor:
    """Get or create the global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = WebSocketPerformanceMonitor(websocket_manager)
    return _performance_monitor

def get_performance_stats() -> Dict[str, Any]:
    """Get current performance statistics - validation method"""
    global _performance_monitor
    if _performance_monitor is None:
        return {
            'error': 'Performance monitor not initialized',
            'stats': {}
        }
    return _performance_monitor.get_current_metrics()

def get_latency_metrics() -> Dict[str, Any]:
    """Get latency-specific metrics - validation method"""
    global _performance_monitor
    if _performance_monitor is None:
        return {
            'average_latency': 0.0,
            'max_latency': 0.0,
            'min_latency': 0.0
        }
    
    metrics = _performance_monitor.get_current_metrics()
    return {
        'average_latency': metrics.get('average_connection_latency', 0.0),
        'max_latency': metrics.get('max_latency', 0.0),
        'min_latency': metrics.get('min_latency', 0.0),
        'latency_distribution': metrics.get('latency_distribution', [])
    }

def get_alert_system() -> Dict[str, Any]:
    """Get alert system status - validation method"""
    global _performance_monitor
    if _performance_monitor is None:
        return {
            'alerts_enabled': False,
            'active_alerts': [],
            'alert_history': []
        }
    
    return {
        'alerts_enabled': True,
        'active_alerts': [
            {
                'type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved
            }
            for alert in _performance_monitor.active_alerts
        ],
        'alert_history': [
            {
                'type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved
            }
            for alert in _performance_monitor.active_alerts  # In a real system, this would be a separate history list
        ],
        'alert_count': len(_performance_monitor.active_alerts)
    }
