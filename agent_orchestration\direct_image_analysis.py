#!/usr/bin/env python3
"""
Direct Image Analysis Execution for Image_Analysis Folder
Implements the README requirements directly without complex agent framework
"""

import asyncio
import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from PIL import Image, PngImagePlugin
from typing import Dict, List, Any, Optional
import hashlib

class DirectImageAnalyzer:
    """Direct implementation of image analysis following README specifications."""
    
    def __init__(self, target_folder: str, verified_workflows_folder: str):
        self.target_folder = Path(target_folder)
        self.verified_workflows_folder = Path(verified_workflows_folder)
        self.processed_folder = self.target_folder / "Processed"
        self.processed_folder.mkdir(exist_ok=True)
        
        # Create verified workflows folder if it doesn't exist
        self.verified_workflows_folder.mkdir(parents=True, exist_ok=True)
        
        # Initialize result storage
        self.analysis_results = []
        self.style_guides = {}
        self.workflow_extraction_log = []
        self.workflow_index = []
        
        # Statistics tracking
        self.stats = {
            "total_images": 0,
            "workflows_extracted": 0,
            "styles_identified": set(),
            "files_moved": 0,
            "analysis_start": datetime.now()
        }
    
    async def execute_comprehensive_analysis(self):
        """Execute the full analysis following README specifications."""
        print("[START] Beginning comprehensive image analysis...")
        
        # Step 1: Folder scan
        image_files = await self.scan_target_folder()
        print(f"[SCAN] Found {len(image_files)} images to analyze")
        
        # Step 2 & 3: Workflow extraction and individual analysis
        for i, image_path in enumerate(image_files, 1):
            print(f"[ANALYZE] Processing image {i}/{len(image_files)}: {image_path.name}")
            
            # Extract workflow from image
            workflow_result = await self.extract_workflow_from_image(image_path)
            
            # Perform individual analysis
            analysis_result = await self.analyze_single_image(image_path, workflow_result)
            self.analysis_results.append(analysis_result)
            
            # Move to processed folder
            await self.move_to_processed(image_path)
            
            print(f"    [OK] Analysis complete - Workflow: {'Yes' if workflow_result['extracted'] else 'No'}")
        
        # Step 4: Style categorization
        await self.categorize_styles()
        
        # Step 5: Generate outputs
        await self.generate_output_files()
        
        # Step 6: Final statistics
        await self.generate_final_report()
        
        return True
    
    async def scan_target_folder(self) -> List[Path]:
        """Scan target folder and identify all image files."""
        image_extensions = {'.png', '.jpg', '.jpeg', '.webp', '.bmp', '.tiff'}
        image_files = []
        
        for file_path in self.target_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(file_path)
        
        self.stats["total_images"] = len(image_files)
        return sorted(image_files)
    
    async def extract_workflow_from_image(self, image_path: Path) -> Dict[str, Any]:
        """Extract ComfyUI workflow from image metadata."""
        result = {
            "extracted": False,
            "workflow_data": None,
            "extraction_method": "none",
            "workflow_file_path": None,
            "error": None
        }
        
        try:
            # Try PNG metadata extraction
            if image_path.suffix.lower() == '.png':
                with Image.open(image_path) as img:
                    if hasattr(img, 'text') and img.text:
                        # Check common workflow fields
                        workflow_fields = ['workflow', 'Workflow', 'ComfyUI_workflow']
                        for field in workflow_fields:
                            if field in img.text:
                                try:
                                    workflow_json = json.loads(img.text[field])
                                    # Validate basic structure
                                    if self.validate_workflow_structure(workflow_json):
                                        result["extracted"] = True
                                        result["workflow_data"] = workflow_json
                                        result["extraction_method"] = f"PNG_{field}"
                                        
                                        # Save workflow to verified folder
                                        workflow_path = await self.save_workflow_to_verified_folder(
                                            workflow_json, image_path
                                        )
                                        result["workflow_file_path"] = str(workflow_path)
                                        self.stats["workflows_extracted"] += 1
                                        break
                                except json.JSONDecodeError:
                                    continue
        
        except Exception as e:
            result["error"] = str(e)
        
        # Log extraction attempt
        self.workflow_extraction_log.append({
            "timestamp": datetime.now().isoformat(),
            "source_image": str(image_path),
            "workflow_extracted": result["extracted"],
            "extraction_method": result["extraction_method"],
            "workflow_saved_path": result["workflow_file_path"],
            "error": result["error"]
        })
        
        return result
    
    def validate_workflow_structure(self, workflow_data: Dict) -> bool:
        """Validate extracted workflow has required structure."""
        required_fields = ['nodes', 'links']
        return all(field in workflow_data for field in required_fields)
    
    async def save_workflow_to_verified_folder(self, workflow_data: Dict, source_image: Path) -> Path:
        """Save validated workflow to verified_workflows folder."""
        # Generate descriptive filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        source_name = source_image.stem
        
        # Simple model type detection
        model_type = "unknown"
        if isinstance(workflow_data.get('nodes'), list):
            for node in workflow_data['nodes']:
                if isinstance(node, dict) and 'type' in node:
                    node_type = node['type'].lower()
                    if 'flux' in node_type:
                        model_type = "flux"
                        break
                    elif 'sdxl' in node_type:
                        model_type = "sdxl"
                        break
                    elif any(sd_type in node_type for sd_type in ['sd15', 'stable_diffusion']):
                        model_type = "sd15"
                        break
        
        filename = f"{timestamp}_{model_type}_{source_name}_workflow.json"
        workflow_path = self.verified_workflows_folder / filename
        
        # Check for duplicates using hash
        workflow_hash = hashlib.md5(
            json.dumps(workflow_data, sort_keys=True).encode()
        ).hexdigest()
        
        # Enhanced workflow with metadata
        enhanced_workflow = {
            "comfyui_workflow": workflow_data,
            "metadata": {
                "extracted_from": str(source_image),
                "extraction_timestamp": datetime.now().isoformat(),
                "model_type": model_type,
                "workflow_hash": workflow_hash,
                "node_count": len(workflow_data.get('nodes', [])),
                "extractor_version": "2.2"
            }
        }
        
        # Save workflow
        with open(workflow_path, 'w') as f:
            json.dump(enhanced_workflow, f, indent=2)
        
        # Update workflow index
        self.workflow_index.append({
            "workflow_id": workflow_hash[:8],
            "filename": filename,
            "source_image": source_image.name,
            "extraction_date": datetime.now().isoformat(),
            "model_type": model_type,
            "node_count": len(workflow_data.get('nodes', [])),
            "workflow_hash": workflow_hash
        })
        
        return workflow_path
    
    async def analyze_single_image(self, image_path: Path, workflow_result: Dict) -> Dict[str, Any]:
        """Perform comprehensive analysis on a single image."""
        try:
            with Image.open(image_path) as img:
                # Basic technical info
                width, height = img.size
                file_size = image_path.stat().st_size
                
                # Convert to RGB if needed for analysis
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Style analysis
                style_analysis = await self.analyze_image_style(img)
                
                # Generate replication blueprint
                replication_blueprint = await self.create_replication_blueprint(
                    img, style_analysis, workflow_result
                )
                
                return {
                    "image_filename": image_path.name,
                    "technical_info": {
                        "dimensions": f"{width}x{height}",
                        "format": img.format or image_path.suffix[1:].upper(),
                        "file_size_bytes": file_size,
                        "color_mode": img.mode
                    },
                    "style_analysis": style_analysis,
                    "replication_blueprint": replication_blueprint,
                    "workflow_integration": {
                        "workflow_extracted": workflow_result["extracted"],
                        "workflow_file_path": workflow_result["workflow_file_path"],
                        "extraction_method": workflow_result["extraction_method"]
                    },
                    "analysis_timestamp": datetime.now().isoformat()
                }
        
        except Exception as e:
            return {
                "image_filename": image_path.name,
                "error": f"Analysis failed: {str(e)}",
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    async def analyze_image_style(self, img: Image.Image) -> Dict[str, Any]:
        """Analyze image style and artistic characteristics."""
        # Simple color analysis
        colors = img.getcolors(maxcolors=256*256*256)
        if colors:
            # Get dominant colors
            dominant_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5]
            color_palette = [f"#{r:02x}{g:02x}{b:02x}" for _, (r, g, b) in dominant_colors]
        else:
            color_palette = ["#000000"]  # Fallback
        
        # Basic style categorization
        style_category = "photorealistic"  # Default
        if len(color_palette) <= 3:
            style_category = "minimalist"
        elif any(len(set([r, g, b])) == 1 for _, (r, g, b) in dominant_colors[:3]):
            style_category = "monochrome"
        
        self.stats["styles_identified"].add(style_category)
        
        return {
            "primary_style": style_category,
            "color_palette": color_palette,
            "color_temperature": "neutral",  # Simplified
            "composition_type": "centered",  # Simplified
            "artistic_movement": "contemporary",
            "complexity_score": min(len(color_palette) / 10.0, 1.0),
            "confidence_score": 75.0  # Simplified scoring
        }
    
    async def create_replication_blueprint(self, img: Image.Image, style_analysis: Dict, workflow_result: Dict) -> Dict[str, Any]:
        """Create comprehensive replication instructions."""
        style = style_analysis["primary_style"]
        
        # Generate base prompt based on style
        base_prompts = {
            "photorealistic": "high quality photograph, professional photography, detailed, sharp focus",
            "minimalist": "minimalist style, clean composition, simple design, elegant",
            "monochrome": "monochrome, black and white, high contrast, dramatic lighting",
            "artistic": "artistic style, creative composition, expressive, vibrant colors"
        }
        
        base_prompt = base_prompts.get(style, "high quality, detailed")
        
        # Model recommendations
        model_recommendations = {
            "primary": "Flux Dev",
            "secondary": "SDXL Base 1.0",
            "fallback": "Stable Diffusion 1.5"
        }
        
        # Include workflow reference if available
        workflow_reference = None
        if workflow_result["extracted"]:
            workflow_reference = {
                "workflow_file": workflow_result["workflow_file_path"],
                "note": "Use extracted workflow for exact replication"
            }
        
        return {
            "base_prompt": base_prompt,
            "style_modifiers": [style, style_analysis["color_temperature"]],
            "negative_prompt": "low quality, blurry, distorted, artifacts",
            "model_recommendations": model_recommendations,
            "estimated_settings": {
                "steps": 30,
                "cfg_scale": 7.0,
                "resolution": "1024x1024"
            },
            "replication_confidence": style_analysis["confidence_score"],
            "workflow_reference": workflow_reference
        }
    
    async def categorize_styles(self):
        """Group images by identified styles and create style guides."""
        style_categories = {}
        
        for analysis in self.analysis_results:
            if "error" not in analysis:
                style = analysis["style_analysis"]["primary_style"]
                if style not in style_categories:
                    style_categories[style] = []
                style_categories[style].append(analysis)
        
        # Create style guides for each category
        for style, images in style_categories.items():
            self.style_guides[style] = {
                "style_name": style,
                "image_count": len(images),
                "common_characteristics": await self.analyze_style_patterns(images),
                "generation_guide": await self.create_style_generation_guide(style, images),
                "example_images": [img["image_filename"] for img in images[:3]]
            }
    
    async def analyze_style_patterns(self, images: List[Dict]) -> Dict[str, Any]:
        """Analyze common patterns in a style category."""
        color_palettes = []
        confidence_scores = []
        
        for img in images:
            if "style_analysis" in img:
                color_palettes.extend(img["style_analysis"]["color_palette"])
                confidence_scores.append(img["style_analysis"]["confidence_score"])
        
        return {
            "common_colors": list(set(color_palettes))[:10],
            "average_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
            "pattern_count": len(images)
        }
    
    async def create_style_generation_guide(self, style: str, images: List[Dict]) -> Dict[str, Any]:
        """Create generation guide for a specific style."""
        # Count workflow references
        workflow_count = sum(1 for img in images if img.get("workflow_integration", {}).get("workflow_extracted", False))
        
        return {
            "recommended_prompts": [
                f"{style} style, high quality, detailed",
                f"professional {style} composition",
                f"{style} aesthetic, well-composed"
            ],
            "model_optimization": {
                "flux_dev": "Best for photorealistic and detailed styles",
                "sdxl_base": "Good for artistic and stylized content", 
                "sd_15": "Fallback option with lower resource requirements"
            },
            "workflow_references": f"{workflow_count} extracted workflows available for this style",
            "generation_tips": [
                f"Use {style}-specific prompts for best results",
                "Adjust CFG scale based on style complexity",
                "Consider extracted workflows for exact replication"
            ]
        }
    
    async def move_to_processed(self, image_path: Path):
        """Move analyzed image to processed folder."""
        try:
            destination = self.processed_folder / image_path.name
            shutil.move(str(image_path), str(destination))
            self.stats["files_moved"] += 1
        except Exception as e:
            print(f"    Warning: Could not move {image_path.name}: {e}")
    
    async def generate_output_files(self):
        """Generate the required output files."""
        # 1A: Image Analysis Results
        results_output = {
            "executive_summary": {
                "total_images_analyzed": self.stats["total_images"],
                "workflows_extracted": self.stats["workflows_extracted"],
                "unique_styles_identified": len(self.stats["styles_identified"]),
                "files_moved_to_processed": self.stats["files_moved"],
                "analysis_completion_time": datetime.now().isoformat(),
                "extraction_success_rate": f"{(self.stats['workflows_extracted'] / self.stats['total_images'] * 100):.1f}%" if self.stats["total_images"] > 0 else "0%"
            },
            "workflow_extraction_summary": {
                "total_attempts": len(self.workflow_extraction_log),
                "successful_extractions": self.stats["workflows_extracted"],
                "extraction_methods_used": list(set(log["extraction_method"] for log in self.workflow_extraction_log if log["workflow_extracted"])),
                "workflows_saved_to": str(self.verified_workflows_folder)
            },
            "individual_image_analyses": self.analysis_results,
            "style_distribution": {style: len([a for a in self.analysis_results if a.get("style_analysis", {}).get("primary_style") == style]) for style in self.stats["styles_identified"]},
            "file_management_log": {
                "processed_folder": str(self.processed_folder),
                "files_moved": self.stats["files_moved"],
                "movement_timestamp": datetime.now().isoformat()
            }
        }
        
        # Save 1A results
        results_file = self.target_folder / "1A-Image_Analysis_Results_List.json"
        with open(results_file, 'w') as f:
            json.dump(results_output, f, indent=2)
        
        # 1B: Style Guides
        style_guides_output = {
            "comprehensive_style_catalog": self.style_guides,
            "workflow_optimization": {
                "extracted_workflows_count": self.stats["workflows_extracted"],
                "workflow_index_location": str(self.verified_workflows_folder / "workflow_index.json"),
                "workflow_integration_notes": "Use extracted workflows for exact style replication"
            },
            "technical_optimization_matrix": {
                "rtx_4070_ti_super": {
                    "recommended_resolution": "1024x1024",
                    "optimal_batch_size": 1,
                    "memory_efficient_settings": "Enable attention slicing for larger images"
                }
            },
            "generation_timestamp": datetime.now().isoformat()
        }
        
        # Save 1B style guides
        style_guides_file = self.target_folder / "1B-Style_Guides.json"
        with open(style_guides_file, 'w') as f:
            json.dump(style_guides_output, f, indent=2)
        
        # Workflow extraction log
        log_file = self.target_folder / "Workflow_Extraction_Log.json"
        with open(log_file, 'w') as f:
            json.dump(self.workflow_extraction_log, f, indent=2)
        
        # Workflow index
        if self.workflow_index:
            index_file = self.verified_workflows_folder / "workflow_index.json"
            with open(index_file, 'w') as f:
                json.dump(self.workflow_index, f, indent=2)
        
        print(f"[OUTPUT] Generated files:")
        print(f"  - {results_file}")
        print(f"  - {style_guides_file}")
        print(f"  - {log_file}")
        if self.workflow_index:
            print(f"  - {index_file}")
    
    async def generate_final_report(self):
        """Generate final execution report."""
        end_time = datetime.now()
        duration = end_time - self.stats["analysis_start"]
        
        print(f"\n[FINAL REPORT] Image Analysis Complete")
        print(f"=" * 50)
        print(f"Images Processed: {self.stats['total_images']}")
        print(f"Workflows Extracted: {self.stats['workflows_extracted']}")
        print(f"Success Rate: {(self.stats['workflows_extracted'] / self.stats['total_images'] * 100):.1f}%" if self.stats["total_images"] > 0 else "0%")
        print(f"Styles Identified: {len(self.stats['styles_identified'])}")
        print(f"Files Moved: {self.stats['files_moved']}")
        print(f"Analysis Duration: {duration}")
        print(f"Processed Folder: {self.processed_folder}")
        print(f"Verified Workflows: {self.verified_workflows_folder}")
        print(f"=" * 50)

async def main():
    """Main execution function."""
    print("Direct Image Analysis Execution")
    print("=" * 50)
    print("Following 2A-README.md specifications")
    print(f"Start Time: {datetime.now().isoformat()}")
    print("=" * 50)
    
    # Initialize analyzer
    analyzer = DirectImageAnalyzer(
        target_folder="G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis",
        verified_workflows_folder="G:/ZComfyUI/comfyui-custom-frontend/verified_workflows"
    )
    
    try:
        # Execute comprehensive analysis
        await analyzer.execute_comprehensive_analysis()
        return True
    except Exception as e:
        print(f"[ERROR] Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)