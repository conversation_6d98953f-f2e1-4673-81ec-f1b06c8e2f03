import httpx
import json
import asyncio
from typing import Dict, List, Optional, Any
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class OllamaService:
    def __init__(self):
        self.base_url = settings.OLLAMA_API_URL
        self.default_model = settings.OLLAMA_DEFAULT_MODEL
        
    async def check_connection(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/tags")
                return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama connection check failed: {e}")
            return False
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """Get list of available Ollama models"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/tags")
                if response.status_code == 200:
                    data = response.json()
                    return data.get("models", [])
                return []
        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return []
    
    async def generate_response(self, 
                              prompt: str, 
                              model: str = None,
                              system_prompt: str = "",
                              temperature: float = 0.7,
                              max_tokens: int = 2000) -> str:
        """Generate a response using Ollama"""
        if model is None:
            model = self.default_model
            
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("response", "")
                else:
                    logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                    raise Exception(f"Ollama API error: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error generating response with Ollama: {e}")
            raise
    
    async def generate_creative_prompt(self, 
                                     base_theme: str = "",
                                     style: str = "photorealistic",
                                     mood: str = "dramatic",
                                     lighting: str = "cinematic") -> Dict[str, Any]:
        """Generate a creative prompt using the AI Creative Mode logic"""
        
        # Generate random letters for theme
        import random
        import string
        
        letter1 = random.choice(string.ascii_uppercase)
        letter2 = random.choice(string.ascii_uppercase)
        
        system_prompt = """You are a professional artist and AI art image technician. You create Masterclass artwork through ComfyUI and Flux.

You will be generating FLUX.1 dev prompts for high-quality, professional art pieces using this process:

1. Dynamically come up with an original art composition idea utilizing your knowledge of artwork, composition, lighting, photography, science, human psychology and anything that yields high quality, professional and visually appealing art
2. Decide if there are any reference images that could help direct the composition (generate 0-2 Google image search terms)
3. If there are LoRAs compatible with FLUX models that would enhance the image, provide them (up to 2)
4. Use best FLUX prompting techniques:
   - Up to 8 descriptive single word techniques (volumetric lighting, ray tracing, subsurface scattering, reflections, etc)
   - Up to 2 camera descriptor techniques (Canon EOS R5, 85mm, etc)
   - Describe the composition with natural language

Return your response as a JSON object with these fields:
- "theme": the main subject/theme
- "composition": detailed composition description
- "prompt": the final FLUX prompt
- "negative_prompt": negative prompt
- "reference_searches": array of 0-2 Google search terms
- "suggested_loras": array of 0-2 LoRA suggestions
- "technical_tags": array of technical enhancement tags
- "camera_settings": array of camera/lens specifications"""

        user_prompt = f"""Create a creative prompt using these parameters:
- Random letters: {letter1}, {letter2} (find a noun containing both letters as the main theme)
- Base theme: {base_theme}
- Style preference: {style}
- Mood: {mood}
- Lighting: {lighting}

Generate a professional, high-quality art prompt following the process outlined."""

        try:
            response = await self.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.8,
                max_tokens=1500
            )
            
            # Try to parse as JSON, fallback to structured text
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # Parse structured response manually
                return self._parse_creative_response(response, letter1, letter2)
                
        except Exception as e:
            logger.error(f"Error generating creative prompt: {e}")
            # Return a fallback creative prompt
            return self._generate_fallback_prompt(letter1, letter2, base_theme, style, mood, lighting)
    
    def _parse_creative_response(self, response: str, letter1: str, letter2: str) -> Dict[str, Any]:
        """Parse a non-JSON creative response"""
        lines = response.split('\n')
        result = {
            "theme": f"Theme with {letter1} and {letter2}",
            "composition": "Professional artistic composition",
            "prompt": response[:500],  # Use first part as prompt
            "negative_prompt": "blurry, low quality, distorted",
            "reference_searches": [],
            "suggested_loras": [],
            "technical_tags": ["volumetric lighting", "ray tracing", "high detail"],
            "camera_settings": ["Canon EOS R5", "85mm f/1.4"]
        }
        
        # Try to extract structured information
        for line in lines:
            line = line.strip()
            if line.lower().startswith('theme:'):
                result["theme"] = line[6:].strip()
            elif line.lower().startswith('prompt:'):
                result["prompt"] = line[7:].strip()
            elif line.lower().startswith('composition:'):
                result["composition"] = line[12:].strip()
        
        return result
    
    def _generate_fallback_prompt(self, letter1: str, letter2: str, base_theme: str, 
                                style: str, mood: str, lighting: str) -> Dict[str, Any]:
        """Generate a fallback creative prompt when Ollama fails"""
        
        # Simple theme generation based on letters
        themes = {
            ('M', 'K'): "mask", ('S', 'T'): "storm", ('F', 'L'): "flame",
            ('W', 'A'): "water", ('C', 'R'): "crystal", ('B', 'E'): "beast"
        }
        
        theme_key = tuple(sorted([letter1, letter2]))
        theme = themes.get(theme_key, f"mysterious {letter1.lower()}{letter2.lower()} artifact")
        
        prompt = f"professional {style} photograph of {theme}, {mood} {lighting}, "
        prompt += "volumetric lighting, ray tracing, subsurface scattering, highly detailed, "
        prompt += "Canon EOS R5, 85mm f/1.4, masterpiece, award winning photography"
        
        return {
            "theme": theme,
            "composition": f"A {mood} composition featuring {theme} with {lighting} lighting",
            "prompt": prompt,
            "negative_prompt": "blurry, low quality, distorted, amateur, oversaturated",
            "reference_searches": [f"{theme} professional photography", f"{style} {theme} art"],
            "suggested_loras": [],
            "technical_tags": ["volumetric lighting", "ray tracing", "subsurface scattering", "high detail"],
            "camera_settings": ["Canon EOS R5", "85mm f/1.4"]
        }
    
    def _get_model_specific_enhancement_prompt(self, model_name: str) -> str:
        """Get model-specific enhancement system prompt"""
        model_lower = model_name.lower()

        # Flux models
        if any(flux_name in model_lower for flux_name in ['flux', 'flux1', 'flux-dev', 'flux-schnell']):
            return """You are an expert at enhancing prompts for FLUX models. FLUX excels at:
- Natural language understanding and complex scene composition
- Photorealistic imagery with excellent detail
- Text rendering and typography integration
- Complex lighting and atmospheric effects
- Multiple subjects and detailed backgrounds

Enhancement Strategy for FLUX:
1. Use natural, descriptive language rather than comma-separated tags
2. Include specific lighting conditions (golden hour, studio lighting, etc.)
3. Add composition details (close-up, wide shot, bird's eye view)
4. Specify camera settings when relevant (shallow depth of field, etc.)
5. Include atmospheric elements (fog, rain, sunbeams)
6. Add material and texture descriptions
7. Specify art styles naturally (in the style of, reminiscent of)

Transform the basic prompt into a rich, natural language description that FLUX can interpret effectively.
Return only the enhanced prompt, no explanations."""

        # SDXL models
        elif any(sdxl_name in model_lower for sdxl_name in ['sdxl', 'xl', 'juggernaut', 'realvisxl']):
            return """You are an expert at enhancing prompts for SDXL models. SDXL works best with:
- Structured, detailed prompts with clear subject definition
- Technical photography terms and camera specifications
- Artistic style references and medium specifications
- Quality boosters and negative prompt considerations
- Aspect ratio and composition awareness

Enhancement Strategy for SDXL:
1. Start with clear subject definition
2. Add technical quality terms (masterpiece, best quality, highly detailed)
3. Include specific art styles or mediums (oil painting, digital art, photography)
4. Add camera and lens specifications (85mm lens, f/1.4, bokeh)
5. Specify lighting setup (studio lighting, rim lighting, dramatic shadows)
6. Include composition terms (rule of thirds, centered, dynamic pose)
7. Add texture and material details (silk fabric, weathered wood, polished metal)

Create a structured, detailed prompt optimized for SDXL's capabilities.
Return only the enhanced prompt, no explanations."""

        # SD 1.5 models
        elif any(sd15_name in model_lower for sd15_name in ['sd15', 'v1-5', 'stable-diffusion-v1']):
            return """You are an expert at enhancing prompts for Stable Diffusion 1.5 models. SD 1.5 responds well to:
- Comma-separated tag structure
- Specific artist and style references
- Quality and aesthetic boosters
- Clear subject and action descriptions
- Weighted emphasis using parentheses

Enhancement Strategy for SD 1.5:
1. Structure as comma-separated tags
2. Start with subject and main action
3. Add quality boosters (masterpiece, best quality, ultra detailed)
4. Include specific artist references (by artist name, art style)
5. Add technical terms (8k, uhd, professional photography)
6. Specify medium (digital art, oil painting, watercolor)
7. Include lighting and mood descriptors
8. Add composition and framing terms

Transform into a well-structured tag-based prompt optimized for SD 1.5.
Return only the enhanced prompt, no explanations."""

        # Default/Generic enhancement
        else:
            return """You are an expert at enhancing AI image generation prompts.
Take the user's basic prompt and enhance it with professional photography and artistic techniques.
Add appropriate technical terms, lighting descriptions, and composition elements.
Keep the core concept but make it more detailed and professional.
Adapt the enhancement style based on what would work best for modern AI image generation.
Return only the enhanced prompt, no explanations."""

    async def enhance_prompt(self, base_prompt: str, model_name: str = "flux", style_preferences: List[str] = None) -> str:
        """Enhance a basic prompt with model-specific techniques"""

        system_prompt = self._get_model_specific_enhancement_prompt(model_name)

        style_context = ""
        if style_preferences:
            style_context = f"Style preferences: {', '.join(style_preferences)}. "

        user_prompt = f"{style_context}Enhance this prompt: {base_prompt}"

        try:
            enhanced = await self.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                model="llama3.2:latest",  # Use llama3.2:latest specifically
                temperature=0.7,
                max_tokens=600
            )
            return enhanced.strip()
        except Exception as e:
            logger.error(f"Error enhancing prompt: {e}")
            return base_prompt  # Return original if enhancement fails
