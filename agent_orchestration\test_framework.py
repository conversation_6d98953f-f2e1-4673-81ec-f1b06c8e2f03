#!/usr/bin/env python3
"""
Test script for the Universal Cross-Platform Agent Framework
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the framework to path
sys.path.insert(0, str(Path(__file__).parent))

from Cross_Platform_Agent_Framework import (
    UniversalAgentOrchestrator,
    EnhancedDocumentationOverseer
)

async def test_framework():
    """Test the universal agent framework deployment and functionality."""
    print("[START] Testing Universal Cross-Platform Agent Framework")
    print("=" * 60)
    
    orchestrator = UniversalAgentOrchestrator()
    
    try:
        # Deploy Enhanced Documentation Overseer
        print("[DEPLOY] Deploying Enhanced Documentation Overseer...")
        context = {"project_root": str(Path.cwd().parent)}
        success = await orchestrator.deploy_agent(
            EnhancedDocumentationOverseer,
            "enhanced-documentation-overseer",
            [
                "build_knowledge_base",
                "generate_context_files", 
                "llm_context_delivery",
                "intelligent_file_organization",
                "audit_documentation",
                "semantic_organization",
                "knowledge_synthesis",
                "maintain_knowledge_graph",
                "content_summarization",
                "topic_clustering",
                "context_extraction"
            ],
            context
        )
        
        if success:
            print("[OK] Enhanced Documentation Overseer deployed successfully")
        else:
            print("[FAILED] Failed to deploy Enhanced Documentation Overseer")
            return
        
        # Wait a moment for registration
        await asyncio.sleep(1)
        
        # List available agents
        print("\n[LIST] Listing available agents...")
        agents = await orchestrator.list_agents()
        for agent in agents:
            print(f"  - {agent['name']} ({agent['status']}) - {', '.join(agent['capabilities'][:3])}...")
        
        # Test agent call - documentation audit
        print("\n[TEST] Testing documentation audit...")
        result = await orchestrator.call_agent(
            "enhanced-documentation-overseer",
            "execute",
            {
                "task": "audit_documentation",
                "parameters": {}
            }
        )
        
        if result.get("status") == "success":
            audit_data = result.get("data", {})
            audit_results = audit_data.get("audit_results", {})
            print(f"[OK] Documentation audit completed:")
            print(f"   - Total files: {audit_results.get('total_files', 0)}")
            print(f"   - Documented files: {audit_results.get('documented_files', 0)}")
            print(f"   - Quality score: {audit_data.get('quality_score', 0):.2f}")
        else:
            print(f"[FAILED] Documentation audit failed: {result.get('error', 'Unknown error')}")
        
        # Test knowledge base building
        print("\n[TEST] Testing knowledge base building...")
        result = await orchestrator.call_agent(
            "enhanced-documentation-overseer",
            "execute",
            {
                "task": "build_knowledge_base",
                "parameters": {
                    "generate_embeddings": False,  # Skip embeddings for speed
                    "cluster_topics": True
                }
            }
        )
        
        if result.get("status") == "success":
            kb_data = result.get("data", {})
            kb_stats = kb_data.get("knowledge_base_stats", {})
            print(f"[OK] Knowledge base built successfully:")
            print(f"   - Total entries: {kb_stats.get('total_entries', 0)}")
            print(f"   - Unique topics: {kb_stats.get('unique_topics', 0)}")
            print(f"   - Files processed: {kb_stats.get('files_processed', 0)}")
        else:
            print(f"[FAILED] Knowledge base building failed: {result.get('error', 'Unknown error')}")
        
        # Test context extraction
        print("\n[TEST] Testing context extraction...")
        result = await orchestrator.call_agent(
            "enhanced-documentation-overseer",
            "execute",
            {
                "task": "context_extraction",
                "parameters": {
                    "query": "ComfyUI workflow generation",
                    "max_size": 2000
                }
            }
        )
        
        if result.get("status") == "success":
            context_data = result.get("data", {})
            metadata = context_data.get("context_metadata", {})
            print(f"[OK] Context extraction completed:")
            print(f"   - Entries found: {metadata.get('entries_found', 0)}")
            print(f"   - Context size: {metadata.get('total_size', 0)} characters")
        else:
            print(f"[FAILED] Context extraction failed: {result.get('error', 'Unknown error')}")
        
        print("\n[SUCCESS] Framework testing completed successfully!")
        print("\n[PLATFORMS] Supported Platforms:")
        platforms = [
            "Claude Code", "VS Code Augment", "Windsurf", "GitHub Copilot",
            "Cursor", "Tabnine", "Qodo", "Gemini CLI", "Amazon Q", 
            "Cline (Continue)", "Roo Code"
        ]
        for platform in platforms:
            print(f"   [OK] {platform}")
        
    except Exception as e:
        print(f"[ERROR] Framework test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Keep running for a few seconds to test background services
        print(f"\n[WAIT] Testing background services for 5 seconds...")
        await asyncio.sleep(5)
        
        print("\n[STOP] Shutting down...")
        orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(test_framework())