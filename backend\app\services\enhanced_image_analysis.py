import requests
import base64
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any
from PIL import Image, ImageStat
import hashlib
import os
import colorsys
import numpy as np

from ..core.config import settings

class EnhancedImageAnalyzer:
    def __init__(self, model: str = "llava:latest"):
        self.model = model
        self.ollama_url = settings.OLLAMA_API_URL
        self.db_path = Path("data/agents/image_expert.db")
        self._ensure_database()
    
    def _ensure_database(self):
        """Initialize database if it doesn't exist"""
        if not self.db_path.exists():
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            schema_path = Path("data/agents/image_expert_schema.sql")
            if schema_path.exists():
                with open(schema_path, 'r') as f:
                    schema = f.read()
                with sqlite3.connect(self.db_path) as conn:
                    conn.executescript(schema)
    
    def _image_to_base64(self, image_path: str) -> str:
        """Convert image to base64 string"""
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    
    def _call_ollama(self, prompt: str, image_b64: str) -> Optional[str]:
        """Make API call to Ollama"""
        try:
            response = requests.post(f"{self.ollama_url}/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "images": [image_b64],
                "stream": False
            }, timeout=120)
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                print(f"Ollama API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return None
    
    def analyze_advanced_composition(self, image_path: str) -> Dict[str, Any]:
        """Deep composition analysis with advanced techniques"""
        prompt = """Perform an advanced composition analysis:

**STRUCTURAL COMPOSITION:**
1. RULE OF THIRDS: How well placed are key elements? Rate adherence (1-10)
2. GOLDEN RATIO: Any golden spiral or ratio usage? Specific examples
3. LEADING LINES: Identify all leading lines and their effectiveness
4. SYMMETRY/ASYMMETRY: Type and quality of balance used
5. GEOMETRIC PATTERNS: Triangles, circles, diagonals in composition

**VISUAL FLOW:**
6. EYE MOVEMENT: Trace how the eye moves through the image
7. VISUAL WEIGHT: How is visual weight distributed?
8. FOCAL HIERARCHY: Primary, secondary, tertiary focal points
9. NEGATIVE SPACE: How effectively is negative space used?
10. DEPTH LAYERS: Foreground, midground, background relationships

**ADVANCED TECHNIQUES:**
11. FRAMING: Natural or architectural framing elements
12. PATTERNS & REPETITION: Repetitive elements and their impact
13. SCALE RELATIONSHIPS: Size relationships creating impact
14. TENSION POINTS: Areas of visual tension or conflict
15. COMPOSITION SCORE: Overall composition mastery (1-10)

Provide specific examples and explain why each technique works or doesn't work."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "advanced_composition",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_comprehensive_color(self, image_path: str) -> Dict[str, Any]:
        """Comprehensive color theory analysis"""
        prompt = """Perform comprehensive color analysis:

**COLOR HARMONY & THEORY:**
1. COLOR SCHEME: Identify the color harmony (monochromatic, analogous, complementary, triadic, split-complementary, tetradic, etc.)
2. DOMINANT COLORS: List 5-7 most prominent colors with specific names (not just "blue" but "cerulean blue", "navy blue", etc.)
3. COLOR TEMPERATURE: Overall temperature and temperature contrast areas
4. SATURATION MAPPING: Where are saturated vs desaturated areas?
5. VALUE DISTRIBUTION: Light, medium, dark value distribution

**COLOR PSYCHOLOGY & MOOD:**
6. EMOTIONAL IMPACT: What emotions do these colors evoke?
7. CULTURAL ASSOCIATIONS: Any cultural color meanings present?
8. COLOR SYMBOLISM: Symbolic meanings of color choices
9. MOOD CREATION: How do colors create the overall mood?
10. SEASONAL ASSOCIATIONS: Any seasonal color connections?

**TECHNICAL COLOR ANALYSIS:**
11. COLOR CONTRAST: Areas of high/low color contrast
12. COLOR TRANSITIONS: How colors blend or transition
13. COLOR BALANCE: Is there proper color balance throughout?
14. ACCENT COLORS: How are accent colors used for emphasis?
15. COLOR EFFECTIVENESS: Rate overall color usage (1-10)

Provide specific color names and explain relationships between colors."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "comprehensive_color",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_professional_lighting(self, image_path: str) -> Dict[str, Any]:
        """Professional lighting analysis"""
        prompt = """Analyze lighting with professional depth:

**LIGHT SOURCES & DIRECTION:**
1. PRIMARY LIGHT: Direction, intensity, quality (hard/soft)
2. SECONDARY LIGHTS: Fill lights, rim lights, accent lights
3. LIGHT TEMPERATURE: Color temperature of different light sources
4. LIGHT RATIOS: Relationship between key, fill, and rim lighting
5. LIGHT MODIFIERS: Evidence of diffusers, reflectors, flags, etc.

**SHADOW & HIGHLIGHT ANALYSIS:**
6. SHADOW QUALITY: Hard/soft shadows and their placement
7. SHADOW DENSITY: How dark are the shadows? Detail retention?
8. HIGHLIGHT ROLLOFF: How do highlights transition to midtones?
9. SPECULAR HIGHLIGHTS: Presence and quality of specular reflections
10. CAST SHADOWS: How cast shadows affect composition

**MOOD & ATMOSPHERE:**
11. DRAMATIC IMPACT: How lighting creates drama or mood
12. TIME OF DAY: What time does the lighting suggest?
13. WEATHER CONDITIONS: How lighting suggests weather/atmosphere
14. LIGHTING STYLE: Portrait, landscape, architectural, product lighting style
15. LIGHTING MASTERY: Rate lighting technique skill (1-10)

Explain specific lighting techniques and their effectiveness."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "professional_lighting",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_tone_atmosphere(self, image_path: str) -> Dict[str, Any]:
        """Advanced tone and atmosphere analysis"""
        prompt = """Analyze tone and atmosphere in detail:

**TONAL RELATIONSHIPS:**
1. TONAL RANGE: Full range from darkest shadow to brightest highlight?
2. MIDTONE QUALITY: How are midtones handled and distributed?
3. CONTRAST LEVELS: Local and global contrast analysis
4. TONAL TRANSITIONS: Smooth or abrupt tonal changes?
5. EXPOSURE BIAS: Tendency toward high-key or low-key?

**ATMOSPHERIC QUALITIES:**
6. DEPTH OF FIELD: How does focus affect atmosphere?
7. ATMOSPHERIC PERSPECTIVE: Haze, fog, distance effects
8. ENVIRONMENTAL MOOD: What environment/setting mood is created?
9. SEASONAL FEELING: What season or time does this evoke?
10. WEATHER IMPRESSION: What weather conditions are suggested?

**EMOTIONAL TONE:**
11. EMOTIONAL REGISTER: Happy, sad, mysterious, energetic, calm, etc.
12. PSYCHOLOGICAL IMPACT: What psychological state does this create?
13. NARRATIVE TONE: Story-telling mood (romantic, thriller, peaceful, etc.)
14. VIEWER RESPONSE: How should viewers emotionally respond?
15. TONE EFFECTIVENESS: How successfully is the intended tone achieved? (1-10)

Explain how visual elements combine to create the overall tone and atmosphere."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "tone_atmosphere",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def analyze_artistic_style_deep(self, image_path: str) -> Dict[str, Any]:
        """Deep artistic style and technique analysis"""
        prompt = """Perform deep artistic style analysis:

**STYLE IDENTIFICATION:**
1. PRIMARY MOVEMENT: Main art movement/style (Renaissance, Impressionism, Digital Art, etc.)
2. SUBSTYLE: Specific substyle within the movement
3. HISTORICAL PERIOD: Time period influence and characteristics
4. REGIONAL INFLUENCE: Geographic/cultural style influences
5. CONTEMPORARY ELEMENTS: Modern interpretations or updates

**TECHNIQUE ANALYSIS:**
6. BRUSHWORK/MARK MAKING: Visible technique characteristics
7. TEXTURE TECHNIQUES: How textures are created and rendered
8. BLENDING METHODS: How colors and tones are blended
9. DETAIL LEVEL: Approach to detail (hyperrealistic, stylized, minimal)
10. MEDIUM CHARACTERISTICS: What medium does this emulate?

**ARTISTIC INFLUENCES:**
11. MASTER REFERENCES: Reminiscent of specific famous artists?
12. CROSS-POLLINATION: Influences from other art forms/media
13. INNOVATION ELEMENTS: What's unique or innovative?
14. TECHNICAL SKILL: Rate the technical execution (1-10)
15. ARTISTIC MERIT: Overall artistic achievement (1-10)

Provide specific examples and comparisons to known works or artists."""
        
        image_b64 = self._image_to_base64(image_path)
        response = self._call_ollama(prompt, image_b64)
        
        return {
            "analysis_type": "artistic_style_deep",
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    def master_analysis(self, image_path: str) -> Dict[str, Any]:
        """Comprehensive master-level analysis combining all aspects"""
        print(f"Performing master-level analysis on: {image_path}")
        
        # Perform all focused analyses
        composition = self.analyze_advanced_composition(image_path)
        color = self.analyze_comprehensive_color(image_path)
        lighting = self.analyze_professional_lighting(image_path)
        tone = self.analyze_tone_atmosphere(image_path)
        style = self.analyze_artistic_style_deep(image_path)
        
        # Get image metadata
        metadata = self._get_image_metadata(image_path)
        
        # Combine all analyses
        result = {
            "image_path": image_path,
            "image_filename": Path(image_path).name,
            "metadata": metadata,
            "analyses": {
                "advanced_composition": composition,
                "comprehensive_color": color,
                "professional_lighting": lighting,
                "tone_atmosphere": tone,
                "artistic_style_deep": style
            },
            "analyzed_at": datetime.now().isoformat(),
            "analyzer_model": self.model,
            "analysis_type": "master_comprehensive"
        }
        
        # Store in database
        self._store_analysis(result)
        
        return result
    
    def _get_image_metadata(self, image_path: str) -> Dict[str, Any]:
        """Extract basic image metadata"""
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "aspect_ratio": round(img.width / img.height, 3),
                    "format": img.format,
                    "mode": img.mode,
                    "file_size": os.path.getsize(image_path)
                }
        except Exception as e:
            return {"error": str(e)}
    
    def _store_analysis(self, analysis_data: Dict[str, Any]):
        """Store analysis results in SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                meta = analysis_data.get("metadata", {})
                
                cursor.execute("""
                    INSERT INTO image_analysis (
                        image_path, image_filename, file_size_bytes,
                        dimensions_width, dimensions_height, aspect_ratio, file_format,
                        style_analysis_details, overall_quality_score,
                        analyzed_at, analyzer_version, tags, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    analysis_data["image_path"],
                    analysis_data["image_filename"],
                    meta.get("file_size", 0),
                    meta.get("width", 0),
                    meta.get("height", 0),
                    meta.get("aspect_ratio", 0.0),
                    meta.get("format", "unknown"),
                    json.dumps(analysis_data["analyses"]),
                    9.0,  # Master analysis quality score
                    analysis_data["analyzed_at"],
                    f"{analysis_data['analyzer_model']}_enhanced",
                    json.dumps(["master_analysis", "composition", "color", "lighting", "tone"]),
                    "Master-level comprehensive analysis"
                ))
                
                conn.commit()
                print(f"Master analysis stored for {analysis_data['image_filename']}")
                
        except Exception as e:
            print(f"Error storing analysis: {e}")

# Quick analysis functions
def analyze_composition_focus(image_path: str) -> Dict[str, Any]:
    """Focus on composition only"""
    analyzer = EnhancedImageAnalyzer()
    return analyzer.analyze_advanced_composition(image_path)

def analyze_color_focus(image_path: str) -> Dict[str, Any]:
    """Focus on color only"""
    analyzer = EnhancedImageAnalyzer()
    return analyzer.analyze_comprehensive_color(image_path)

def analyze_lighting_focus(image_path: str) -> Dict[str, Any]:
    """Focus on lighting only"""
    analyzer = EnhancedImageAnalyzer()
    return analyzer.analyze_professional_lighting(image_path)

def analyze_tone_focus(image_path: str) -> Dict[str, Any]:
    """Focus on tone and atmosphere only"""
    analyzer = EnhancedImageAnalyzer()
    return analyzer.analyze_tone_atmosphere(image_path)

def master_image_analysis(image_path: str) -> Dict[str, Any]:
    """Complete master-level analysis"""
    analyzer = EnhancedImageAnalyzer()
    return analyzer.master_analysis(image_path)