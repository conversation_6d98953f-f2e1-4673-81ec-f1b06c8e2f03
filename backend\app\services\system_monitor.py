import psutil
import asyncio
import logging
from typing import Dict, Any, Optional
import time

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    logging.warning("GPUtil not available - GPU monitoring disabled")

from app.core.config import settings

logger = logging.getLogger(__name__)

class SystemMonitor:
    def __init__(self):
        self.monitoring = False
        self.stats_cache = {}
        self.last_update = 0
        
    async def start_monitoring(self):
        """Start system monitoring loop"""
        self.monitoring = True
        asyncio.create_task(self._monitoring_loop())
        logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        logger.info("System monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                self.stats_cache = await self._collect_stats()
                self.last_update = time.time()
                await asyncio.sleep(settings.SYSTEM_STATS_UPDATE_INTERVAL)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Wait longer on error
    
    async def get_current_stats(self) -> Dict[str, Any]:
        """Get current system statistics"""
        if not self.stats_cache or (time.time() - self.last_update) > 10:
            # Refresh if cache is empty or stale
            self.stats_cache = await self._collect_stats()
            self.last_update = time.time()
        
        return self.stats_cache
    
    async def _collect_stats(self) -> Dict[str, Any]:
        """Collect system statistics"""
        stats = {
            "timestamp": time.time(),
            "cpu": await self._get_cpu_stats(),
            "memory": await self._get_memory_stats(),
            "gpu": await self._get_gpu_stats(),
            "disk": await self._get_disk_stats(),
            "network": await self._get_network_stats()
        }
        
        return stats
    
    async def _get_cpu_stats(self) -> Dict[str, Any]:
        """Get CPU statistics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # Get per-core usage
            per_cpu = psutil.cpu_percent(interval=0.1, percpu=True)
            
            # Get CPU temperature if available
            temperature = None
            try:
                temps = psutil.sensors_temperatures()
                if 'coretemp' in temps:
                    # Intel CPU
                    core_temps = [temp.current for temp in temps['coretemp'] if 'Core' in temp.label]
                    if core_temps:
                        temperature = sum(core_temps) / len(core_temps)
                elif 'k10temp' in temps:
                    # AMD CPU
                    temperature = temps['k10temp'][0].current
            except:
                pass
            
            return {
                "usage_percent": cpu_percent,
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "per_core_usage": per_cpu,
                "temperature_celsius": temperature
            }
        except Exception as e:
            logger.error(f"Error getting CPU stats: {e}")
            return {"usage_percent": 0, "count": 0}
    
    async def _get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics"""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            return {
                "total_gb": round(memory.total / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "usage_percent": memory.percent,
                "swap_total_gb": round(swap.total / (1024**3), 2),
                "swap_used_gb": round(swap.used / (1024**3), 2),
                "swap_percent": swap.percent
            }
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {"total_gb": 0, "used_gb": 0, "usage_percent": 0}
    
    async def _get_gpu_stats(self) -> Dict[str, Any]:
        """Get GPU statistics"""
        if not GPU_AVAILABLE:
            return {"available": False, "message": "GPU monitoring not available"}
        
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                return {"available": False, "message": "No GPUs found"}
            
            # Get primary GPU (usually index 0)
            gpu = gpus[0]
            
            return {
                "available": True,
                "name": gpu.name,
                "driver_version": gpu.driver,
                "memory_total_gb": round(gpu.memoryTotal / 1024, 2),
                "memory_used_gb": round(gpu.memoryUsed / 1024, 2),
                "memory_free_gb": round(gpu.memoryFree / 1024, 2),
                "memory_usage_percent": round((gpu.memoryUsed / gpu.memoryTotal) * 100, 1),
                "gpu_usage_percent": round(gpu.load * 100, 1),
                "temperature_celsius": gpu.temperature,
                "uuid": gpu.uuid
            }
        except Exception as e:
            logger.error(f"Error getting GPU stats: {e}")
            return {"available": False, "error": str(e)}
    
    async def _get_disk_stats(self) -> Dict[str, Any]:
        """Get disk statistics for relevant drives"""
        try:
            drives = {}
            
            # Monitor specific drives mentioned in config
            drive_letters = ['C:', 'L:', 'G:']
            
            for drive in drive_letters:
                try:
                    usage = psutil.disk_usage(drive + '\\')
                    drives[drive] = {
                        "total_gb": round(usage.total / (1024**3), 2),
                        "used_gb": round(usage.used / (1024**3), 2),
                        "free_gb": round(usage.free / (1024**3), 2),
                        "usage_percent": round((usage.used / usage.total) * 100, 1)
                    }
                except Exception as e:
                    logger.debug(f"Could not get stats for drive {drive}: {e}")
            
            return drives
        except Exception as e:
            logger.error(f"Error getting disk stats: {e}")
            return {}
    
    async def _get_network_stats(self) -> Dict[str, Any]:
        """Get network statistics"""
        try:
            net_io = psutil.net_io_counters()
            
            return {
                "bytes_sent": net_io.bytes_sent,
                "bytes_received": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_received": net_io.packets_recv
            }
        except Exception as e:
            logger.error(f"Error getting network stats: {e}")
            return {}
    
    def check_system_health(self) -> Dict[str, Any]:
        """Check system health and return warnings"""
        warnings = []
        critical = []
        
        if not self.stats_cache:
            return {"warnings": ["System monitoring not initialized"], "critical": []}
        
        try:
            # Check GPU temperature
            gpu_stats = self.stats_cache.get("gpu", {})
            if gpu_stats.get("available") and gpu_stats.get("temperature_celsius"):
                temp = gpu_stats["temperature_celsius"]
                if temp > settings.GPU_TEMPERATURE_WARNING:
                    warnings.append(f"GPU temperature high: {temp}°C")
                if temp > 85:
                    critical.append(f"GPU temperature critical: {temp}°C")
            
            # Check VRAM usage
            if gpu_stats.get("available") and gpu_stats.get("memory_usage_percent"):
                vram_usage = gpu_stats["memory_usage_percent"] / 100
                if vram_usage > settings.VRAM_WARNING_THRESHOLD:
                    warnings.append(f"VRAM usage high: {gpu_stats['memory_usage_percent']}%")
                if vram_usage > 0.95:
                    critical.append(f"VRAM usage critical: {gpu_stats['memory_usage_percent']}%")
            
            # Check CPU usage
            cpu_stats = self.stats_cache.get("cpu", {})
            if cpu_stats.get("usage_percent", 0) > 90:
                warnings.append(f"CPU usage high: {cpu_stats['usage_percent']}%")
            
            # Check memory usage
            memory_stats = self.stats_cache.get("memory", {})
            if memory_stats.get("usage_percent", 0) > 90:
                warnings.append(f"Memory usage high: {memory_stats['usage_percent']}%")
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            warnings.append("Error checking system health")
        
        return {
            "warnings": warnings,
            "critical": critical,
            "status": "critical" if critical else "warning" if warnings else "healthy"
        }
