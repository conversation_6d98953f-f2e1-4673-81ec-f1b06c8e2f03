{"permissions": {"allow": ["mcp__desktop-commander__read_file", "mcp__desktop-commander__search_files", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__start_process", "mcp__desktop-commander__read_process_output", "mcp__desktop-commander__force_terminate", "mcp__desktop-commander__list_sessions", "mcp__filesystem__directory_tree", "mcp__filesystem__list_directory", "mcp__filesystem__read_multiple_files", "mcp__filesystem__read_text_file", "Bash(npm run type-check:*)", "mcp__desktop-commander__write_file", "mcp__desktop-commander__create_directory", "mcp__sequential-thinking__sequentialthinking", "mcp__filesystem__edit_file", "mcp__filesystem__create_directory", "<PERSON><PERSON>(python:*)", "Bash(\"G:\\PORT_COMFY_front\\comfycustomenv\\Scripts\\pip.exe\" install PyYAML)", "Bash(\"G:\\PORT_COMFY_front\\comfycustomenv\\Scripts\\python.exe\" main.py --listen --port 8188)", "Bash(\"G:\\PORT_COMFY_front\\comfycustomenv\\Scripts\\pip.exe\" install typing_extensions)", "<PERSON><PERSON>(timeout:*)", "Bash(\"G:\\PORT_COMFY_front\\comfycustomenv\\Scripts\\pip.exe\" install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121)", "Bash(\"G:\\PORT_COMFY_front\\comfycustomenv\\Scripts\\pip.exe\" install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu121)", "mcp__desktop-commander__interact_with_process", "mcp__memory__create_entities", "<PERSON><PERSON>(dir:*)", "Bash(npm audit:*)", "Bash(npm ls:*)", "Bash(npm why:*)", "Bash(./python.exe:*)", "Bash(.venvScriptsActivate.ps1)", "Bash(.venvScriptsactivate.bat)", "Bash(\".\\venv\\Scripts\\python.exe\" -m pip install --upgrade pip)", "Bash(\".\\venv\\Scripts\\pip.exe\" install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(npm test:*)", "mcp__desktop-commander__move_file", "mcp__filesystem__write_file", "mcp__filesystem__search_files", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(.examplesdaily_health_check.bat)", "Bash(cmd /c:*)", "mcp__desktop-commander__get_config", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__get_file_info", "mcp__memory__search_nodes", "mcp__desktop-commander__read_multiple_files", "<PERSON><PERSON>(start:*)", "mcp__desktop-commander__list_processes", "mcp__memory__create_relations", "mcp__desktop-commander__search_code", "Bash(.venvScriptspython.exe -c \"import sys, torch; print(f''Python: {sys.version}''); print(f''PyTorch: {torch.__version__}''); print(f''CUDA Available: {torch.cuda.is_available()}''); print(f''CUDA Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"\"No CUDA\"\"}'')\")", "Bash(\".\\venv\\Scripts\\python.exe\" -c \"import sys, torch; print(f''Python: {sys.version}''); print(f''PyTorch: {torch.__version__}''); print(f''CUDA Available: {torch.cuda.is_available()}''); print(f''CUDA Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"\"No CUDA\"\"}'')\")", "Bash(\".\\venv\\Scripts\\pip.exe\" check)", "Bash(\".\\venv\\Scripts\\pip.exe\" list:*)", "Bash(\"backvenv/Scripts/pip.exe\" list)", "Bash(\"backvenv/Scripts/pip.exe\" install -r requirements.txt)", "Bash(\"G:\\ZComfyUI\\ComfyPort\\run_nvidia_gpu.bat\")", "Bash(\"G:\\ZComfyUI\\comfyui-custom-frontend\\backvenv\\Scripts\\python.exe\" main.py)", "Bash(\"G:\\ZComfyUI\\ComfyPort\\python_embeded\\python.exe\" -s \"G:\\ZComfyUI\\ComfyPort\\ComfyUI\\main.py\" --windows-standalone-build --listen --port 8188)", "Bash(\"..\\backvenv\\Scripts\\python.exe\" main.py)", "Bash(\"G:\\ZComfyUI\\comfyui-custom-frontend\\backend\\backvenv\\Scripts\\python.exe\" \"G:\\ZComfyUI\\comfyui-custom-frontend\\backend\\main.py\")", "Bash(npm run dev:*)", "WebSearch", "Bash(pip install:*)", "Bash(move Processed*.png .)", "Bash(for f in *.png)", "Bash(do if [ -f \"$f\" ])", "Bash(then mv \"$f\" \"../$f\")", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(powershell:*)", "Bash(rm:*)", "<PERSON><PERSON>(del:*)"], "additionalDirectories": ["G:\\PORT_COMFY_front", "G:\\comfyui_front"]}, "mcpServers": {"context7": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-context7"]}, "huggingface": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-huggingface"], "env": {"HUGGINGFACE_API_TOKEN": "*************************************"}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"]}}}