"""
SQLite-based Workflow Data Service
Handles persistent storage of workflow execution data for troubleshooting and optimization
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_, or_, text
from sqlalchemy.orm import selectinload

from app.core.database import AsyncSessionLocal
from app.models.workflow_execution import (
    WorkflowExecution, WorkflowCorrection, WorkflowWarning, WorkflowError,
    ModelUsageStats, SystemStats
)

logger = logging.getLogger(__name__)


class SQLiteWorkflowDataService:
    """SQLite-based workflow data persistence service"""
    
    def __init__(self):
        self.session_factory = AsyncSessionLocal
    
    async def start_tracking(
        self,
        execution_id: str,
        original_request: Dict[str, Any],
        generation_settings: Dict[str, Any],
        original_workflow: Dict[str, Any],
        validation_result: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> str:
        """Start tracking a new workflow execution"""
        
        async with self.session_factory() as session:
            try:
                # Create main execution record
                execution = WorkflowExecution(
                    id=execution_id,
                    timestamp=datetime.utcnow(),
                    user_id=user_id,
                    model_name=original_request.get('model', 'unknown'),
                    model_type=original_request.get('modelType', 'unknown'),
                    generation_mode=original_request.get('mode', 'txt2img'),
                    generation_settings=generation_settings,
                    original_request=original_request,
                    corrected_request=validation_result.get('correctedRequest'),
                    original_workflow=json.dumps(original_workflow),
                    final_workflow=json.dumps(validation_result.get('correctedWorkflow', original_workflow)),
                    node_count=len(original_workflow),
                    auto_corrections_applied=len(validation_result.get('corrections', [])),
                    validation_warnings=len(validation_result.get('warnings', [])),
                    validation_errors=len(validation_result.get('errors', [])),
                    execution_status='submitted',
                    frontend_version='1.0.0',
                    platform=generation_settings.get('platform', 'unknown'),
                    user_agent=generation_settings.get('userAgent', 'unknown'),
                    tags=','.join(self._generate_tags(original_request, validation_result))
                )
                
                session.add(execution)
                
                # Add corrections
                for correction in validation_result.get('corrections', []):
                    correction_record = WorkflowCorrection(
                        execution_id=execution_id,
                        node_id=correction.get('nodeId', 'unknown'),
                        node_class=correction.get('nodeClass', 'unknown'),
                        parameter=correction.get('parameter', 'unknown'),
                        original_value=str(correction.get('originalValue', '')),
                        corrected_value=str(correction.get('correctedValue', '')),
                        reason=correction.get('reason', ''),
                        severity=correction.get('severity', 'minor')
                    )
                    session.add(correction_record)
                
                # Add warnings
                for warning in validation_result.get('warnings', []):
                    warning_record = WorkflowWarning(
                        execution_id=execution_id,
                        node_id=warning.get('nodeId', 'unknown'),
                        node_class=warning.get('nodeClass', 'unknown'),
                        parameter=warning.get('parameter'),
                        message=warning.get('message', ''),
                        suggestion=warning.get('suggestion')
                    )
                    session.add(warning_record)
                
                # Add errors
                for error in validation_result.get('errors', []):
                    error_record = WorkflowError(
                        execution_id=execution_id,
                        node_id=error.get('nodeId', 'unknown'),
                        node_class=error.get('nodeClass', 'unknown'),
                        parameter=error.get('parameter'),
                        message=error.get('message', ''),
                        fatal=error.get('fatal', False)
                    )
                    session.add(error_record)
                
                await session.commit()
                logger.info(f"Started tracking workflow execution: {execution_id}")
                return execution_id
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to start tracking workflow {execution_id}: {e}")
                raise
    
    async def update_execution_status(
        self,
        execution_id: str,
        status: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update execution status and additional data"""
        
        async with self.session_factory() as session:
            try:
                # Get execution record
                result = await session.execute(
                    select(WorkflowExecution).where(WorkflowExecution.id == execution_id)
                )
                execution = result.scalar_one_or_none()
                
                if not execution:
                    logger.warning(f"Execution {execution_id} not found for status update")
                    return False
                
                # Update status
                execution.execution_status = status
                
                if status == 'processing' and not execution.execution_start_time:
                    execution.execution_start_time = datetime.utcnow()
                
                if status in ['completed', 'failed', 'cancelled']:
                    execution.execution_end_time = datetime.utcnow()
                    if execution.execution_start_time:
                        duration = execution.execution_end_time - execution.execution_start_time
                        execution.execution_duration = duration.total_seconds() * 1000  # milliseconds
                
                # Apply additional data
                if additional_data:
                    for key, value in additional_data.items():
                        if hasattr(execution, key):
                            setattr(execution, key, value)
                
                await session.commit()
                logger.info(f"Updated execution {execution_id} status to {status}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to update execution status for {execution_id}: {e}")
                raise
    
    async def complete_tracking(
        self,
        execution_id: str,
        result_data: Dict[str, Any]
    ) -> bool:
        """Complete tracking with final results"""
        
        async with self.session_factory() as session:
            try:
                # Get execution record
                result = await session.execute(
                    select(WorkflowExecution).where(WorkflowExecution.id == execution_id)
                )
                execution = result.scalar_one_or_none()
                
                if not execution:
                    logger.warning(f"Execution {execution_id} not found for completion")
                    return False
                
                # Update final results
                execution.success = result_data.get('success', False)
                execution.error_message = result_data.get('error')
                execution.error_details = result_data.get('errorDetails')
                execution.image_generated = result_data.get('imageGenerated', False)
                execution.image_size = result_data.get('imageSize')
                execution.processing_time = result_data.get('processingTime')
                execution.memory_usage = result_data.get('memoryUsage')
                execution.gpu_utilization = result_data.get('gpuUtilization')
                
                # Set final status
                if not execution.execution_end_time:
                    execution.execution_end_time = datetime.utcnow()
                
                execution.execution_status = 'completed' if result_data.get('success') else 'failed'
                
                # Update model usage statistics
                await self._update_model_stats(session, execution)
                
                await session.commit()
                logger.info(f"Completed tracking for {execution_id}: {'SUCCESS' if result_data.get('success') else 'FAILED'}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to complete tracking for {execution_id}: {e}")
                raise
    
    async def get_execution_by_id(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get execution record by ID"""
        
        async with self.session_factory() as session:
            try:
                result = await session.execute(
                    select(WorkflowExecution)
                    .options(
                        selectinload(WorkflowExecution.corrections),
                        selectinload(WorkflowExecution.warnings),
                        selectinload(WorkflowExecution.errors)
                    )
                    .where(WorkflowExecution.id == execution_id)
                )
                execution = result.scalar_one_or_none()
                
                if not execution:
                    return None
                
                # Convert to dict with relationships
                data = execution.to_dict()
                data['corrections'] = [c.to_dict() for c in execution.corrections]
                data['warnings'] = [w.to_dict() for w in execution.warnings]
                data['errors'] = [e.to_dict() for e in execution.errors]
                
                return data
                
            except Exception as e:
                logger.error(f"Failed to get execution {execution_id}: {e}")
                raise
    
    async def get_executions(
        self,
        limit: int = 100,
        offset: int = 0,
        status: Optional[str] = None,
        model_type: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get executions with filtering and pagination"""
        
        async with self.session_factory() as session:
            try:
                # Build query
                query = select(WorkflowExecution)
                
                # Apply filters
                conditions = []
                if status:
                    conditions.append(WorkflowExecution.execution_status == status)
                if model_type:
                    conditions.append(WorkflowExecution.model_type == model_type)
                if date_from:
                    conditions.append(WorkflowExecution.timestamp >= date_from)
                if date_to:
                    conditions.append(WorkflowExecution.timestamp <= date_to)
                
                if conditions:
                    query = query.where(and_(*conditions))
                
                # Get total count
                count_query = select(func.count(WorkflowExecution.id))
                if conditions:
                    count_query = count_query.where(and_(*conditions))
                
                total_result = await session.execute(count_query)
                total = total_result.scalar()
                
                # Get paginated results
                query = query.order_by(desc(WorkflowExecution.timestamp)).limit(limit).offset(offset)
                result = await session.execute(query)
                executions = result.scalars().all()
                
                return {
                    'executions': [execution.to_dict() for execution in executions],
                    'total': total,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total
                }
                
            except Exception as e:
                logger.error(f"Failed to get executions: {e}")
                raise
    
    async def get_analytics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get workflow analytics"""
        
        async with self.session_factory() as session:
            try:
                # Date range filtering
                date_conditions = []
                if date_from:
                    date_conditions.append(WorkflowExecution.timestamp >= date_from)
                if date_to:
                    date_conditions.append(WorkflowExecution.timestamp <= date_to)
                
                base_query = select(WorkflowExecution)
                if date_conditions:
                    base_query = base_query.where(and_(*date_conditions))
                
                # Total executions
                total_result = await session.execute(
                    select(func.count(WorkflowExecution.id)).select_from(base_query.subquery())
                )
                total_executions = total_result.scalar()
                
                # Success rate
                success_result = await session.execute(
                    select(func.count(WorkflowExecution.id))
                    .select_from(base_query.subquery())
                    .where(WorkflowExecution.success == True)
                )
                successful_executions = success_result.scalar()
                success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
                
                # Average execution time
                avg_time_result = await session.execute(
                    select(func.avg(WorkflowExecution.execution_duration))
                    .select_from(base_query.subquery())
                    .where(WorkflowExecution.execution_duration.isnot(None))
                )
                avg_execution_time = avg_time_result.scalar() or 0
                
                # Common failure reasons
                failure_reasons_result = await session.execute(
                    select(WorkflowExecution.error_message, func.count(WorkflowExecution.id).label('count'))
                    .select_from(base_query.subquery())
                    .where(and_(WorkflowExecution.success == False, WorkflowExecution.error_message.isnot(None)))
                    .group_by(WorkflowExecution.error_message)
                    .order_by(desc('count'))
                    .limit(10)
                )
                failure_reasons = [
                    {'reason': row[0], 'count': row[1], 'percentage': (row[1] / total_executions * 100) if total_executions > 0 else 0}
                    for row in failure_reasons_result.fetchall()
                ]
                
                # Correction statistics
                corrections_result = await session.execute(
                    select(func.count(WorkflowCorrection.id))
                    .join(WorkflowExecution)
                    .where(and_(*date_conditions) if date_conditions else True)
                )
                total_corrections = corrections_result.scalar()
                
                # Model usage stats
                model_stats_result = await session.execute(
                    select(
                        WorkflowExecution.model_name,
                        WorkflowExecution.model_type,
                        func.count(WorkflowExecution.id).label('count'),
                        func.sum(WorkflowExecution.success.cast('integer')).label('successful'),
                        func.avg(WorkflowExecution.execution_duration).label('avg_time')
                    )
                    .select_from(base_query.subquery())
                    .group_by(WorkflowExecution.model_name, WorkflowExecution.model_type)
                    .order_by(desc('count'))
                )
                
                model_usage = {}
                for row in model_stats_result.fetchall():
                    model_name, model_type, count, successful, avg_time = row
                    model_usage[model_name] = {
                        'count': count,
                        'success_rate': (successful / count * 100) if count > 0 else 0,
                        'average_time': avg_time or 0,
                        'model_type': model_type
                    }
                
                return {
                    'total_executions': total_executions,
                    'success_rate': round(success_rate, 2),
                    'average_execution_time': round(avg_execution_time, 2),
                    'common_failure_reasons': failure_reasons,
                    'correction_stats': {
                        'total_corrections': total_corrections,
                        'correction_success_rate': 0  # Would need more complex query
                    },
                    'model_usage_stats': model_usage
                }
                
            except Exception as e:
                logger.error(f"Failed to get analytics: {e}")
                raise
    
    async def get_troubleshooting_data(
        self,
        status: Optional[str] = None,
        model_type: Optional[str] = None,
        error_type: Optional[str] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """Get troubleshooting data for specific issues"""
        
        async with self.session_factory() as session:
            try:
                # Build base query
                query = select(WorkflowExecution).options(
                    selectinload(WorkflowExecution.corrections),
                    selectinload(WorkflowExecution.warnings),
                    selectinload(WorkflowExecution.errors)
                )
                
                conditions = []
                if status:
                    conditions.append(WorkflowExecution.execution_status == status)
                if model_type:
                    conditions.append(WorkflowExecution.model_type == model_type)
                if error_type and status == 'failed':
                    conditions.append(WorkflowExecution.error_message.like(f'%{error_type}%'))
                
                if conditions:
                    query = query.where(and_(*conditions))
                
                query = query.order_by(desc(WorkflowExecution.timestamp)).limit(limit)
                
                result = await session.execute(query)
                executions = result.scalars().all()
                
                # Convert to dicts with relationships
                records = []
                for execution in executions:
                    data = execution.to_dict()
                    data['corrections'] = [c.to_dict() for c in execution.corrections]
                    data['warnings'] = [w.to_dict() for w in execution.warnings]
                    data['errors'] = [e.to_dict() for e in execution.errors]
                    records.append(data)
                
                # Analyze common patterns
                error_patterns = {}
                for record in records:
                    if not record['success'] and record['error_message']:
                        pattern = self._extract_error_pattern(record['error_message'])
                        if pattern not in error_patterns:
                            error_patterns[pattern] = {'count': 0, 'examples': []}
                        error_patterns[pattern]['count'] += 1
                        if len(error_patterns[pattern]['examples']) < 3:
                            error_patterns[pattern]['examples'].append(record['id'])
                
                common_patterns = [
                    {'pattern': pattern, 'count': data['count'], 'examples': data['examples']}
                    for pattern, data in sorted(error_patterns.items(), key=lambda x: x[1]['count'], reverse=True)
                ][:10]
                
                # Generate recommendations
                recommendations = self._generate_recommendations(records, common_patterns)
                
                return {
                    'records': records,
                    'common_patterns': common_patterns,
                    'recommendations': recommendations
                }
                
            except Exception as e:
                logger.error(f"Failed to get troubleshooting data: {e}")
                raise
    
    async def cleanup_old_records(self, days_to_keep: int = 30) -> int:
        """Clean up old workflow execution records"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        async with self.session_factory() as session:
            try:
                # Count records to be deleted
                count_result = await session.execute(
                    select(func.count(WorkflowExecution.id))
                    .where(WorkflowExecution.timestamp < cutoff_date)
                )
                count = count_result.scalar()
                
                if count > 0:
                    # Delete old records (cascading will handle related records)
                    from sqlalchemy import delete
                    await session.execute(
                        delete(WorkflowExecution)
                        .where(WorkflowExecution.timestamp < cutoff_date)
                    )
                    await session.commit()
                    
                    logger.info(f"Cleaned up {count} old workflow execution records")
                    return count
                else:
                    logger.info("No old records to clean up")
                    return 0
                    
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to cleanup old records: {e}")
                raise
    
    async def _update_model_stats(self, session: AsyncSession, execution: WorkflowExecution):
        """Update model usage statistics"""
        try:
            # Get or create model stats record
            result = await session.execute(
                select(ModelUsageStats).where(ModelUsageStats.model_name == execution.model_name)
            )
            stats = result.scalar_one_or_none()
            
            if not stats:
                stats = ModelUsageStats(
                    model_name=execution.model_name,
                    model_type=execution.model_type,
                    first_used=execution.timestamp
                )
                session.add(stats)
            
            # Update statistics
            stats.total_uses += 1
            if execution.success:
                stats.successful_uses += 1
            else:
                stats.failed_uses += 1
            
            stats.last_used = execution.timestamp
            
            if execution.execution_duration:
                stats.total_execution_time += execution.execution_duration
                stats.average_execution_time = stats.total_execution_time / stats.total_uses
            
            if execution.memory_usage:
                # Simple moving average for memory usage
                if stats.average_memory_usage:
                    stats.average_memory_usage = (stats.average_memory_usage + execution.memory_usage) / 2
                else:
                    stats.average_memory_usage = execution.memory_usage
            
            if execution.gpu_utilization:
                # Simple moving average for GPU utilization
                if stats.average_gpu_utilization:
                    stats.average_gpu_utilization = (stats.average_gpu_utilization + execution.gpu_utilization) / 2
                else:
                    stats.average_gpu_utilization = execution.gpu_utilization
            
            stats.updated_at = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Failed to update model stats: {e}")
            # Don't raise - this is not critical for main workflow
    
    def _generate_tags(self, request: Dict[str, Any], validation_result: Dict[str, Any]) -> List[str]:
        """Generate tags for the execution"""
        tags = []
        
        # Model type tag
        if request.get('modelType'):
            tags.append(f"model:{request['modelType']}")
        
        # Mode tag
        tags.append(f"mode:{request.get('mode', 'txt2img')}")
        
        # Correction tags
        corrections = validation_result.get('corrections', [])
        if corrections:
            tags.append('auto-corrected')
            for correction in corrections:
                tags.append(f"correction:{correction.get('parameter', 'unknown')}")
        
        # Resolution tag
        if request.get('width') and request.get('height'):
            tags.append(f"resolution:{request['width']}x{request['height']}")
        
        # Quality preference
        if request.get('prioritize_quality'):
            tags.append('quality-focused')
        elif request.get('prioritize_speed'):
            tags.append('speed-focused')
        
        return tags
    
    def _extract_error_pattern(self, error: str) -> str:
        """Extract common error patterns"""
        error_lower = error.lower()
        
        if 'node' in error_lower and 'not found' in error_lower:
            return 'Missing node type'
        elif 'parameter' in error_lower and 'invalid' in error_lower:
            return 'Invalid parameter value'
        elif 'connection' in error_lower or 'input' in error_lower:
            return 'Connection error'
        elif 'memory' in error_lower or 'cuda' in error_lower:
            return 'Memory/GPU error'
        elif 'model' in error_lower and 'load' in error_lower:
            return 'Model loading error'
        else:
            return 'Other error'
    
    def _generate_recommendations(
        self, 
        records: List[Dict[str, Any]], 
        patterns: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate troubleshooting recommendations"""
        recommendations = []
        
        total_records = len(records)
        failed_records = [r for r in records if not r['success']]
        failure_rate = (len(failed_records) / total_records * 100) if total_records > 0 else 0
        
        if failure_rate > 20:
            recommendations.append('High failure rate detected. Consider reviewing model compatibility and parameter settings.')
        
        for pattern in patterns:
            if pattern['pattern'] == 'Missing node type' and pattern['count'] > 5:
                recommendations.append('Update ComfyUI to latest version to ensure all required nodes are available.')
            elif pattern['pattern'] == 'Invalid parameter value' and pattern['count'] > 3:
                recommendations.append('Enable auto-correction feature to automatically fix invalid parameter values.')
            elif pattern['pattern'] == 'Memory/GPU error' and pattern['count'] > 2:
                recommendations.append('Consider reducing batch size or image resolution to prevent memory issues.')
        
        if not recommendations:
            recommendations.append('System is running well with low error rates.')
        
        return recommendations


# Singleton instance
workflow_data_service = SQLiteWorkflowDataService()