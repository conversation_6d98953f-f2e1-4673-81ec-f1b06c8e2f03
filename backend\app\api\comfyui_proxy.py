"""
ComfyUI API Proxy Service
Provides complete API proxy functionality between frontend and ComfyUI backend
Enhanced with centralized configuration and service discovery
"""

from fastapi import APIRouter, HTTPException, Request, Response
from fastapi.responses import StreamingResponse
import httpx
import json
import logging
import asyncio
from typing import Any, Dict
from app.core.config import settings, get_service_url

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/comfyui", tags=["ComfyUI Proxy"])

class ComfyUIProxy:
    """
    Complete proxy for ComfyUI API calls
    Routes all requests to ComfyUI backend with proper error handling
    """
    
    def __init__(self):
        # Use centralized configuration with service discovery
        self.comfyui_base_url = get_service_url('comfyui')
        # Tune timeouts for long-running ComfyUI operations (read/write up to 10 minutes)
        self.timeout = httpx.Timeout(connect=10.0, read=600.0, write=600.0, pool=None)
        # Connection pooling keeps sockets alive to reduce handshake overhead
        self.limits = httpx.Limits(max_keepalive_connections=20, max_connections=100)
        # Reuse a single AsyncClient to enable keep-alive & pooling across requests
        self.client = httpx.AsyncClient(base_url=self.comfyui_base_url, timeout=self.timeout, limits=self.limits)

    async def _request_with_retries(self, method: str, url: str, *, headers: Dict[str, str], params: Dict[str, Any], json_body: Any = None, content_body: bytes | None = None, is_idempotent: bool = True) -> httpx.Response:
        """
        Make an HTTP request with light retry logic for idempotent methods (GET/HEAD/OPTIONS).
        Retries transient network errors and 502/503/504 with exponential backoff + jitter.
        """
        max_attempts = 3 if is_idempotent else 1
        attempt = 0
        backoff = 0.5
        while True:
            attempt += 1
            try:
                if json_body is not None:
                    return await self.client.request(method=method, url=url, headers=headers, params=params, json=json_body)
                elif content_body is not None:
                    return await self.client.request(method=method, url=url, headers=headers, params=params, content=content_body)
                else:
                    return await self.client.request(method=method, url=url, headers=headers, params=params)
            except (httpx.ConnectError, httpx.ReadTimeout, httpx.RemoteProtocolError) as e:
                if attempt >= max_attempts:
                    raise
                jitter = 0.2 * backoff
                logger.warning(f"Transient error on attempt {attempt}/{max_attempts}: {e}. Retrying in ~{backoff:.1f}s")
                await asyncio.sleep(backoff + (jitter))
                backoff = min(backoff * 2, 5.0)
            except httpx.TimeoutException:
                # Honor timeout exceptions as-is (non-retriable here)
                raise
        
    async def proxy_request(
        self, 
        method: str, 
        path: str, 
        request: Request,
        **kwargs
    ) -> Response:
        """
        Proxy a request to ComfyUI backend
        """
        # Build target URL (relative to base_url configured on client)
        target_url = f"{path}"
        
        # Get query parameters
        query_params = dict(request.query_params)
        
        # Get request body if present
        body = None
        if method.upper() in ["POST", "PUT", "PATCH"]:
            try:
                if request.headers.get("content-type", "").startswith("application/json"):
                    body = await request.json()
                else:
                    body = await request.body()
            except Exception as e:
                logger.warning(f"Could not parse request body: {e}")
        
        # Forward headers (excluding host and connection-related headers)
        headers = {}
        for key, value in request.headers.items():
            if key.lower() not in ['host', 'connection', 'content-length', 'transfer-encoding']:
                headers[key] = value
        
        try:
            # Make the proxied request using shared client (keep-alive)
            is_idempotent = method.upper() in ["GET", "HEAD", "OPTIONS"]
            if body is not None:
                if isinstance(body, (dict, list)):
                    response = await self._request_with_retries(
                        method=method,
                        url=target_url,
                        headers=headers,
                        params=query_params,
                        json_body=body,
                        is_idempotent=is_idempotent,
                    )
                else:
                    response = await self._request_with_retries(
                        method=method,
                        url=target_url,
                        headers=headers,
                        params=query_params,
                        content_body=body,
                        is_idempotent=is_idempotent,
                    )
            else:
                response = await self._request_with_retries(
                    method=method,
                    url=target_url,
                    headers=headers,
                    params=query_params,
                    is_idempotent=is_idempotent,
                )

            # Return response with same status code and headers
            response_headers = dict(response.headers)

            # Handle streaming responses (like images)
            if response.headers.get("content-type", "").startswith("image/"):
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=response_headers,
                    media_type=response.headers.get("content-type")
                )

            # Handle JSON responses
            if response.headers.get("content-type", "").startswith("application/json"):
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=response_headers,
                    media_type="application/json"
                )

            # Handle other responses
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=response_headers
            )

        except httpx.ConnectError:
            logger.error(f"Failed to connect to ComfyUI at {target_url}")
            raise HTTPException(
                status_code=503, 
                detail="ComfyUI backend is not available. Please ensure ComfyUI is running."
            )
        except httpx.TimeoutException:
            logger.error(f"Timeout connecting to ComfyUI at {target_url}")
            raise HTTPException(
                status_code=504,
                detail="ComfyUI backend request timed out"
            )
        except Exception as e:
            logger.error(f"Error proxying request to ComfyUI: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error communicating with ComfyUI backend: {str(e)}"
            )

# Initialize proxy
comfyui_proxy = ComfyUIProxy()

# Common ComfyUI API endpoints
@router.get("/queue")
async def get_queue(request: Request):
    """Get current queue status"""
    return await comfyui_proxy.proxy_request("GET", "/queue", request)

@router.post("/prompt")
async def queue_prompt(request: Request):
    """Queue a new prompt for generation"""
    return await comfyui_proxy.proxy_request("POST", "/prompt", request)

@router.post("/queue")
async def manage_queue(request: Request):
    """Manage queue (clear, etc.)"""
    return await comfyui_proxy.proxy_request("POST", "/queue", request)

@router.get("/history")
async def get_history(request: Request):
    """Get generation history"""
    return await comfyui_proxy.proxy_request("GET", "/history", request)

@router.get("/history/{prompt_id}")
async def get_history_item(prompt_id: str, request: Request):
    """Get specific history item"""
    return await comfyui_proxy.proxy_request("GET", f"/history/{prompt_id}", request)

@router.get("/system_stats")
async def get_system_stats(request: Request):
    """Get ComfyUI system statistics"""
    return await comfyui_proxy.proxy_request("GET", "/system_stats", request)

@router.get("/object_info")
async def get_object_info(request: Request):
    """Get available nodes and their information"""
    return await comfyui_proxy.proxy_request("GET", "/object_info", request)

@router.get("/object_info/{class_type}")
async def get_node_info(class_type: str, request: Request):
    """Get information about a specific node type"""
    return await comfyui_proxy.proxy_request("GET", f"/object_info/{class_type}", request)

@router.post("/upload/image")
async def upload_image(request: Request):
    """Upload an image to ComfyUI"""
    return await comfyui_proxy.proxy_request("POST", "/upload/image", request)

@router.get("/view")
async def view_image(request: Request):
    """View an image from ComfyUI"""
    return await comfyui_proxy.proxy_request("GET", "/view", request)

@router.get("/view_metadata/{folder_name}")
async def view_metadata(folder_name: str, request: Request):
    """View image metadata"""
    return await comfyui_proxy.proxy_request("GET", f"/view_metadata/{folder_name}", request)

@router.post("/free")
async def free_memory(request: Request):
    """Free GPU memory"""
    return await comfyui_proxy.proxy_request("POST", "/free", request)

@router.get("/embeddings")
async def get_embeddings(request: Request):
    """Get available embeddings"""
    return await comfyui_proxy.proxy_request("GET", "/embeddings", request)

@router.get("/extensions")
async def get_extensions(request: Request):
    """Get available extensions"""
    return await comfyui_proxy.proxy_request("GET", "/extensions", request)

# Catch-all route for any other ComfyUI API calls
@router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_catch_all(path: str, request: Request):
    """Catch-all proxy for any ComfyUI API calls not explicitly defined"""
    method = request.method
    return await comfyui_proxy.proxy_request(method, f"/{path}", request)
