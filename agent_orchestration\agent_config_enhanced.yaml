# Enhanced Agent Configuration with Health Monitoring & Advanced Features
# Enhanced Version: Adds health monitoring, automatic recovery, and enhanced error handling

# Global Configuration
global:
  version: "2.0"
  enhanced_features: true
  
  # Health Monitoring System
  health_monitoring:
    enabled: true
    check_interval: 300  # seconds
    failure_threshold: 3  # consecutive failures before marking as unhealthy
    recovery_attempts: 5
    
    # Agent Health Metrics
    metrics:
      - "execution_time"
      - "memory_usage"
      - "error_rate"
      - "success_rate"
      - "last_execution"
      
    # Automatic Recovery Actions
    recovery_actions:
      restart_agent: true
      clear_cache: true
      reset_database_connections: true
      notify_administrator: true
      
  # Enhanced Error Handling
  error_handling:
    retry_attempts: 3
    retry_delay: 5  # seconds
    exponential_backoff: true
    max_retry_delay: 60  # seconds
    
    # Error Classification
    error_types:
      network: "retryable"
      database: "retryable"
      filesystem: "retryable"
      configuration: "fatal"
      syntax: "fatal"
      
    # Error Recovery Strategies
    recovery_strategies:
      network:
        - "check_connectivity"
        - "restart_connection"
        - "fallback_endpoint"
      database:
        - "reconnect"
        - "repair_database"
        - "backup_restore"
      filesystem:
        - "check_permissions"
        - "create_missing_directories"
        - "clear_temp_files"
        
  # Performance Optimization
  performance:
    parallel_execution: true
    max_concurrent_agents: 4
    memory_limit: "2GB"
    cpu_limit: "80%"
    
    # Resource Management
    resource_pools:
      database_connections: 10
      file_handles: 100
      network_connections: 20
      
  # Security Enhancements
  security:
    validate_inputs: true
    sanitize_outputs: true
    rate_limiting: true
    max_requests_per_minute: 60
    
    # Access Control
    permissions:
      file_system: "restricted"
      network: "limited"
      database: "read_write"
      
# Database Configuration with Connection Pooling
database_config:
  connection_pooling:
    enabled: true
    pool_size: 10
    max_overflow: 20
    pool_timeout: 30
    
  # Database Health Monitoring
  health_checks:
    enabled: true
    check_interval: 60
    timeout: 10
    
  # Backup Strategy
  backup:
    enabled: true
    interval: "daily"
    retention_days: 30
    compress: true
    
# Agent Definitions (Enhanced)
agents:
  # Enhanced System Agents
  dependency-orchestrator:
    description: "Enhanced environment and dependency management with predictive analysis"
    category: "system"
    color: "yellow"
    entrypoint: "scripts/dependency_orchestrator.py"
    
    # Health Configuration
    health:
      enabled: true
      check_commands:
        - "pip list"
        - "node --version"
        - "python --version"
      timeout: 30
      
    # Resource Limits
    resources:
      memory_limit: "512MB"
      cpu_limit: "50%"
      execution_timeout: 300
      
    # Enhanced Knowledge Bases
    knowledge_bases:
      - "Backend_Environment_Management.md"
      - "Frontend_Environment_Management.md"
      - "Dependency_Resolution_Solutions.md"
      - "package.json"
      - "backend/requirements.txt"
      - "setup_environment.ps1"
      
    databases:
      - "dependency_orchestrator.db"
      - "environment_metrics.db"
      - "predictive_analysis.db"
    
    # Enhanced Tasks
    tasks:
      predictive_maintenance:
        description: "Predict and prevent dependency issues before they occur"
        parameters:
          prediction_window:
            type: integer
            default: 168  # hours (1 week)
            min: 24
            max: 720
            description: "Hours ahead to predict issues"
          confidence_threshold:
            type: float
            default: 0.7
            min: 0.1
            max: 1.0
            description: "Minimum confidence for predictions"
          auto_apply_fixes:
            type: boolean
            default: false
            description: "Automatically apply preventive fixes"
            
      dependency_vulnerability_scan:
        description: "Scan all dependencies for security vulnerabilities"
        parameters:
          severity_threshold:
            type: string
            default: "medium"
            options: ["low", "medium", "high", "critical"]
            description: "Minimum severity to report"
          auto_update_safe:
            type: boolean
            default: false
            description: "Automatically update safe dependencies"
          generate_report:
            type: boolean
            default: true
            description: "Generate vulnerability report"
            
      environment_optimization:
        description: "Optimize environment performance and resource usage"
        parameters:
          optimization_targets:
            type: array
            default: ["startup_time", "memory_usage", "build_time"]
            options: ["startup_time", "memory_usage", "build_time", "disk_space"]
            description: "Areas to optimize"
          aggressive_optimization:
            type: boolean
            default: false
            description: "Apply aggressive optimization (may break compatibility)"

  system-connections-manager:
    description: "Enhanced system integration with AI-powered diagnostics"
    category: "system"
    color: "red"
    entrypoint: "scripts/system_connections_manager.py"
    
    # Enhanced Health Configuration
    health:
      enabled: true
      check_commands:
        - "curl -f http://localhost:3003/health"
        - "curl -f http://localhost:8000/health"
        - "curl -f http://localhost:8188/health"
      timeout: 10
      
    knowledge_bases:
      - "Frontend_Backend_Solutions.md"
      - "System_Integration_Solutions.md"
      - "Connection_Diagnostics.md"
      - "websocket_patterns.md"
      
    databases:
      - "system_connections.db"
      - "connection_metrics.db"
      - "ai_diagnostics.db"
    
    tasks:
      ai_powered_diagnostics:
        description: "Use AI to diagnose complex system integration issues"
        parameters:
          analysis_depth:
            type: string
            default: "comprehensive"
            options: ["basic", "standard", "comprehensive", "deep_learning"]
            description: "Depth of AI analysis"
          include_historical_data:
            type: boolean
            default: true
            description: "Include historical patterns in analysis"
          generate_solutions:
            type: boolean
            default: true
            description: "Generate potential solutions"
            
      real_time_monitoring:
        description: "Continuous real-time monitoring of all system connections"
        parameters:
          monitoring_duration:
            type: integer
            default: 3600  # seconds (1 hour)
            min: 300
            max: 86400
            description: "Duration to monitor"
          alert_thresholds:
            type: object
            default:
              response_time: 2000  # ms
              error_rate: 0.05     # 5%
              connection_drops: 3   # per hour
            description: "Thresholds for alerts"
          auto_remediation:
            type: boolean
            default: false
            description: "Automatically attempt to fix issues"

# Enhanced Task Templates with Error Handling
task_templates:
  comprehensive_system_health:
    description: "Enhanced comprehensive system health check with AI analysis"
    error_handling:
      continue_on_failure: true
      collect_failure_data: true
      generate_failure_report: true
      
    agents_sequence:
      - agent: "dependency-orchestrator"
        task: "predictive_maintenance"
        params:
          prediction_window: 72
          auto_apply_fixes: false
        timeout: 300
        
      - agent: "dependency-orchestrator"
        task: "dependency_vulnerability_scan"
        params:
          severity_threshold: "medium"
          generate_report: true
        timeout: 600
        
      - agent: "system-connections-manager"
        task: "ai_powered_diagnostics"
        params:
          analysis_depth: "comprehensive"
          generate_solutions: true
        timeout: 300
        
      - agent: "system-connections-manager"
        task: "real_time_monitoring"
        params:
          monitoring_duration: 1800  # 30 minutes
          auto_remediation: false
        timeout: 2000
        
  emergency_recovery:
    description: "Emergency system recovery procedures"
    priority: "critical"
    error_handling:
      stop_on_failure: false
      escalate_failures: true
      
    agents_sequence:
      - agent: "dependency-orchestrator"
        task: "environment_audit"
        params:
          create_backup: true
          emergency_mode: true
        timeout: 180
        
      - agent: "system-connections-manager"
        task: "diagnose_connections"
        params:
          component: "all"
          auto_heal: true
        timeout: 300

# Enhanced Reporting with Analytics
reporting:
  formats: ["markdown", "json", "html", "pdf"]
  default_format: "markdown"
  
  # Enhanced Metrics
  metrics:
    include_performance: true
    include_resource_usage: true
    include_predictive_insights: true
    include_recommendations: true
    include_trend_analysis: true
    
  # Analytics Dashboard
  dashboard:
    enabled: true
    update_interval: 300  # seconds
    retention_days: 90
    
    # Dashboard Widgets
    widgets:
      - "system_health_overview"
      - "agent_performance_metrics"
      - "error_rate_trends"
      - "resource_utilization"
      - "predictive_alerts"
      
  # Alert System
  alerts:
    enabled: true
    channels: ["email", "slack", "webhook"]
    
    # Alert Rules
    rules:
      high_error_rate:
        condition: "error_rate > 0.1"
        severity: "warning"
        cooldown: 300
        
      system_degradation:
        condition: "response_time > 5000"
        severity: "critical"
        cooldown: 60
        
      prediction_alert:
        condition: "prediction_confidence > 0.8 AND prediction_severity >= 'high'"
        severity: "warning"
        cooldown: 3600

# Automation Rules
automation:
  enabled: true
  
  # Scheduled Tasks
  scheduled_tasks:
    daily_health_check:
      schedule: "0 9 * * *"  # 9 AM daily
      template: "comprehensive_system_health"
      
    weekly_optimization:
      schedule: "0 2 * * 0"  # 2 AM Sunday
      agents_sequence:
        - agent: "dependency-orchestrator"
          task: "environment_optimization"
          params:
            optimization_targets: ["memory_usage", "startup_time"]
            
    security_scan:
      schedule: "0 3 * * 1"  # 3 AM Monday
      agents_sequence:
        - agent: "dependency-orchestrator"
          task: "dependency_vulnerability_scan"
          params:
            severity_threshold: "low"
            generate_report: true
            
  # Trigger-based Automation
  triggers:
    error_rate_spike:
      condition: "error_rate > 0.2"
      action: "emergency_recovery"
      
    dependency_vulnerability:
      condition: "new_vulnerability_detected"
      action: "dependency_vulnerability_scan"
      
    performance_degradation:
      condition: "response_time > 10000"
      action: "comprehensive_system_health"

# Integration with External Tools
integrations:
  monitoring:
    prometheus:
      enabled: false
      endpoint: "http://localhost:9090"
      
    grafana:
      enabled: false
      endpoint: "http://localhost:3000"
      
  notification:
    slack:
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
      
    email:
      enabled: false
      smtp_server: "${SMTP_SERVER}"
      
  version_control:
    git:
      enabled: true
      auto_commit_reports: false
      branch: "agent-reports"
