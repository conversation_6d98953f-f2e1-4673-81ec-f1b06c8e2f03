"""
Advanced Connection Monitor with Predictive Health Analysis and Auto-Recovery
Implements intelligent connection monitoring, failure prediction, and automatic failover
"""
import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import httpx

from app.core.config import settings
from app.services.comfyui_service import ComfyUIService
from app.services.ollama_service import OllamaService

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded" 
    CRITICAL = "critical"
    OFFLINE = "offline"
    RECOVERING = "recovering"

@dataclass
class ConnectionMetrics:
    service_name: str
    response_time: float
    success_rate: float
    error_count: int
    last_check: datetime
    consecutive_failures: int
    status: HealthStatus
    prediction_score: float = 0.0  # 0-1 scale, 1 = high failure probability

@dataclass
class SystemHealth:
    overall_status: HealthStatus
    services: Dict[str, ConnectionMetrics]
    alerts: List[str]
    recommendations: List[str]
    timestamp: datetime

class ConnectionMonitor:
    """Advanced connection monitoring with predictive capabilities"""
    
    def __init__(self):
        self.comfyui_service = ComfyUIService()
        self.ollama_service = OllamaService()
        self.metrics_history: Dict[str, List[ConnectionMetrics]] = {
            "comfyui": [],
            "ollama": [],
            "backend": []
        }
        self.monitoring = False
        self.monitor_task = None
        self.check_interval = 30  # seconds
        self.max_history = 100  # Keep last 100 metrics per service
        
        # Failure prediction thresholds
        self.prediction_window = 300  # 5 minutes
        self.failure_threshold = 0.7  # 70% failure probability triggers alert
        self.recovery_threshold = 3   # 3 consecutive successes = recovered
        
    async def start_monitoring(self):
        """Start continuous monitoring"""
        if self.monitoring:
            return
            
        logger.info("Starting connection monitoring system...")
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        
    async def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Connection monitoring stopped")
        
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                await self._check_all_services()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.check_interval)
                
    async def _check_all_services(self):
        """Check all services and update metrics"""
        tasks = [
            self._check_comfyui(),
            self._check_ollama(),
            self._check_backend_self()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and update history
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Service check failed: {result}")
                
        # Analyze trends and predict failures
        await self._analyze_trends()
        
    async def _check_comfyui(self) -> ConnectionMetrics:
        """Check ComfyUI service health"""
        start_time = time.time()
        service_name = "comfyui"
        
        try:
            # Test connection and get system stats
            connected = await self.comfyui_service.check_connection()
            response_time = time.time() - start_time
            
            if connected:
                # Get additional metrics
                stats = await self.comfyui_service.get_system_stats()
                queue = await self.comfyui_service.get_queue_status()
                
                status = HealthStatus.HEALTHY
                if queue.get("queue_pending", []) and len(queue["queue_pending"]) > 5:
                    status = HealthStatus.DEGRADED
                    
            else:
                status = HealthStatus.OFFLINE
                
            metrics = self._create_metrics(service_name, response_time, connected, status)
            
        except Exception as e:
            logger.error(f"ComfyUI check failed: {e}")
            response_time = time.time() - start_time
            metrics = self._create_metrics(service_name, response_time, False, HealthStatus.CRITICAL)
            
        self._update_history(service_name, metrics)
        return metrics
        
    async def _check_ollama(self) -> ConnectionMetrics:
        """Check Ollama service health"""
        start_time = time.time()
        service_name = "ollama"
        
        try:
            # Test connection and list models
            connected = await self.ollama_service.check_connection()
            response_time = time.time() - start_time
            
            if connected:
                # Test with a quick model list
                models = await self.ollama_service.list_models()
                status = HealthStatus.HEALTHY if models else HealthStatus.DEGRADED
            else:
                status = HealthStatus.OFFLINE
                
            metrics = self._create_metrics(service_name, response_time, connected, status)
            
        except Exception as e:
            logger.error(f"Ollama check failed: {e}")
            response_time = time.time() - start_time
            metrics = self._create_metrics(service_name, response_time, False, HealthStatus.CRITICAL)
            
        self._update_history(service_name, metrics)
        return metrics
        
    async def _check_backend_self(self) -> ConnectionMetrics:
        """Check backend self-health"""
        start_time = time.time()
        service_name = "backend"
        
        try:
            # Simple self-check - measure response time and memory
            import psutil
            process = psutil.Process()
            memory_percent = process.memory_percent()
            cpu_percent = process.cpu_percent()
            
            response_time = time.time() - start_time
            
            # Determine status based on resource usage
            if memory_percent > 80 or cpu_percent > 80:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.HEALTHY
                
            metrics = self._create_metrics(service_name, response_time, True, status)
            
        except Exception as e:
            logger.error(f"Backend self-check failed: {e}")
            response_time = time.time() - start_time
            metrics = self._create_metrics(service_name, response_time, False, HealthStatus.CRITICAL)
            
        self._update_history(service_name, metrics)
        return metrics
        
    def _create_metrics(self, service_name: str, response_time: float, 
                       success: bool, status: HealthStatus) -> ConnectionMetrics:
        """Create connection metrics object"""
        
        # Get previous metrics for trend analysis
        history = self.metrics_history.get(service_name, [])
        
        # Calculate success rate over last 10 checks
        recent_history = history[-10:] if history else []
        if recent_history:
            success_count = sum(1 for m in recent_history if m.status in [HealthStatus.HEALTHY, HealthStatus.DEGRADED])
            success_rate = success_count / len(recent_history)
        else:
            success_rate = 1.0 if success else 0.0
            
        # Count consecutive failures
        consecutive_failures = 0
        for metrics in reversed(history):
            if metrics.status in [HealthStatus.CRITICAL, HealthStatus.OFFLINE]:
                consecutive_failures += 1
            else:
                break
                
        if not success:
            consecutive_failures += 1
            
        return ConnectionMetrics(
            service_name=service_name,
            response_time=response_time,
            success_rate=success_rate,
            error_count=consecutive_failures,
            last_check=datetime.now(),
            consecutive_failures=consecutive_failures,
            status=status
        )
        
    def _update_history(self, service_name: str, metrics: ConnectionMetrics):
        """Update metrics history"""
        if service_name not in self.metrics_history:
            self.metrics_history[service_name] = []
            
        history = self.metrics_history[service_name]
        history.append(metrics)
        
        # Keep only recent history
        if len(history) > self.max_history:
            self.metrics_history[service_name] = history[-self.max_history:]
            
    async def _analyze_trends(self):
        """Analyze trends and predict potential failures"""
        for service_name, history in self.metrics_history.items():
            if len(history) < 5:  # Need minimum data for analysis
                continue
                
            # Calculate failure prediction score
            recent_metrics = history[-10:]  # Last 10 checks
            
            # Factors that increase failure probability:
            # 1. Increasing response times
            # 2. Decreasing success rates
            # 3. Recent failures
            # 4. Resource exhaustion patterns
            
            prediction_score = self._calculate_failure_prediction(recent_metrics)
            
            # Update latest metric with prediction
            if history:
                history[-1].prediction_score = prediction_score
                
            # Generate alerts if prediction is high
            if prediction_score > self.failure_threshold:
                await self._handle_predicted_failure(service_name, prediction_score)
                
    def _calculate_failure_prediction(self, metrics: List[ConnectionMetrics]) -> float:
        """Calculate failure prediction score (0-1)"""
        if not metrics:
            return 0.0
            
        score = 0.0
        
        # Response time trend (increasing = bad)
        response_times = [m.response_time for m in metrics]
        if len(response_times) >= 3:
            recent_avg = sum(response_times[-3:]) / 3
            older_avg = sum(response_times[:-3]) / max(1, len(response_times) - 3)
            if recent_avg > older_avg * 1.5:  # 50% increase
                score += 0.3
                
        # Success rate trend
        latest_success_rate = metrics[-1].success_rate
        if latest_success_rate < 0.8:  # Less than 80% success
            score += 0.4
            
        # Consecutive failures
        consecutive_failures = metrics[-1].consecutive_failures
        if consecutive_failures >= 2:
            score += 0.5
            
        # Recent critical status
        critical_count = sum(1 for m in metrics[-5:] if m.status in [HealthStatus.CRITICAL, HealthStatus.OFFLINE])
        if critical_count >= 2:
            score += 0.3
            
        return min(1.0, score)  # Cap at 1.0
        
    async def _handle_predicted_failure(self, service_name: str, prediction_score: float):
        """Handle predicted service failure"""
        logger.warning(f"HIGH FAILURE PROBABILITY for {service_name}: {prediction_score:.2f}")
        
        # Log predictive alert
        alert_msg = f"Service {service_name} has {prediction_score:.1%} failure probability"
        logger.warning(alert_msg)
        
        # Attempt preemptive recovery actions
        if service_name == "ollama":
            await self._recover_ollama()
        elif service_name == "comfyui":
            await self._recover_comfyui()
            
    async def _recover_ollama(self):
        """Attempt to recover Ollama service"""
        logger.info("Attempting Ollama service recovery...")
        
        try:
            # Try to restart Ollama connection
            connected = await self.ollama_service.check_connection()
            if not connected:
                # Could attempt to restart Ollama here if we have system permissions
                logger.warning("Ollama service appears offline - manual restart may be required")
        except Exception as e:
            logger.error(f"Ollama recovery attempt failed: {e}")
            
    async def _recover_comfyui(self):
        """Attempt to recover ComfyUI service"""
        logger.info("Attempting ComfyUI service recovery...")
        
        try:
            # Try to free memory and clear queue
            await self.comfyui_service.free_memory()
            await self.comfyui_service.clear_queue()
            logger.info("ComfyUI recovery actions completed")
        except Exception as e:
            logger.error(f"ComfyUI recovery attempt failed: {e}")
            
    async def get_system_health(self) -> SystemHealth:
        """Get comprehensive system health report"""
        
        # Get latest metrics for each service
        current_metrics = {}
        for service_name, history in self.metrics_history.items():
            if history:
                current_metrics[service_name] = history[-1]
            else:
                # No data available
                current_metrics[service_name] = ConnectionMetrics(
                    service_name=service_name,
                    response_time=0.0,
                    success_rate=0.0,
                    error_count=0,
                    last_check=datetime.now(),
                    consecutive_failures=0,
                    status=HealthStatus.OFFLINE
                )
                
        # Determine overall system status
        statuses = [m.status for m in current_metrics.values()]
        if HealthStatus.CRITICAL in statuses or HealthStatus.OFFLINE in statuses:
            overall_status = HealthStatus.CRITICAL
        elif HealthStatus.DEGRADED in statuses:
            overall_status = HealthStatus.DEGRADED
        else:
            overall_status = HealthStatus.HEALTHY
            
        # Generate alerts and recommendations
        alerts = []
        recommendations = []
        
        for service_name, metrics in current_metrics.items():
            if metrics.prediction_score > 0.5:
                alerts.append(f"{service_name} has high failure probability ({metrics.prediction_score:.1%})")
                recommendations.append(f"Consider restarting {service_name} service")
                
            if metrics.consecutive_failures >= 3:
                alerts.append(f"{service_name} has {metrics.consecutive_failures} consecutive failures")
                recommendations.append(f"Check {service_name} service logs and configuration")
                
            if metrics.response_time > 5.0:
                alerts.append(f"{service_name} response time is high ({metrics.response_time:.1f}s)")
                recommendations.append(f"Check {service_name} service performance")
                
        return SystemHealth(
            overall_status=overall_status,
            services=current_metrics,
            alerts=alerts,
            recommendations=recommendations,
            timestamp=datetime.now()
        )
        
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics for reporting"""
        summary = {}
        
        for service_name, history in self.metrics_history.items():
            if not history:
                continue
                
            recent_metrics = history[-10:]  # Last 10 checks
            
            summary[service_name] = {
                "current_status": history[-1].status.value,
                "response_time_avg": sum(m.response_time for m in recent_metrics) / len(recent_metrics),
                "success_rate": history[-1].success_rate,
                "consecutive_failures": history[-1].consecutive_failures,
                "prediction_score": history[-1].prediction_score,
                "last_check": history[-1].last_check.isoformat(),
                "total_checks": len(history)
            }
            
        return summary