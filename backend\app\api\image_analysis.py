from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from pathlib import Path
import tempfile
import os
from typing import Optional, Dict, Any

from ..services.image_analysis_service import OllamaImageAnalyzer, analyze_image
from ..services.enhanced_image_analysis import (
    EnhancedImageAnalyzer, 
    analyze_composition_focus,
    analyze_color_focus, 
    analyze_lighting_focus,
    analyze_tone_focus,
    master_image_analysis
)

router = APIRouter()

class ImageAnalysisRequest(BaseModel):
    image_path: str
    analysis_type: Optional[str] = "comprehensive"  # style, composition, technical, comprehensive, advanced_composition, comprehensive_color, professional_lighting, tone_atmosphere, artistic_style_deep, master

class ImageAnalysisResponse(BaseModel):
    success: bool
    analysis: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@router.post("/analyze-path", response_model=ImageAnalysisResponse)
async def analyze_image_from_path(request: ImageAnalysisRequest):
    """Analyze an image from a file path"""
    try:
        if not Path(request.image_path).exists():
            raise HTTPException(status_code=404, detail="Image file not found")
        
        # Enhanced analysis types
        if request.analysis_type == "advanced_composition":
            result = analyze_composition_focus(request.image_path)
        elif request.analysis_type == "comprehensive_color":
            result = analyze_color_focus(request.image_path)
        elif request.analysis_type == "professional_lighting":
            result = analyze_lighting_focus(request.image_path)
        elif request.analysis_type == "tone_atmosphere":
            result = analyze_tone_focus(request.image_path)
        elif request.analysis_type == "master":
            result = master_image_analysis(request.image_path)
        else:
            # Original analysis types
            analyzer = OllamaImageAnalyzer()
            
            if request.analysis_type == "style":
                result = analyzer.analyze_art_style(request.image_path)
            elif request.analysis_type == "composition":
                result = analyzer.analyze_composition(request.image_path)
            elif request.analysis_type == "technical":
                result = analyzer.analyze_technical_quality(request.image_path)
            else:  # comprehensive
                result = analyzer.comprehensive_analysis(request.image_path)
        
        return ImageAnalysisResponse(success=True, analysis=result)
        
    except Exception as e:
        return ImageAnalysisResponse(success=False, error=str(e))

@router.post("/analyze-upload")
async def analyze_uploaded_image(file: UploadFile = File(...)):
    """Analyze an uploaded image file"""
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file.filename.split('.')[-1]}") as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            temp_path = tmp_file.name
        
        try:
            # Analyze the temporary file
            result = analyze_image(temp_path)
            return JSONResponse(content={"success": True, "analysis": result})
        finally:
            # Clean up temporary file
            os.unlink(temp_path)
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@router.get("/history/{limit}")
async def get_analysis_history(limit: int = 10):
    """Get recent image analysis history"""
    try:
        analyzer = OllamaImageAnalyzer()
        history = analyzer.get_analysis_history(limit)
        return JSONResponse(content={"success": True, "history": history})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@router.get("/styles")
async def get_art_styles():
    """Get list of known art styles from database"""
    try:
        analyzer = OllamaImageAnalyzer()
        # This would query the art_styles table once populated
        return JSONResponse(content={"success": True, "styles": []})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@router.post("/test-ollama")
async def test_ollama_connection():
    """Test if Ollama is running and LLaVA is available"""
    try:
        analyzer = OllamaImageAnalyzer()
        # Try a simple test (you'd need a test image)
        return JSONResponse(content={
            "success": True,
            "model": analyzer.model,
            "ollama_url": analyzer.ollama_url,
            "status": "Ollama connection ready"
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )