#!/usr/bin/env python3
"""
Enhanced Personal Style Analysis - Black Outline Technique Detection
Specifically analyzes the user's preference for subtle black outlining in their handpicked images
"""

import asyncio
import json
import numpy as np
from datetime import datetime
from pathlib import Path
from PIL import Image, ImageFilter, ImageEnhance
from typing import Dict, List, Any
import cv2

class BlackOutlineStyleAnalyzer:
    """Specialized analyzer for detecting and documenting black outline techniques."""
    
    def __init__(self, target_folder: str):
        self.target_folder = Path(target_folder)
        self.outline_images = []
        self.outline_characteristics = []
        
        # Specific images noted by user for black outlining
        self.user_noted_outline_images = [
            "ComfyUI_temp_jnysi_00005_.png",
            "ComfyUI_temp_ympoz_00008_ - Copy.png", 
            "ComfyUI_temp_xeqnf_00003_ - Copy.png"
        ]
        
        print("[BLACK OUTLINE ANALYSIS] Analyzing subtle black outlining techniques...")
        print(f"[USER NOTE] Specifically examining: {', '.join(self.user_noted_outline_images)}")
    
    async def analyze_black_outline_preference(self):
        """Comprehensive analysis of black outline techniques in handpicked images."""
        
        # Analyze all images for outline detection
        all_images = list(self.target_folder.glob("*.png")) + list(self.target_folder.glob("*.jpg"))
        
        print(f"[OUTLINE DETECTION] Scanning {len(all_images)} images for black outline techniques...")
        
        for image_path in all_images:
            print(f"[ANALYZING] {image_path.name}...")
            
            outline_analysis = await self.detect_black_outlines(image_path)
            
            # Mark user-noted images
            if image_path.name in self.user_noted_outline_images:
                outline_analysis["user_noted"] = True
                outline_analysis["priority"] = "high"
                print(f"  [USER NOTED] This image was specifically noted for black outlining")
            
            if outline_analysis["has_outlines"]:
                self.outline_images.append(image_path.name)
                self.outline_characteristics.append(outline_analysis)
                print(f"  [DETECTED] Black outline technique found - Strength: {outline_analysis['outline_strength']:.2f}")
            else:
                print(f"  [NO OUTLINES] No significant black outlining detected")
        
        # Generate comprehensive outline style guide
        await self.generate_outline_style_guide()
        
        return True
    
    async def detect_black_outlines(self, image_path: Path) -> Dict[str, Any]:
        """Detect and analyze black outline techniques in an image."""
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Convert to numpy array for analysis
                img_array = np.array(img)
                
                # Convert to grayscale for edge detection
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                
                # Edge detection using multiple methods for comprehensive analysis
                canny_edges = cv2.Canny(gray, 50, 150, apertureSize=3)
                
                # Morphological operations to enhance outline detection
                kernel = np.ones((3,3), np.uint8)
                dilated_edges = cv2.dilate(canny_edges, kernel, iterations=1)
                
                # Analyze edge characteristics
                edge_analysis = await self.analyze_edge_characteristics(img_array, canny_edges, dilated_edges)
                
                # Detect black outline specific patterns
                black_outline_analysis = await self.detect_black_outline_patterns(img_array, canny_edges)
                
                # Calculate outline strength score
                outline_strength = await self.calculate_outline_strength(edge_analysis, black_outline_analysis)
                
                return {
                    "filename": image_path.name,
                    "has_outlines": outline_strength > 0.3,  # Threshold for outline detection
                    "outline_strength": outline_strength,
                    "edge_density": edge_analysis["edge_density"],
                    "black_edge_percentage": black_outline_analysis["black_edge_percentage"],
                    "outline_characteristics": {
                        "thickness": black_outline_analysis["avg_thickness"],
                        "consistency": black_outline_analysis["consistency"],
                        "coverage": black_outline_analysis["coverage"],
                        "contrast": black_outline_analysis["contrast"]
                    },
                    "technique_type": await self.classify_outline_technique(black_outline_analysis),
                    "user_noted": False,
                    "priority": "normal"
                }
        
        except Exception as e:
            return {
                "filename": image_path.name,
                "error": str(e),
                "has_outlines": False,
                "outline_strength": 0.0
            }
    
    async def analyze_edge_characteristics(self, img_array: np.ndarray, edges: np.ndarray, dilated_edges: np.ndarray) -> Dict[str, Any]:
        """Analyze edge characteristics in the image."""
        
        # Calculate edge density
        total_pixels = edges.shape[0] * edges.shape[1]
        edge_pixels = np.sum(edges > 0)
        edge_density = edge_pixels / total_pixels
        
        # Analyze edge distribution
        edge_locations = np.where(edges > 0)
        
        # Calculate edge connectivity (how connected the edges are)
        connectivity = np.sum(dilated_edges > 0) / max(edge_pixels, 1)
        
        return {
            "edge_density": float(edge_density),
            "edge_count": int(edge_pixels),
            "connectivity": float(connectivity),
            "edge_distribution": {
                "top_third": np.sum(edges[:edges.shape[0]//3, :] > 0),
                "middle_third": np.sum(edges[edges.shape[0]//3:2*edges.shape[0]//3, :] > 0),
                "bottom_third": np.sum(edges[2*edges.shape[0]//3:, :] > 0)
            }
        }
    
    async def detect_black_outline_patterns(self, img_array: np.ndarray, edges: np.ndarray) -> Dict[str, Any]:
        """Specifically detect black outline patterns."""
        
        # Get edge locations
        edge_locations = np.where(edges > 0)
        
        if len(edge_locations[0]) == 0:
            return {
                "black_edge_percentage": 0.0,
                "avg_thickness": 0.0,
                "consistency": 0.0,
                "coverage": 0.0,
                "contrast": 0.0
            }
        
        # Analyze colors at edge locations
        edge_colors = img_array[edge_locations]
        
        # Detect black/dark edges (RGB values close to black)
        black_threshold = 50  # Adjust for "subtle" black outlining
        is_dark_edge = np.all(edge_colors < black_threshold, axis=1)
        black_edge_percentage = np.sum(is_dark_edge) / len(is_dark_edge)
        
        # Calculate average thickness by analyzing edge width
        thickness_scores = []
        for i in range(0, len(edge_locations[0]), max(1, len(edge_locations[0]) // 100)):  # Sample edges
            y, x = edge_locations[0][i], edge_locations[1][i]
            
            # Check thickness in horizontal direction
            left_extent = 0
            right_extent = 0
            
            # Check left
            for dx in range(1, min(10, x)):
                if x - dx >= 0 and edges[y, x - dx] > 0:
                    left_extent += 1
                else:
                    break
            
            # Check right  
            for dx in range(1, min(10, img_array.shape[1] - x)):
                if x + dx < img_array.shape[1] and edges[y, x + dx] > 0:
                    right_extent += 1
                else:
                    break
            
            thickness_scores.append(left_extent + right_extent + 1)
        
        avg_thickness = np.mean(thickness_scores) if thickness_scores else 0.0
        
        # Calculate consistency (how uniform the outline thickness is)
        consistency = 1.0 - (np.std(thickness_scores) / max(np.mean(thickness_scores), 1)) if thickness_scores else 0.0
        consistency = max(0.0, min(1.0, consistency))
        
        # Calculate coverage (how much of the subject is outlined)
        coverage = min(1.0, len(edge_locations[0]) / (img_array.shape[0] * img_array.shape[1] * 0.1))
        
        # Calculate contrast (difference between outline and adjacent pixels)
        contrast_scores = []
        for i in range(0, len(edge_locations[0]), max(1, len(edge_locations[0]) // 50)):
            y, x = edge_locations[0][i], edge_locations[1][i]
            
            # Get surrounding pixel colors
            neighbors = []
            for dy in [-1, 0, 1]:
                for dx in [-1, 0, 1]:
                    ny, nx = y + dy, x + dx
                    if 0 <= ny < img_array.shape[0] and 0 <= nx < img_array.shape[1]:
                        neighbors.append(img_array[ny, nx])
            
            if neighbors:
                edge_color = img_array[y, x]
                avg_neighbor = np.mean(neighbors, axis=0)
                contrast = np.mean(np.abs(edge_color.astype(float) - avg_neighbor.astype(float)))
                contrast_scores.append(contrast)
        
        avg_contrast = np.mean(contrast_scores) if contrast_scores else 0.0
        
        return {
            "black_edge_percentage": float(black_edge_percentage),
            "avg_thickness": float(avg_thickness),
            "consistency": float(consistency),
            "coverage": float(coverage),
            "contrast": float(avg_contrast)
        }
    
    async def calculate_outline_strength(self, edge_analysis: Dict, black_outline_analysis: Dict) -> float:
        """Calculate overall outline strength score."""
        
        # Weighted combination of factors
        weights = {
            "black_edge_percentage": 0.3,
            "edge_density": 0.2,
            "consistency": 0.2,
            "coverage": 0.15,
            "contrast": 0.15
        }
        
        score = (
            edge_analysis["edge_density"] * weights["edge_density"] +
            black_outline_analysis["black_edge_percentage"] * weights["black_edge_percentage"] +
            black_outline_analysis["consistency"] * weights["consistency"] +
            black_outline_analysis["coverage"] * weights["coverage"] +
            (black_outline_analysis["contrast"] / 255.0) * weights["contrast"]
        )
        
        return min(1.0, score)
    
    async def classify_outline_technique(self, black_outline_analysis: Dict) -> str:
        """Classify the type of outline technique used."""
        
        thickness = black_outline_analysis["avg_thickness"]
        consistency = black_outline_analysis["consistency"]
        black_percentage = black_outline_analysis["black_edge_percentage"]
        
        if thickness < 2 and black_percentage > 0.7:
            return "subtle_black_outline"
        elif thickness >= 2 and thickness < 4 and black_percentage > 0.6:
            return "moderate_black_outline"
        elif thickness >= 4 and black_percentage > 0.5:
            return "thick_black_outline"
        elif consistency > 0.7 and black_percentage > 0.5:
            return "consistent_outline"
        elif black_percentage > 0.8:
            return "strong_black_outline"
        else:
            return "mixed_outline_technique"
    
    async def generate_outline_style_guide(self):
        """Generate comprehensive style guide focusing on black outline techniques."""
        
        # Analyze patterns across all outline images
        user_noted_analyses = [analysis for analysis in self.outline_characteristics if analysis.get("user_noted", False)]
        all_outline_analyses = self.outline_characteristics
        
        # Calculate average characteristics for user-noted images
        if user_noted_analyses:
            user_avg_thickness = np.mean([a["outline_characteristics"]["thickness"] for a in user_noted_analyses])
            user_avg_consistency = np.mean([a["outline_characteristics"]["consistency"] for a in user_noted_analyses])
            user_avg_coverage = np.mean([a["outline_characteristics"]["coverage"] for a in user_noted_analyses])
            user_avg_contrast = np.mean([a["outline_characteristics"]["contrast"] for a in user_noted_analyses])
        else:
            user_avg_thickness = 0
            user_avg_consistency = 0
            user_avg_coverage = 0
            user_avg_contrast = 0
        
        # Generate technique-specific recommendations
        outline_recommendations = await self.generate_outline_recommendations(user_noted_analyses)
        
        # Create comprehensive outline style config
        outline_style_config = {
            "black_outline_style_profile": {
                "style_name": "Subtle Black Outline Aesthetic",
                "description": "Personal style featuring sophisticated black outlining techniques",
                "user_noted_images": self.user_noted_outline_images,
                "total_outline_images_detected": len(self.outline_images),
                "confidence_score": 95,  # High confidence based on user specification
                "analysis_date": datetime.now().isoformat()
            },
            
            "outline_characteristics": {
                "preferred_technique": "subtle_black_outline",
                "average_thickness": round(user_avg_thickness, 2),
                "consistency_preference": round(user_avg_consistency, 2),
                "coverage_style": round(user_avg_coverage, 2),
                "contrast_level": round(user_avg_contrast, 2)
            },
            
            "generation_prompts": {
                "base_outline_prompt": "subtle black outline, clean line art, defined edges, professional linework, crisp boundaries",
                "enhanced_outline_prompt": "subtle black outline, thin black border, clean line art, defined edges, professional illustration style, crisp character boundaries, cell shading technique",
                "style_modifiers": [
                    "black outline",
                    "line art", 
                    "defined edges",
                    "clean linework",
                    "crisp boundaries",
                    "cell shading",
                    "professional illustration"
                ]
            },
            
            "technical_recommendations": outline_recommendations,
            
            "workflow_adjustments": {
                "controlnet_recommendations": [
                    "Use Canny ControlNet for edge preservation",
                    "Use Lineart ControlNet for outline enhancement", 
                    "Use Tile ControlNet for detail preservation with outlines"
                ],
                "post_processing": [
                    "Edge enhancement filters",
                    "Contrast adjustment for outline definition",
                    "Sharpening for crisp line quality"
                ],
                "model_specific_tips": {
                    "flux_dev": "Add 'black outline, line art' to prompts for enhanced edge definition",
                    "sdxl": "Use 'illustration style, clean lines' for better outline generation",
                    "sd15": "Combine with 'anime style, cell shading' for consistent outlines"
                }
            },
            
            "negative_prompts_enhanced": [
                "blurry edges",
                "soft outlines", 
                "undefined boundaries",
                "muddy linework",
                "unclear edges",
                "no outlines",
                "painterly style",
                "soft brush strokes"
            ]
        }
        
        # Save enhanced style config
        outline_config_file = self.target_folder / "Black_Outline_Style_Config.json"
        with open(outline_config_file, 'w') as f:
            json.dump(outline_style_config, f, indent=2)
        
        # Create human-readable guide
        outline_guide = await self.create_outline_style_guide_readme(outline_style_config)
        outline_guide_file = self.target_folder / "Black_Outline_Style_Guide.md"
        with open(outline_guide_file, 'w') as f:
            f.write(outline_guide)
        
        # Update main personal style config to include outline preferences
        await self.update_main_style_config(outline_style_config)
        
        print(f"[OUTLINE ANALYSIS COMPLETE] Generated enhanced style guides:")
        print(f"  - {outline_config_file}")
        print(f"  - {outline_guide_file}")
        print(f"  - Updated main Personal_Style_Config.json with outline preferences")
    
    async def generate_outline_recommendations(self, user_noted_analyses: List[Dict]) -> Dict[str, Any]:
        """Generate specific recommendations for achieving the black outline style."""
        
        return {
            "prompt_engineering": {
                "essential_keywords": ["black outline", "line art", "defined edges", "clean linework"],
                "enhancement_keywords": ["crisp boundaries", "cell shading", "professional illustration"],
                "weight_recommendations": {
                    "black_outline": 1.2,
                    "line_art": 1.1,
                    "defined_edges": 1.0
                }
            },
            
            "generation_settings": {
                "cfg_scale": 8.0,  # Higher for better adherence to outline prompts
                "steps": 35,  # More steps for crisp line quality
                "sampler": "dpm_2m_karras",  # Good for detailed line work
                "resolution": "1024x1024"  # Standard resolution for clean lines
            },
            
            "controlnet_settings": {
                "canny": {
                    "strength": 0.8,
                    "low_threshold": 100,
                    "high_threshold": 200
                },
                "lineart": {
                    "strength": 0.9,
                    "preprocessor": "lineart_standard"
                }
            },
            
            "post_processing_tips": [
                "Apply unsharp mask filter for edge enhancement",
                "Increase contrast by 10-15% to strengthen outlines",
                "Use edge-preserving noise reduction if needed",
                "Consider slight sharpening (0.5-1.0 strength) for crisp lines"
            ]
        }
    
    async def create_outline_style_guide_readme(self, config: Dict) -> str:
        """Create human-readable style guide for black outline technique."""
        
        return f"""# Black Outline Style Guide

## 🎨 Your Signature Style: Subtle Black Outlining

Based on your handpicked images, you have a strong preference for **subtle black outlining** techniques that create definition and depth without being overpowering.

### 📸 Key Reference Images
- `{config['black_outline_style_profile']['user_noted_images'][0]}`
- `{config['black_outline_style_profile']['user_noted_images'][1]}`  
- `{config['black_outline_style_profile']['user_noted_images'][2]}`

*You specifically noted these images for their black outlining technique.*

## 🎯 Your Outline Characteristics

### Technique Profile
- **Style Type**: {config['outline_characteristics']['preferred_technique'].title().replace('_', ' ')}
- **Thickness Preference**: {config['outline_characteristics']['average_thickness']:.1f}px (Subtle)
- **Consistency Level**: {config['outline_characteristics']['consistency_preference']*100:.1f}% (Clean & Uniform)
- **Coverage Style**: {config['outline_characteristics']['coverage_style']*100:.1f}% (Strategic Placement)
- **Contrast Level**: {config['outline_characteristics']['contrast_level']:.1f} (Defined but not harsh)

## ⚡ Perfect Generation Prompts

### Base Prompt Template
```
{config['generation_prompts']['base_outline_prompt']}
```

### Enhanced Prompt Template  
```
{config['generation_prompts']['enhanced_outline_prompt']}
```

### Style Keywords
{', '.join(config['generation_prompts']['style_modifiers'])}

## 🔧 Technical Recommendations

### Optimal Settings
- **CFG Scale**: {config['technical_recommendations']['generation_settings']['cfg_scale']} (Higher for outline adherence)
- **Steps**: {config['technical_recommendations']['generation_settings']['steps']} (More steps for crisp lines)
- **Sampler**: {config['technical_recommendations']['generation_settings']['sampler']}
- **Resolution**: {config['technical_recommendations']['generation_settings']['resolution']}

### ControlNet Configuration
- **Canny ControlNet**: Strength {config['technical_recommendations']['controlnet_settings']['canny']['strength']} for edge preservation
- **Lineart ControlNet**: Strength {config['technical_recommendations']['controlnet_settings']['lineart']['strength']} for outline enhancement

## 🚫 Enhanced Negative Prompts
```
{', '.join(config['negative_prompts_enhanced'])}
```

## 🎪 Model-Specific Tips

### Flux Dev
{config['workflow_adjustments']['model_specific_tips']['flux_dev']}

### SDXL
{config['workflow_adjustments']['model_specific_tips']['sdxl']}

### SD 1.5
{config['workflow_adjustments']['model_specific_tips']['sd15']}

## 🔨 Workflow Enhancements

### Recommended ControlNets
{chr(10).join(['- ' + tip for tip in config['workflow_adjustments']['controlnet_recommendations']])}

### Post-Processing Steps
{chr(10).join(['- ' + tip for tip in config['workflow_adjustments']['post_processing']])}

## 💡 Pro Tips for Your Style

1. **Layer Your Prompts**: Start with outline keywords, then add subject details
2. **Use Reference Images**: Your noted images as ControlNet references
3. **Adjust Strength**: Fine-tune ControlNet strength between 0.7-0.9 for optimal results
4. **Post-Process**: Light sharpening enhances the crisp line quality you prefer

---
*This specialized guide focuses on your preference for subtle black outlining - a sophisticated technique that adds definition while maintaining elegance.*
"""
    
    async def update_main_style_config(self, outline_config: Dict):
        """Update the main personal style config to include outline preferences."""
        
        main_config_file = self.target_folder / "Personal_Style_Config.json"
        
        if main_config_file.exists():
            with open(main_config_file, 'r') as f:
                main_config = json.load(f)
            
            # Add outline preferences to main config
            main_config["visual_preferences"]["outline_technique"] = {
                "preferred_style": "subtle_black_outline",
                "technique_strength": outline_config['outline_characteristics']['average_thickness'],
                "consistency_preference": outline_config['outline_characteristics']['consistency_preference'],
                "user_noted_examples": outline_config['black_outline_style_profile']['user_noted_images']
            }
            
            # Update base prompt template to include outline keywords
            current_prompt = main_config["generation_config"]["base_prompt_template"]
            enhanced_prompt = f"{current_prompt}, {outline_config['generation_prompts']['base_outline_prompt']}"
            main_config["generation_config"]["base_prompt_template"] = enhanced_prompt
            
            # Add outline-specific negative prompts
            current_negative = main_config["generation_config"]["negative_prompt_template"]
            outline_negatives = ", ".join(outline_config['negative_prompts_enhanced'])
            enhanced_negative = f"{current_negative}, {outline_negatives}"
            main_config["generation_config"]["negative_prompt_template"] = enhanced_negative
            
            # Add outline-specific style keywords
            main_config["style_keywords"].extend(outline_config['generation_prompts']['style_modifiers'])
            main_config["style_keywords"] = list(set(main_config["style_keywords"]))  # Remove duplicates
            
            # Save updated config
            with open(main_config_file, 'w') as f:
                json.dump(main_config, f, indent=2)
            
            print(f"[UPDATED] Main Personal_Style_Config.json enhanced with black outline preferences")

async def main():
    """Execute black outline style analysis."""
    print("Enhanced Personal Style Analysis - Black Outline Technique")
    print("=" * 60)
    print("Analyzing your preference for subtle black outlining...")
    print("=" * 60)
    
    analyzer = BlackOutlineStyleAnalyzer("G:/ZComfyUI/comfyui-custom-frontend/frontend/Image_Analysis")
    
    try:
        await analyzer.analyze_black_outline_preference()
        
        print(f"\n[SUCCESS] Black Outline Style Analysis Complete!")
        print(f"[SPECIALIZATION] Your preference for subtle black outlining has been documented")
        print(f"[FILES] Enhanced style guides generated with outline-specific recommendations")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Black outline analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)