"""
WebSocket Health Monitor

Monitors WebSocket connection health and prevents common connection issues
like sending to closed connections and race conditions.
"""

import asyncio
import logging
import time
from typing import Dict, Set, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from fastapi import WebSocket
from starlette.websockets import WebSocketState

logger = logging.getLogger(__name__)

class ConnectionHealth(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CLOSED = "closed"

@dataclass
class ConnectionMetrics:
    """Metrics for a WebSocket connection"""
    client_id: str
    connected_at: datetime
    last_ping: Optional[datetime] = None
    last_pong: Optional[datetime] = None
    last_message: Optional[datetime] = None
    message_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    health_status: ConnectionHealth = ConnectionHealth.HEALTHY
    
    @property
    def connection_duration(self) -> timedelta:
        return datetime.now() - self.connected_at
    
    @property
    def last_activity(self) -> Optional[datetime]:
        activities = [self.last_ping, self.last_pong, self.last_message]
        valid_activities = [a for a in activities if a is not None]
        return max(valid_activities) if valid_activities else None

class WebSocketHealthMonitor:
    """
    Monitors WebSocket connection health and prevents common issues
    """
    
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.metrics: Dict[str, ConnectionMetrics] = {}
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        
        # Configuration
        self.health_check_interval = 30  # seconds
        self.ping_timeout = 10  # seconds
        self.max_error_rate = 0.1  # 10% error rate threshold
        self.connection_timeout = 300  # 5 minutes
        
        # Statistics
        self.total_connections = 0
        self.total_disconnections = 0
        self.total_errors = 0
        self.prevented_send_errors = 0
    
    async def start_monitoring(self):
        """Start the health monitoring loop"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("WebSocket health monitoring started")
    
    async def stop_monitoring(self):
        """Stop the health monitoring loop"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("WebSocket health monitoring stopped")
    
    def register_connection(self, client_id: str, websocket: WebSocket):
        """Register a new WebSocket connection for monitoring"""
        self.connections[client_id] = websocket
        self.metrics[client_id] = ConnectionMetrics(
            client_id=client_id,
            connected_at=datetime.now()
        )
        self.total_connections += 1
        logger.debug(f"Registered connection for monitoring: {client_id}")
    
    def unregister_connection(self, client_id: str):
        """Unregister a WebSocket connection from monitoring"""
        if client_id in self.connections:
            del self.connections[client_id]
        if client_id in self.metrics:
            del self.metrics[client_id]
        self.total_disconnections += 1
        logger.debug(f"Unregistered connection from monitoring: {client_id}")
    
    def is_connection_healthy(self, client_id: str) -> bool:
        """Check if a connection is healthy and safe to send messages to"""
        if client_id not in self.connections:
            return False
        
        websocket = self.connections[client_id]
        metrics = self.metrics.get(client_id)
        
        # Check WebSocket state
        try:
            if hasattr(websocket, 'client_state'):
                if websocket.client_state != WebSocketState.CONNECTED:
                    if metrics:
                        metrics.health_status = ConnectionHealth.CLOSED
                    return False
            elif hasattr(websocket, 'application_state'):
                if websocket.application_state != WebSocketState.CONNECTED:
                    if metrics:
                        metrics.health_status = ConnectionHealth.CLOSED
                    return False
        except Exception as e:
            logger.warning(f"Error checking connection state for {client_id}: {e}")
            if metrics:
                metrics.health_status = ConnectionHealth.UNHEALTHY
            return False
        
        # Check metrics if available
        if metrics:
            # Check error rate
            if metrics.message_count > 0:
                error_rate = metrics.error_count / metrics.message_count
                if error_rate > self.max_error_rate:
                    metrics.health_status = ConnectionHealth.DEGRADED
                    return False
            
            # Check for timeout
            if metrics.last_activity:
                time_since_activity = datetime.now() - metrics.last_activity
                if time_since_activity.total_seconds() > self.connection_timeout:
                    metrics.health_status = ConnectionHealth.UNHEALTHY
                    return False
        
        return True
    
    async def safe_send(self, client_id: str, message: str) -> bool:
        """Safely send a message to a WebSocket connection with health checks"""
        if not self.is_connection_healthy(client_id):
            self.prevented_send_errors += 1
            logger.warning(f"Prevented send to unhealthy connection: {client_id}")
            return False
        
        websocket = self.connections[client_id]
        metrics = self.metrics.get(client_id)
        
        try:
            await websocket.send_text(message)
            
            # Update metrics
            if metrics:
                metrics.message_count += 1
                metrics.last_message = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {client_id}: {e}")
            self.total_errors += 1
            
            # Update metrics
            if metrics:
                metrics.error_count += 1
                metrics.last_error = str(e)
                metrics.health_status = ConnectionHealth.UNHEALTHY
            
            # Remove unhealthy connection
            self.unregister_connection(client_id)
            return False
    
    async def ping_connection(self, client_id: str) -> bool:
        """Send a ping to a connection and update metrics"""
        if client_id not in self.connections:
            return False
        
        websocket = self.connections[client_id]
        metrics = self.metrics.get(client_id)
        
        try:
            # Send ping (implementation depends on WebSocket library)
            # For FastAPI/Starlette, we can send a ping message
            ping_message = '{"type": "ping", "timestamp": ' + str(time.time()) + '}'
            await websocket.send_text(ping_message)
            
            if metrics:
                metrics.last_ping = datetime.now()
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to ping connection {client_id}: {e}")
            if metrics:
                metrics.error_count += 1
                metrics.last_error = str(e)
            return False
    
    def get_connection_health(self, client_id: str) -> Optional[ConnectionHealth]:
        """Get the health status of a connection"""
        metrics = self.metrics.get(client_id)
        return metrics.health_status if metrics else None
    
    def get_health_stats(self) -> Dict[str, Any]:
        """Get overall health statistics"""
        healthy_connections = sum(
            1 for metrics in self.metrics.values() 
            if metrics.health_status == ConnectionHealth.HEALTHY
        )
        
        return {
            "total_connections": self.total_connections,
            "active_connections": len(self.connections),
            "healthy_connections": healthy_connections,
            "total_disconnections": self.total_disconnections,
            "total_errors": self.total_errors,
            "prevented_send_errors": self.prevented_send_errors,
            "error_prevention_rate": (
                self.prevented_send_errors / max(1, self.total_errors + self.prevented_send_errors)
            )
        }
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not self.is_monitoring:
                    break
                
                # Check all connections
                unhealthy_connections = []
                
                for client_id in list(self.connections.keys()):
                    if not self.is_connection_healthy(client_id):
                        unhealthy_connections.append(client_id)
                    else:
                        # Send periodic ping to healthy connections
                        await self.ping_connection(client_id)
                
                # Remove unhealthy connections
                for client_id in unhealthy_connections:
                    logger.info(f"Removing unhealthy connection: {client_id}")
                    self.unregister_connection(client_id)
                
                # Log health statistics
                stats = self.get_health_stats()
                logger.debug(f"WebSocket health stats: {stats}")
                
            except Exception as e:
                logger.error(f"Error in WebSocket health monitoring loop: {e}")

# Global instance
websocket_health_monitor = WebSocketHealthMonitor()
